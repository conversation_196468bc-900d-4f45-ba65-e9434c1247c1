{"Sack of Surprises": {"name": "Sack of Surprises", "rarity": "Unique", "cost": 10, "class": "Neutral", "type": "Bag", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Bag", "Cost": "10", "Class": "Neutral", "Effect": "Game started: Replace this with random starting bags and items.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1]]}, "Light Goobert": {"name": "Light Goobert", "rarity": "<PERSON><PERSON>", "cost": 18, "class": "Neutral", "type": "Pet", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Pet", "Cost": "18", "Class": "Neutral", "Effect": "6  item activations: Heal for 20  and inflict 6  for 3s.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1, 1], [1, 1, 1, 1]]}, "Spiked Staff": {"name": "Spiked Staff", "rarity": "Legendary", "cost": 16, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Weapon", "Cost": "16", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "On attack: Use 3  to gain 2 , and during Battle Rage also gain 2 .", "Damage": "8-10 (5/s)", "Stamina": "1 (0.6/s)", "Accuracy": "90%", "Cooldown": "1.8s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1], [1]]}, "Rat": {"name": "Rat", "rarity": "Common", "cost": 4, "class": "<PERSON>", "type": "Pet", "description": "", "raw_stats": {"Rarity": "Common", "Type": "Pet", "Cost": "4", "Class": "<PERSON>", "Effect": "Every 3.3s: Deal 5 -damage. 75% to inflict 1 . 10% to inflict 1 .\nTriggers 15% faster for each  Pet or Food.", "In shop": "Big Bowl of Treats needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 1, 0], [1, 1, 1, 1], [0, 1, 1, 0]]}, "Chili Pepper": {"name": "Chili Pepper", "rarity": "Rare", "cost": 5, "class": "Pyromancer", "type": "Food", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Food", "Cost": "5", "Class": "Pyromancer", "Effect": "Every 5s: <PERSON>ain 1  and heal 5. When you have at least 10 , cleanse 1 debuff.\n\n\nFood: Triggers 10% faster for each  Food of a different type (not Chili Pepper).", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0], [1, 1, 1], [1, 1, 1], [0, 1, 0]]}, "Dagger": {"name": "<PERSON>gger", "rarity": "Rare", "cost": 4, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Weapon", "Cost": "4", "Class": "Neutral", "Effect": "On stun: <PERSON><PERSON><PERSON> extra attack.", "Damage": "2-5 (1/s)", "Stamina": "0 (0/s)", "Accuracy": "95%", "Cooldown": "3.5s", "Sockets": "1", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1]]}, "Ace of Spades": {"name": "Ace of Spades", "rarity": "Rare", "cost": 3, "class": "Reaper", "type": "Playing Card", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Playing Card", "Cost": "3", "Class": "Reaper", "Effect": "On reveal: Your next hit is critical. If the number of cards before is odd, gain 2  and 3 .", "In shop": "Deck of Cards needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1]]}, "Miss Fortune": {"name": "Miss Fortune", "rarity": "Epic", "cost": 7, "class": "Reaper", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Accessory", "Cost": "7", "Class": "Reaper", "Effect": "Every 2.1s: Use 1  to gain 3 buffs of the type of which you have the most.", "In shop": "Mr. <PERSON><PERSON> needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1]]}, "Duffle Bag": {"name": "<PERSON><PERSON> Bag", "rarity": "Unique", "cost": 16, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Bag", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Bag", "Cost": "16", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "Add 6 backpack slots.\nHealth drops below 50%: Enter Battle Rage for 5s (once).\nDuring Battle Rage: Items inside trigger 30% faster. You take 20% reduced damage.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1], [1, 1, 1]]}, "Eggscalibur": {"name": "Eggscalibur", "rarity": "Legendary", "cost": 10, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Weapon", "Cost": "10", "Class": "Neutral", "Effect": "On attack: Use 11 : <PERSON><PERSON> all  Food.\nDeals +1 damage for each  Food.", "Damage": "8-9 (5.3/s)", "Stamina": "1.3 (0.8/s)", "Accuracy": "90%", "Cooldown": "1.6s", "Sockets": "2", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 0, 1, 1, 1], [0, 1, 1, 1, 1], [1, 1, 1, 1, 0], [1, 1, 1, 0, 0]]}, "Hammer": {"name": "Hammer", "rarity": "Rare", "cost": 8, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Weapon", "Cost": "8", "Class": "Neutral", "Effect": "On hit: 45% chance to stun your opponent for 0.5s.", "Damage": "7-11 (4.5/s)", "Stamina": "3 (1.5/s)", "Accuracy": "75%", "Cooldown": "2s", "Sockets": "2", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1], [0, 1, 0], [0, 1, 0]]}, "Rat Chef": {"name": "Rat Chef", "rarity": "Rare", "cost": 8, "class": "<PERSON>", "type": "Pet", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Pet", "Cost": "8", "Class": "<PERSON>", "Effect": "Start of battle: Gain 1  for each  Food.\nEvery 7s: Regenerate 2 stamina and gain 1 .\nTriggers 15% faster for each  Pet or Food.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0], [1, 1, 1], [1, 1, 1], [0, 1, 0]]}, "Belladonna's Whisper": {"name": "Belladonna's Whisper", "rarity": "Legendary", "cost": 14, "class": "<PERSON>", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Weapon", "Cost": "14", "Class": "<PERSON>", "Effect": "For every 5 damage  Weapon deals: Inflict +1  on the next attack.\nDeals +0.5 damage per  of your opponent.", "Damage": "9-12 (3.5/s)", "Stamina": "1.2 (0.4/s)", "Accuracy": "85%", "Cooldown": "3s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0, 0], [1, 1, 1, 1], [0, 1, 0, 0]]}, "Shovel": {"name": "<PERSON><PERSON><PERSON>", "rarity": "Rare", "cost": 8, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Weapon", "Cost": "8", "Class": "Neutral", "Effect": "Shop entered: Dig up a random item.\nOn hit: 40% chance to inflict 1 .", "Damage": "5-8 (2.7/s)", "Stamina": "1.7 (0.7/s)", "Accuracy": "95%", "Cooldown": "2.4s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1], [1]]}, "Staff of Unhealing": {"name": "Staff of Unhealing", "rarity": "<PERSON><PERSON>", "cost": 19, "class": "Reaper", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Weapon", "Cost": "19", "Class": "Reaper", "Effect": "Every 2s: Heal for 20. Use 5 : For 2s, deal 100% of your healing as -damage.", "Stamina": "1.5 (0.8/s)", "Cooldown": "2s", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1], [1]]}, "Snowcake": {"name": "Snowcake", "rarity": "Unique", "cost": 6, "class": "Neutral", "type": "Food", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Food", "Cost": "6", "Class": "Neutral", "Effect": "Every 3s: Inflict 1 .\nIf your opponent has at least 10 , increase -damage by 10% and deal 10 -damage.\n\nFood: Triggers 10% faster for each  Food of a different type (not Snowcake).", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1]]}, "Healing Herbs": {"name": "Healing Herbs", "rarity": "Common", "cost": 4, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Common", "Type": "Accessory", "Cost": "4", "Class": "Neutral", "Effect": "Start of battle: Gain 2 .", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Dragon Nest": {"name": "Dragon Nest", "rarity": "Unique", "cost": 10, "class": "Pyromancer", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Accessory", "Cost": "10", "Class": "Pyromancer", "Effect": "Start of battle: <PERSON><PERSON> 2 , 2 , 4  and 2 .\n Dragon attacks: Heal for 5.\nDragon Eggs hatch after 1 round.\nAdditional dragon eggs are offered in the shop.", "In shop": "Round 8 subclass item", "Adds to shop": "Amethyst Egg, Emerald Egg, Sapphire Egg"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 1, 0], [1, 1, 1, 1], [1, 1, 1, 1], [0, 1, 1, 0]]}, "Fanny Pack": {"name": "<PERSON>", "rarity": "Rare", "cost": 3, "class": "Neutral", "type": "Bag", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Bag", "Cost": "3", "Class": "Neutral", "Effect": "Add 2 backpack slots.\nItems inside trigger 10% faster.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1]]}, "Ruby Whelp": {"name": "<PERSON>", "rarity": "<PERSON><PERSON>", "cost": 10, "class": "Neutral", "type": "Weapon, Pet", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Weapon, Pet", "Cost": "10", "Class": "Neutral", "Effect": "Start of battle: <PERSON><PERSON> 4 . <PERSON><PERSON><PERSON> 3 debuffs.", "Damage": "5-10 (3.6/s)", "Stamina": "0 (0/s)", "Accuracy": "90%", "Cooldown": "2.1s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1]]}, "Demonic Flask": {"name": "<PERSON><PERSON>", "rarity": "<PERSON><PERSON>", "cost": 7, "class": "Reaper", "type": "Potion", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Potion", "Cost": "7", "Class": "Reaper", "Effect": "Opponent drops below 50%: Consume this and deal 0.45 -damage for every debuff of your opponent.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 0, 0, 0, 0, 0, 0], [1, 0, 0, 1, 0, 1, 0], [1, 0, 1, 1, 0, 1, 1]]}, "Piercing Arrow": {"name": "Piercing Arrow", "rarity": "Unique", "cost": 10, "class": "<PERSON>", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Accessory", "Cost": "10", "Class": "<PERSON>", "Effect": "Weapons deal +50% critical damage. They remove 15  on crit.\n Item activates: 65% chance to gain 1 .", "In shop": "Round 8 subclass item"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0], [1, 1, 1], [0, 1, 0]]}, "Flute": {"name": "Flute", "rarity": "Epic", "cost": 6, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Accessory", "Cost": "6", "Class": "Neutral", "Effect": "Every 4.7s: Randomly gain 14  or 2 stamina or 2 .\nTriggers 10% faster for each  item.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 0, 0, 0, 0, 1], [0, 0, 0, 0, 1, 1], [1, 1, 1, 1, 1, 1], [0, 0, 0, 0, 1, 1], [0, 0, 0, 0, 0, 1]]}, "Carrot": {"name": "Carrot", "rarity": "Rare", "cost": 3, "class": "<PERSON>", "type": "Food", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Food", "Cost": "3", "Class": "<PERSON>", "Effect": "Every 3.2s: Cleanse 1 debuff. If you have at least 4 : 55% chance to gain 1 .\n\n\nFood: Triggers 10% faster for each  Food of a different type (not Carrot).", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 1, 0], [1, 1, 1, 1], [0, 1, 1, 0]]}, "Flame Whip": {"name": "Flame Whip", "rarity": "Legendary", "cost": 10, "class": "Pyromancer", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Weapon", "Cost": "10", "Class": "Pyromancer", "Effect": "On hit: Use 1  to gain 4  and deal +8 damage.", "Damage": "6-10 (4.7/s)", "Stamina": "1.5 (0.9/s)", "Accuracy": "90%", "Cooldown": "1.7s", "Sockets": "2", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1], [0, 0, 1], [0, 0, 1]]}, "Cheese": {"name": "Cheese", "rarity": "Legendary", "cost": 8, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Food", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Food", "Cost": "8", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "Every 4s: <PERSON><PERSON> 10 maximum health and a random buff.\n\n\nFood: Triggers 10% faster for each  Food of a different type (not Cheese).", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 1, 0], [1, 1, 1, 1], [0, 1, 1, 0]]}, "Unstable Recombobulator": {"name": "Unstable Recombobulator", "rarity": "Epic", "cost": 6, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Accessory", "Cost": "6", "Class": "Neutral", "Effect": "Shop entered: Consume this and  items. Create different items based on the combined value.\nEvery 4s: <PERSON>ain 1 random buff and cleanse 1 debuff.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 0, 0, 0, 1], [0, 1, 0, 1, 0], [0, 0, 1, 0, 0], [0, 1, 0, 1, 0], [1, 0, 0, 0, 1]]}, "Strong Demonic Flask": {"name": "Strong Demonic Flask", "rarity": "<PERSON><PERSON>", "cost": 14, "class": "Reaper", "type": "Potion", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Potion", "Cost": "14", "Class": "Reaper", "Effect": "Opponent drops below 50% or you drop below 25%: Consume this and deal 0.75 -damage for each debuff of your opponent with 100% lifesteal. For 3s, reduce opponent's healing by 30%.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 0, 0, 0, 0, 0, 0], [1, 0, 0, 1, 0, 1, 0], [1, 0, 1, 1, 0, 1, 1]]}, "Leather Bag": {"name": "<PERSON><PERSON>", "rarity": "Common", "cost": 4, "class": "Neutral", "type": "Bag", "description": "", "raw_stats": {"Rarity": "Common", "Type": "Bag", "Cost": "4", "Class": "Neutral", "Effect": "Add 4 backpack slots.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1]]}, "Blood Amulet": {"name": "Blood Amulet", "rarity": "Legendary", "cost": 8, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Accessory", "Cost": "8", "Class": "Neutral", "Effect": "Start of battle: <PERSON><PERSON> 2  and 20 maximum health.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Storage Coffin": {"name": "Storage Coffin", "rarity": "Unique", "cost": 20, "class": "Reaper", "type": "Bag", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Bag", "Cost": "20", "Class": "Reaper", "Effect": "Add 8 backpack slots.\nItem inside activates: 25% chance to inflict 1 .", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1], [1, 1], [1, 1]]}, "Shiny Shell": {"name": "Shiny Shell", "rarity": "Common", "cost": 2, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Common", "Type": "Accessory", "Cost": "2", "Class": "Neutral", "Effect": "After 5s: Heal for 5 + 3 for each  -item.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1]]}, "Wolf Badge": {"name": "<PERSON> Badge", "rarity": "Unique", "cost": 5, "class": "Neutral", "type": "Accessory", "description": "Sometimes, late at night, you may hear it bark at you.", "raw_stats": {"Rarity": "Unique", "Type": "Accessory", "Cost": "5", "Class": "Neutral", "Effect": "Berserker items are offered in the shop.\nHealth drops below 50%: Enter Battle Rage for 5s (once).\nDuring Battle Rage:  items trigger 25% faster. You take 20% reduced damage.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1], [1, 1, 1], [1, 1, 1]]}, "Cubert": {"name": "<PERSON><PERSON><PERSON>", "rarity": "Unique", "cost": 8, "class": "Neutral", "type": "Pet", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Pet", "Cost": "8", "Class": "Neutral", "Effect": "activates: 35% chance to gain 1 .\n activates: 30% chance to use 1  to gain 1 .", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 0], [1, 1, 1, 1], [1, 1, 1, 1], [0, 0]]}, "Toad": {"name": "Toad", "rarity": "Epic", "cost": 6, "class": "Reaper", "type": "Pet", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Pet", "Cost": "6", "Class": "Reaper", "Effect": "items gained 10 buffs: Heal for 12.\n\n\n items used 10 buffs: <PERSON><PERSON> 1  and 1 .\nEvery 3.8s: <PERSON>ain 1  and 1 .", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 0, 1, 0, 0], [0, 0, 0, 0, 0], [1, 0, 1, 0, 1], [0, 0, 0, 0, 0], [0, 0, 1, 0, 0]]}, "Hungry Blade": {"name": "Hungry Blade", "rarity": "Epic", "cost": 7, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Weapon", "Cost": "7", "Class": "Neutral", "Effect": "Start of battle: <PERSON>ain 1 .\nOn hit: Use 1  to gain 1 .\nDeals +1 maximum damage per .", "Damage": "3-6 (2.8/s)", "Stamina": "1 (0.6/s)", "Accuracy": "90%", "Cooldown": "1.6s", "Sockets": "1", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1]]}, "Spear": {"name": "Spear", "rarity": "Rare", "cost": 6, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Weapon", "Cost": "6", "Class": "Neutral", "Effect": "On hit: Des<PERSON>y 4  for each free  slot in front of this.", "Damage": "3-8 (3.7/s)", "Stamina": "1 (0.7/s)", "Accuracy": "85%", "Cooldown": "1.5s", "Sockets": "1", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1], [1], [1], [1], [1], [1], [1]]}, "Burning Banner": {"name": "Burning Banner", "rarity": "Unique", "cost": 10, "class": "Pyromancer", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Accessory", "Cost": "10", "Class": "Pyromancer", "Effect": "25% chance to protect your buffs from removal and your opponent's debuffs from cleansing.\n-item activates: 50% chance to inflict 1  for 5s.\nEvery 3.8s: Remove 2 buffs from your opponent and gain 2 .", "In shop": "Round 8 subclass item"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1]]}, "Cauldron": {"name": "<PERSON><PERSON><PERSON>", "rarity": "Unique", "cost": 10, "class": "Reaper", "type": "Accessory", "description": "Everything can be soup with the right mindset.", "raw_stats": {"Rarity": "Unique", "Type": "Accessory", "Cost": "10", "Class": "Reaper", "Effect": "Shop entered: Upgrade an adjacent Potion.\nEvery 3.3s: Heal for 20 or gain 6  or gain 5 .\nTriggers 15% faster for each  Food or Potion.", "In shop": "Round 8 subclass item"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 1, 0], [1, 1, 1, 1], [1, 1, 1, 1]]}, "Squirrel Archer": {"name": "Squirrel Archer", "rarity": "Epic", "cost": 9, "class": "<PERSON>", "type": "Weapon, Pet", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Weapon, Pet", "Cost": "9", "Class": "<PERSON>", "Effect": "On hit: Steal a random buff.\nTriggers 15% faster for each  Pet or Food.", "Damage": "2-3 (0.8/s)", "Stamina": "0 (0/s)", "Accuracy": "85%", "Cooldown": "3.2s", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 0, 1, 0], [0, 1, 1, 1], [1, 1, 1, 1], [0, 1, 1, 0]]}, "Heart Container": {"name": "Heart Container", "rarity": "<PERSON><PERSON>", "cost": 12, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Accessory", "Cost": "12", "Class": "Neutral", "Effect": "Every 3s: Gain 1 .\nUse 7 : <PERSON><PERSON> 100 maximum health, 2  and your healing is increased by 15% (once).", "Sockets": "1", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1]]}, "Oil Lamp": {"name": "Oil Lamp", "rarity": "Epic", "cost": 7, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Accessory", "Cost": "7", "Class": "Neutral", "Effect": "Start of battle: <PERSON><PERSON> 2 .\nEvery 3.4s: The  Weapon gains 1 damage and 5% accuracy.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1], [1, 1]]}, "Pumpkin": {"name": "<PERSON><PERSON><PERSON>", "rarity": "Unique", "cost": 8, "class": "Neutral", "type": "Weapon, Food", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Weapon, Food", "Cost": "8", "Class": "Neutral", "Effect": "On hit: 50% chance to stun for 0.5s.\nFatigue starts: gain 10 .\n\nFood: Triggers 10% faster for each  Food of a different type (not Pumpkin).", "Damage": "6-12 (1.8/s)", "Stamina": "0 (0/s)", "Accuracy": "90%", "Cooldown": "5s", "Sockets": "2", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 1, 0], [1, 1, 1, 1], [1, 1, 1, 1], [0, 1, 1, 0]]}, "Doom Cap": {"name": "Doom Cap", "rarity": "<PERSON><PERSON>", "cost": 10, "class": "Reaper", "type": "Food", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Food", "Cost": "10", "Class": "Reaper", "Effect": "Every 3s: Inflict 3  and reduce opponent's healing by 10%.\n\n\nFood: Triggers 10% faster for each  Food of a different type (not Doom Cap).", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 1, 1, 0], [1, 1, 1, 1, 1], [0, 1, 1, 1, 0], [0, 0, 1, 0, 0]]}, "Obsidian Dragon": {"name": "Obsidian Dragon", "rarity": "<PERSON><PERSON>", "cost": 18, "class": "Pyromancer", "type": "Weapon, Pet", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Weapon, Pet", "Cost": "18", "Class": "Pyromancer", "Effect": "8  gained: G<PERSON> 2 damage and the next hit of the  Weapon is critical.", "Damage": "11-16 (6.4/s)", "Stamina": "0 (0/s)", "Accuracy": "90%", "Cooldown": "2.1s", "Sockets": "2", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 0], [1, 1, 1], [1, 1, 0]]}, "Vampiric Potion": {"name": "Vampiric Potion", "rarity": "Legendary", "cost": 8, "class": "Neutral", "type": "Potion", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Potion", "Cost": "8", "Class": "Neutral", "Effect": "Both characters drop below 80% health: Consume this and gain 3  and deal 15 -damage with 100% lifesteal.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 0, 0, 0, 0, 0, 0], [1, 0, 0, 1, 0, 1, 0], [1, 0, 1, 1, 0, 1, 1]]}, "Flame": {"name": "Flame", "rarity": "Common", "cost": 1, "class": "Pyromancer", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Common", "Type": "Accessory", "Cost": "1", "Class": "Pyromancer", "Effect": "Start of battle: Gain 1 .", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Stone Shoes": {"name": "Stone Shoes", "rarity": "Legendary", "cost": 12, "class": "Neutral", "type": "Shoes", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Shoes", "Cost": "12", "Class": "Neutral", "Effect": "Health drops below 70%: Gain 1 , 1 , and 30 . Reduce /-damage taken by 30% for 5s (once).", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1]]}, "Axe": {"name": "Axe", "rarity": "Rare", "cost": 6, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Weapon", "Cost": "6", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "On hit: Gain 1 damage.", "Damage": "3-6 (2.3/s)", "Stamina": "1.4 (0.7/s)", "Accuracy": "85%", "Cooldown": "2s", "Sockets": "2", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 0], [1, 0]]}, "Stone Armor": {"name": "Stone Armor", "rarity": "Legendary", "cost": 13, "class": "Neutral", "type": "Armor", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Armor", "Cost": "13", "Class": "Neutral", "Effect": "Items use +20% stamina.\nStart of battle: <PERSON><PERSON> 90 .\nEvery 4s: Remove 1  and 1  from opponent.\nHealth drops below 50%: Gain  equal to 40% of your missing health (once).", "Sockets": "2", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1], [1, 1]]}, "Sun Armor": {"name": "Sun Armor", "rarity": "<PERSON><PERSON>", "cost": 15, "class": "Pyromancer", "type": "Armor", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Armor", "Cost": "15", "Class": "Pyromancer", "Effect": "-Items gain Holy.\nStart of battle: <PERSON><PERSON> 70 . <PERSON>ain 1  for each  -item.\nEvery 3s: Use 1  to heal for 12 and cleanse 2 debuffs.", "Sockets": "3", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 1, 0], [1, 1, 1, 1], [1, 1, 1, 1], [1, 1, 1, 1], [0, 1, 1, 0]]}, "Goobling": {"name": "<PERSON><PERSON><PERSON>", "rarity": "Common", "cost": 2, "class": "Neutral", "type": "Pet", "description": "", "raw_stats": {"Rarity": "Common", "Type": "Pet", "Cost": "2", "Class": "Neutral", "Effect": "3  item activations: Heal for 4.", "In shop": "It's Slime Time! needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1]]}, "Wolpertinger": {"name": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "<PERSON><PERSON>", "cost": 12, "class": "Neutral", "type": "Pet", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Pet", "Cost": "12", "Class": "Neutral", "Effect": "Increase base stamina regeneration by 0.7% for each buff you have.\nEvery 5s: Gain 3 of the buff you have least of.\nTriggers 15% faster for each  Pet.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 1, 0], [1, 1, 1, 1], [1, 1, 1, 1], [0, 1, 1, 0]]}, "Molten Dagger": {"name": "<PERSON><PERSON> Dagger", "rarity": "Epic", "cost": 6, "class": "Pyromancer", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Weapon", "Cost": "6", "Class": "Pyromancer", "Effect": "On hit: Use 1  to gain 2 damage.\nOn stun: Triggers extra attack.", "Damage": "3-7 (1.4/s)", "Stamina": "0 (0/s)", "Accuracy": "95%", "Cooldown": "3.5s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1]]}, "Pandamonium": {"name": "Pandamonium", "rarity": "Legendary", "cost": 11, "class": "Neutral", "type": "Weapon", "description": "Prophe<PERSON> says it brings chaos through the means of food poisoning.", "raw_stats": {"Rarity": "Legendary", "Type": "Weapon", "Cost": "11", "Class": "Neutral", "Effect": "Food activates: Inflict 1 .", "Damage": "9-11 (5/s)", "Stamina": "0.7 (0.4/s)", "Accuracy": "90%", "Cooldown": "2s", "Sockets": "2", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 0, 1, 1, 0], [1, 1, 1, 1, 1], [0, 1, 1, 1, 1]]}, "Rainbow Goobert Omegaooze Primeslime": {"name": "Rainbow Goobert Omegaooze Primeslime", "rarity": "<PERSON><PERSON>", "cost": 68, "class": "Reaper", "type": "Pet", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Pet", "Cost": "68", "Class": "Reaper", "Effect": "6  item activations: Heal for 20, gain 20  and 2 , inflict 4  and 4 , and  Weapons gain 4 damage.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1, 1], [1, 1, 1, 1], [1, 1, 1, 1], [1, 1, 1, 1]]}, "Steel Goobert": {"name": "Steel Goobert", "rarity": "Legendary", "cost": 17, "class": "Neutral", "type": "Pet", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Pet", "Cost": "17", "Class": "Neutral", "Effect": "5  item activations:  Weapons gain +2 damage. Gain 16 .", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 0], [1, 1, 1, 1], [1, 1, 1, 1], [0, 0]]}, "Serpent Staff": {"name": "Serpent Staff", "rarity": "Legendary", "cost": 17, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Weapon", "Cost": "17", "Class": "Neutral", "Effect": "On attack: Use 4  to gain 2 damage and inflict 1  for each 4 damage dealt.\nYou have 30% chance to duplicate  you inflict.", "Damage": "8-10 (5/s)", "Stamina": "1 (0.6/s)", "Accuracy": "90%", "Cooldown": "1.8s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1], [1]]}, "Vampiric Gloves": {"name": "Vampiri<PERSON>s", "rarity": "<PERSON><PERSON>", "cost": 12, "class": "Neutral", "type": "Gloves", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Gloves", "Cost": "12", "Class": "Neutral", "Effect": "After 4s: <PERSON><PERSON> 5 ,  items trigger 35% faster.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1, 1]]}, "Armored Power Puppy": {"name": "Armored Power Puppy", "rarity": "<PERSON><PERSON>", "cost": 7, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Pet", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Pet", "Cost": "7", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "Every 2.8s: Randomly gain 1  or 1  or 1 .\nTriggers 10% faster for each  Pet. Triggers 20% faster for each  Food.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 1, 0], [1, 1, 1, 1], [0, 1, 1, 0]]}, "Offering Bowl": {"name": "Offering Bowl", "rarity": "Unique", "cost": 8, "class": "Pyromancer", "type": "Bag", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Bag", "Cost": "8", "Class": "Pyromancer", "Effect": "Add 4 backpack slots.\nStart of battle: Gain 1 .\nShop entered: Consume all items inside. Create a Flame and different items based on the combined value.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1]]}, "Torch": {"name": "<PERSON>ch", "rarity": "Rare", "cost": 5, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Weapon", "Cost": "5", "Class": "Neutral", "Effect": "On hit: 25% chance to gain 1 damage.", "Damage": "2-3 (1.8/s)", "Stamina": "1 (0.7/s)", "Accuracy": "90%", "Cooldown": "1.4s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1]]}, "Sapphire Whelp": {"name": "Sapphire Whelp", "rarity": "<PERSON><PERSON>", "cost": 14, "class": "Pyromancer", "type": "Weapon, Pet", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Weapon, Pet", "Cost": "14", "Class": "Pyromancer", "Effect": "Start of battle: <PERSON><PERSON> 4 .\nOn hit: Use 2  to gain 5  and a random other buff.", "Damage": "5-10 (3.6/s)", "Stamina": "0 (0/s)", "Accuracy": "90%", "Cooldown": "2.1s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1]]}, "Claws of Attack": {"name": "Claws of Attack", "rarity": "Epic", "cost": 8, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Weapon", "Cost": "8", "Class": "Neutral", "Effect": "After 4 hits, gain 1 .\nAttacks 5% faster for every .", "Damage": "4-5 (2.8/s)", "Stamina": "0.5 (0.3/s)", "Accuracy": "90%", "Cooldown": "1.6s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1]]}, "Double Axe": {"name": "Double Axe", "rarity": "Epic", "cost": 12, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Weapon", "Cost": "12", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "On hit: G<PERSON> 2 damage.\nBattle Rage entered: <PERSON><PERSON> extra attack. Damage gain increased to 3.", "Damage": "6-12 (5/s)", "Stamina": "2 (1.1/s)", "Accuracy": "85%", "Cooldown": "1.8s", "Sockets": "5", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1], [1, 1, 1], [0, 1, 0], [0, 1, 0]]}, "Whetstone": {"name": "Whetstone", "rarity": "Common", "cost": 4, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Common", "Type": "Accessory", "Cost": "4", "Class": "Neutral", "Effect": "Start of battle:  Weapons gain 1 damage.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1]]}, "Utility Pouch": {"name": "Utility Pouch", "rarity": "Unique", "cost": 18, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Bag", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Bag", "Cost": "18", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "Add 8 backpack slots.\nWeapons inside deal +30% damage but attack 30% slower.\nAfter 5s: Enter Battle Rage for 6s.\nDuring Battle Rage: +35% lifesteal.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1], [1, 1, 1], [1, 0, 1]]}, "Banana": {"name": "Banana", "rarity": "Common", "cost": 3, "class": "Neutral", "type": "Food", "description": "", "raw_stats": {"Rarity": "Common", "Type": "Food", "Cost": "3", "Class": "Neutral", "Effect": "Every 5s: Heal for 4 and regenerate 1 stamina.\n\n\nFood: Triggers 10% faster for each  Food of a different type (not Banana).", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0, 0], [1, 1, 1, 0], [1, 1, 1, 1], [0, 1, 1, 0]]}, "Skull Badge": {"name": "Skull Badge", "rarity": "Unique", "cost": 5, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Accessory", "Cost": "5", "Class": "Neutral", "Effect": "Reaper items are offered in the shop.\nEvery 1.5s: Inflict a random debuff.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Frozen Buckler": {"name": "<PERSON><PERSON><PERSON>", "rarity": "Epic", "cost": 8, "class": "Neutral", "type": "Shield", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Shield", "Cost": "8", "Class": "Neutral", "Effect": "On attacked (): 30% chance to prevent 7 damage, remove 0.5 stamina from opponent and inflict 1  (up to 10).", "Sockets": "1", "In shop": "Frozen Flame needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1]]}, "Emerald": {"name": "Emerald", "rarity": "Varies", "cost": 1, "class": "Neutral", "type": "Gemstone", "description": "", "raw_stats": {"Rarity": "Varies", "Type": "Gemstone", "Cost": "1/2/4/8/16", "Class": "Neutral", "Effect": "Weapon sockets:\nOn hit: 35/50/80/80/100% chance to inflict 1/1/1/2/3 .\n\nArmor & other sockets:\n10/15/20/25/35% chance to resist .\n\nBackpack:\nAfter 3/4/4/3.5/3s: <PERSON>ain 1/2/3/4/6 .", "In shop": "Box of Riches needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Ripsaw Blade": {"name": "Ripsaw Blade", "rarity": "Legendary", "cost": 10, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Weapon", "Cost": "10", "Class": "Neutral", "Effect": "On hit: Remove 1 damage gained in battle from all opponent Weapons and gain 0.5 damage.", "Damage": "8-10 (5/s)", "Stamina": "1.5 (0.8/s)", "Accuracy": "90%", "Cooldown": "1.8s", "Sockets": "3", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1], [1]]}, "Spiked Collar": {"name": "<PERSON><PERSON>", "rarity": "Legendary", "cost": 6, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Accessory", "Cost": "6", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "Battle Rage lasts 2s longer.\nBattle Rage entered: Gain 1 .", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "King Goobert": {"name": "King <PERSON><PERSON>", "rarity": "<PERSON><PERSON>", "cost": 23, "class": "Neutral", "type": "Pet", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Pet", "Cost": "23", "Class": "Neutral", "Effect": "6  item activations: Heal for 35, protect 3 buffs from removal and use 4  to become invulnerable for 1.5s (up to 3 times).\nEffects of Gemstones socketed in this are increased by 50%.", "Sockets": "2", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1, 1], [1, 1, 1, 1], [1, 1, 1, 1]]}, "Snow Stick": {"name": "Snow Stick", "rarity": "Legendary", "cost": 8, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Weapon", "Cost": "8", "Class": "Neutral", "Effect": "On hit: Inflict 3  and 2  to yourself.", "Damage": "5-8 (3.8/s)", "Stamina": "1.2 (0.7/s)", "Accuracy": "95%", "Cooldown": "1.7s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1], [1]]}, "Burning Coal": {"name": "Burning Coal", "rarity": "Rare", "cost": 2, "class": "Neutral", "type": "Gemstone", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Gemstone", "Cost": "2", "Class": "Neutral", "Effect": "Weapon sockets:\nOn hit: 12% chance to deal +6 damage and gain 1 .\n\nArmor & other sockets:\nStart of battle: Gain 15 .\nResist 5 .\n\nBackpack:\nAfter 5s: Gain 2 , cleanse 3 debuffs.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Deck of Cards": {"name": "Deck of Cards", "rarity": "Rare", "cost": 3, "class": "Reaper", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Accessory", "Cost": "3", "Class": "Reaper", "Effect": "Playing cards are offered in the shop.\nStart of battle: Gain 2 . Start revealing the  Playing card.", "In shop": "Yes", "Adds to shop": "<PERSON> of Spades, <PERSON><PERSON> Lotus, <PERSON><PERSON> Lizard, <PERSON><PERSON>, Reverse!, <PERSON> Fool, The Lovers, White-Eyes Blue Dragon, Heart of the Cards"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1]]}, "Box of Prosperity": {"name": "Box of Prosperity", "rarity": "Epic", "cost": 5, "class": "Neutral", "type": "Bag", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Bag", "Cost": "5", "Class": "Neutral", "Effect": "Add 4 backpack slots.\nShop entered: If this has at least 2 Godly or Unique items inside, generate a chipped Gemstone.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1]]}, "Unidentified Amulet": {"name": "Unidentified Amulet", "rarity": "Rare", "cost": 6, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Accessory", "Cost": "6", "Class": "Neutral", "Effect": "On buy: Gain a random effect.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Poison Ivy": {"name": "Poison Ivy", "rarity": "Unique", "cost": 10, "class": "<PERSON>", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Accessory", "Cost": "10", "Class": "<PERSON>", "Effect": "You have a 5% chance to resist debuffs for each  -item.\n gained: Inflict 2 .\nOpponent reaches 18 : They take +25% damage.", "In shop": "Round 8 subclass item"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 0, 1, 0, 1], [0, 1, 1, 1, 0], [1, 0, 1, 0, 1]]}, "Present": {"name": "Present", "rarity": "Unique", "cost": 10, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Accessory", "Cost": "10", "Class": "Neutral", "Effect": "Shop entered: Instead of gold, you receive items with a higher value.\nStart of battle: Gain 5 random  buffs.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1]]}, "Platinum Customer Card": {"name": "Platinum Customer Card", "rarity": "Epic", "cost": 8, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Accessory", "Cost": "8", "Class": "Neutral", "Effect": "Start of battle: Reflect 2 debuffs for each  Legendary, Godly or Unique item.\n20% chance to protect your buffs from removal.\nChance to find -items +25%.\nYou can obtain +1 -item.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 0, 1], [0, 1, 0], [1, 0, 1]]}, "Dragon Claws": {"name": "Dragon Claws", "rarity": "Epic", "cost": 4, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Gloves", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Gloves", "Cost": "4", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "10% chance to resist .\nDuring Battle Rage:  Items trigger 40% faster.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1, 1]]}, "Ruby Chonk": {"name": "<PERSON>", "rarity": "<PERSON><PERSON>", "cost": 15, "class": "Reaper", "type": "Weapon, Pet", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Weapon, Pet", "Cost": "15", "Class": "Reaper", "Effect": "On hit: <PERSON><PERSON> 1 . When you have at least 12 : 30% chance to stun your opponent for 0.4s.", "Damage": "17-22 (9.3/s)", "Stamina": "0 (0/s)", "Accuracy": "90%", "Cooldown": "2.1s", "Sockets": "2", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1], [1, 1]]}, "Amulet of Darkness": {"name": "Amulet of Darkness", "rarity": "Rare", "cost": 6, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Accessory", "Cost": "6", "Class": "Neutral", "Effect": "10 -damage dealt: Inflict 1 random debuff.\n item activates: 30% chance to deal 5 -damage.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0], [1, 0, 1], [0, 1, 0]]}, "Squirrel": {"name": "Squirrel", "rarity": "Rare", "cost": 5, "class": "<PERSON>", "type": "Pet", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Pet", "Cost": "5", "Class": "<PERSON>", "Effect": "Every 4s: Steal a random buff.\nTriggers 15% faster for each  Pet or Food.", "In shop": "Big Bowl of Treats needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0, 0], [1, 1, 1, 0], [1, 1, 1, 1], [0, 1, 1, 0]]}, "Chain Whip": {"name": "Chain Whip", "rarity": "Legendary", "cost": 8, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Weapon", "Cost": "8", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "During Battle Rage additionally heal for 8.\nDeals +1 damage for each buff you removed from your opponent.\nOn hit: Remove 2 random buffs from your opponent.", "Damage": "4-9 (2.7/s)", "Stamina": "2.1 (0.9/s)", "Accuracy": "85%", "Cooldown": "2.4s", "Sockets": "2", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 0], [0, 1, 0], [0, 1, 1]]}, "Courage Puppy": {"name": "<PERSON>", "rarity": "Legendary", "cost": 7, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Weapon, Pet", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Weapon, Pet", "Cost": "7", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "Deals +2 damage for each  Pet.", "Damage": "5-7 (1.7/s)", "Stamina": "0 (0/s)", "Accuracy": "95%", "Cooldown": "3.5s", "In shop": "<PERSON> needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 1, 0], [1, 1, 1, 1], [0, 1, 1, 0]]}, "Fancy Fencing Rapier": {"name": "<PERSON><PERSON>", "rarity": "<PERSON><PERSON>", "cost": 12, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Weapon", "Cost": "12", "Class": "Neutral", "Effect": "On hit: Use 3  to gain 3 damage.\nOn miss: Gain 3 .", "Damage": "10-16 (9.3/s)", "Stamina": "0.8 (0.6/s)", "Accuracy": "60%", "Cooldown": "1.4s", "Sockets": "1", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1], [1]]}, "Thornbloom": {"name": "Thornbloom", "rarity": "<PERSON><PERSON>", "cost": 14, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Weapon", "Cost": "14", "Class": "Neutral", "Effect": "On hit: Gain 1 . 60% chance to gain 1 .\n gained: Gain 10 maximum health.\nDeals +1 damage per .", "Damage": "7-12 (4.5/s)", "Stamina": "2.1 (1/s)", "Accuracy": "80%", "Cooldown": "2.1s", "Sockets": "2", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 0], [1, 1, 0], [0, 1, 1]]}, "Spectral Dagger": {"name": "<PERSON><PERSON><PERSON> Dagger", "rarity": "Legendary", "cost": 10, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Weapon", "Cost": "10", "Class": "Neutral", "Effect": "On attack: Use 1  to ignore  and deal +7 damage.\nOn stun: Triggers extra attack.", "Damage": "3-6 (2.1/s)", "Stamina": "0 (0/s)", "Accuracy": "95%", "Cooldown": "2.1s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1]]}, "Strong Vampiric Potion": {"name": "Strong Vampiric Potion", "rarity": "<PERSON><PERSON>", "cost": 12, "class": "Reaper", "type": "Potion", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Potion", "Cost": "12", "Class": "Reaper", "Effect": "Both characters drop below 80% health: Consume this and gain 5  and 35% lifesteal for 6s.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 0, 0, 0, 0, 0, 0], [1, 0, 0, 1, 0, 1, 0], [1, 0, 1, 1, 0, 1, 1]]}, "Snake": {"name": "Snake", "rarity": "Unique", "cost": 10, "class": "Reaper", "type": "Pet", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Pet", "Cost": "10", "Class": "Reaper", "Effect": "4% chance for each  to protect  on your opponent from being cleased.\nStart of battle: <PERSON>ain 4  and 50 maximum health for each  Pet.\nEvery 2.3s: Inflict 2 .", "In shop": "Round 8 subclass item"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 1, 0], [0, 1, 1, 1], [1, 1, 1, 0], [1, 0, 0, 0]]}, "Stone Golem": {"name": "Stone Golem", "rarity": "<PERSON><PERSON>", "cost": 16, "class": "Neutral", "type": "Weapon, Pet", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Weapon, Pet", "Cost": "16", "Class": "Neutral", "Effect": "On hit: <PERSON><PERSON> 1 . 30% chance to stun for 0.5s.\nUse 7 : Reduce cooldown to 2.6s and gain 150  (once).\nDeals +10 damage for each  Bag of Stones.", "Damage": "8-13 (1.9/s)", "Stamina": "0 (0/s)", "Accuracy": "85%", "Cooldown": "5.5s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 1, 0], [0, 1, 1, 0], [1, 1, 1, 1]]}, "Magic Staff": {"name": "Magic Staff", "rarity": "Epic", "cost": 10, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Weapon", "Cost": "10", "Class": "Neutral", "Effect": "On attack: Use 3  to deal +6 damage and gain 2 damage.", "Damage": "6-8 (3.9/s)", "Stamina": "1 (0.6/s)", "Accuracy": "90%", "Cooldown": "1.8s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1], [1]]}, "Chili Goobert": {"name": "<PERSON><PERSON>", "rarity": "Epic", "cost": 11, "class": "Pyromancer", "type": "Pet", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Pet", "Cost": "11", "Class": "Pyromancer", "Effect": "6  item activations: Heal for 12 and gain 2 .", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1, 1], [1, 1, 1, 1]]}, "Corrupted Armor": {"name": "Corrupted Armor", "rarity": "<PERSON><PERSON>", "cost": 20, "class": "Neutral", "type": "Armor", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Armor", "Cost": "20", "Class": "Neutral", "Effect": "-Items gain .\n10% chance for each  -item to protect debuffs on your opponent from being cleansed.\nStart of battle: <PERSON><PERSON> 85 .\nEvery 2.4s: Cleanse 2 debuffs and inflict them on your opponent.", "Sockets": "3", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 1, 0], [1, 1, 1, 1], [1, 1, 1, 1], [1, 1, 1, 1], [0, 1, 1, 0]]}, "Armored Wisdom Puppy": {"name": "Armored Wisdom Puppy", "rarity": "<PERSON><PERSON>", "cost": 7, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Pet", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Pet", "Cost": "7", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "Every 4s: <PERSON><PERSON> 14  and cleanse 1 . Increase  gain by 1.\nTriggers 15% faster for each  Pet.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0], [1, 1, 1], [1, 1, 1], [0, 1, 0]]}, "Holo Fire Lizard": {"name": "Holo Fire Lizard", "rarity": "Legendary", "cost": 5, "class": "Reaper", "type": "Playing Card", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Playing Card", "Cost": "5", "Class": "Reaper", "Effect": "On reveal: Increase -damage by 10%. Deal 12 -damage + 4 for each card before. Gain 4 .", "In shop": "Deck of Cards needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1]]}, "Moon Armor": {"name": "Moon Armor", "rarity": "<PERSON><PERSON>", "cost": 19, "class": "Neutral", "type": "Armor", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Armor", "Cost": "19", "Class": "Neutral", "Effect": "Start of battle: <PERSON><PERSON> 50  + 20  for each  -item.\nEvery 2.6s: <PERSON><PERSON> 3  and reflect 2 debuffs.", "Sockets": "3", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 1, 0], [1, 1, 1, 1], [1, 1, 1, 1], [1, 1, 1, 1], [0, 1, 1, 0]]}, "Time Dilator": {"name": "Time Dilator", "rarity": "Unique", "cost": 8, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Accessory", "Cost": "8", "Class": "Neutral", "Effect": "Your and your opponent's Weapons attack 30% slower.\nEvery 1s: Your item with the highest cooldown triggers 6% faster.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Strong Pestilence Flask": {"name": "Strong Pestilence Flask", "rarity": "Legendary", "cost": 10, "class": "Reaper", "type": "Potion", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Potion", "Cost": "10", "Class": "Reaper", "Effect": "Opponent regenerates health: Consume this and inflict 3  and 1  to yourself. After 6s, inflict another 3 .", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 0, 0, 0, 0, 0, 0], [1, 0, 0, 1, 0, 1, 0], [1, 0, 1, 1, 0, 1, 1]]}, "Leather Armor": {"name": "<PERSON><PERSON> Armor", "rarity": "Rare", "cost": 7, "class": "Neutral", "type": "Armor", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Armor", "Cost": "7", "Class": "Neutral", "Effect": "Start of battle: <PERSON><PERSON> 45 . <PERSON><PERSON> 3 debuffs.", "Sockets": "2", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1], [1, 1]]}, "Elephant Rune": {"name": "Elephant Rune", "rarity": "Legendary", "cost": 4, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Gemstone", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Gemstone", "Cost": "4", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "Weapon sockets:\nOn hit: 25% chance to stun for 0.5s (cooldown 3s).\n\nArmor & other sockets:\nStart of battle: 40% chance to resist debuffs for 4s.\n\nBackpack:\nGain 40 maximum health.", "In shop": "Shaman <PERSON> needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Wisdom Puppy": {"name": "<PERSON>", "rarity": "Legendary", "cost": 7, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Pet", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Pet", "Cost": "7", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "Every 4s: <PERSON><PERSON> 10  and cleanse 1 .\n<PERSON><PERSON>s 15% faster for each  Pet.", "In shop": "<PERSON> needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0], [1, 1, 1], [1, 1, 1], [0, 1, 0]]}, "Holy Spear": {"name": "Holy Spear", "rarity": "<PERSON><PERSON>", "cost": 18, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Weapon", "Cost": "18", "Class": "Neutral", "Effect": "On hit: Destroy 10  and cleanse 1 debuff for each  free slot or -item in front of it.\nUse 10 : Become invulnerable and attack 100% faster for 3s (once).", "Damage": "13-18 (6.2/s)", "Stamina": "0.4 (0.2/s)", "Accuracy": "200%", "Cooldown": "2.5s", "Sockets": "2", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1], [1], [1], [1], [1], [1], [1]]}, "Magic Torch": {"name": "Magic Torch", "rarity": "Legendary", "cost": 11, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Weapon", "Cost": "11", "Class": "Neutral", "Effect": "On hit: Use 1 : This and  Weapons gain 1 damage.", "Damage": "3-6 (3/s)", "Stamina": "0.7 (0.5/s)", "Accuracy": "200%", "Cooldown": "1.5s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1], [0, 1, 0]]}, "Gloves of Power": {"name": "Gloves of Power", "rarity": "Legendary", "cost": 10, "class": "Neutral", "type": "Gloves", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Gloves", "Cost": "10", "Class": "Neutral", "Effect": "Weapons deal +20% damage but attack 10% slower.\n Weapon hits: gain 7 .", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1, 1]]}, "Acorn Collar": {"name": "Acorn Collar", "rarity": "Epic", "cost": 6, "class": "<PERSON>", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Accessory", "Cost": "6", "Class": "<PERSON>", "Effect": "items gain 5% critical hit chance for each .", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1], [1, 1, 1], [1, 1, 1]]}, "Heart of Darkness": {"name": "Heart of Darkness", "rarity": "<PERSON><PERSON>", "cost": 19, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Accessory", "Cost": "19", "Class": "Neutral", "Effect": "Every 4s: Steal 2 buffs, prioritizing .\nTriggers 20% faster for each  -item.\nUse 7 : <PERSON>ain 100 maximum health, 4  and your opponent's healing is reduced by 40% (once).", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 1, 0], [1, 1, 1, 1], [1, 1, 1, 1], [0, 1, 1, 0]]}, "Mana Orb": {"name": "<PERSON><PERSON>", "rarity": "Epic", "cost": 6, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Accessory", "Cost": "6", "Class": "Neutral", "Effect": "item activates: 50% chance to gain 1 .\nUse 35 : <PERSON><PERSON> 17 random other buffs (once).", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 0, 1], [0, 1, 0], [1, 0, 1]]}, "Emerald Egg": {"name": "Emerald Egg", "rarity": "Legendary", "cost": 10, "class": "Pyromancer", "type": "Pet", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Pet", "Cost": "10", "Class": "Pyromancer", "Effect": "Start of battle: <PERSON><PERSON> 3 .\n<PERSON><PERSON> after 2 rounds in your backpack.", "In shop": "Dragon Nest needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1]]}, "Mrs. Struggles": {"name": "Mrs. <PERSON>", "rarity": "Legendary", "cost": 7, "class": "Reaper", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Accessory", "Cost": "7", "Class": "Reaper", "Effect": "Every 3.5s: Remove 1 buff of each type from your opponent.\nTriggers 10% faster for each  -item.", "In shop": "Mr. <PERSON><PERSON> needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 0], [1, 1, 0], [0, 1, 1], [0, 1, 1]]}, "Burning Torch": {"name": "Burning Torch", "rarity": "Epic", "cost": 5, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Weapon", "Cost": "5", "Class": "Neutral", "Effect": "Start of battle: <PERSON><PERSON> 2 .\nOn hit: 30% chance to gain 1 damage.", "Damage": "2-3 (1.8/s)", "Stamina": "1 (0.7/s)", "Accuracy": "90%", "Cooldown": "1.4s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1]]}, "Forging Hammer": {"name": "Forging Hammer", "rarity": "Unique", "cost": 3, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Weapon", "Cost": "3", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "Deals additional +1 damage per .", "Damage": "2-5 (1/s)", "Stamina": "0 (0/s)", "Accuracy": "95%", "Cooldown": "3.5s", "Sockets": "1", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1]]}, "Wooden Buckler": {"name": "<PERSON><PERSON>", "rarity": "Common", "cost": 4, "class": "Neutral", "type": "Shield", "description": "", "raw_stats": {"Rarity": "Common", "Type": "Shield", "Cost": "4", "Class": "Neutral", "Effect": "On attacked (): 30% chance to prevent 7 damage and remove 0.3 stamina from opponent.", "Sockets": "1", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1]]}, "Poison Goobert": {"name": "Poison Goobert", "rarity": "Epic", "cost": 12, "class": "Reaper", "type": "Pet", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Pet", "Cost": "12", "Class": "Reaper", "Effect": "5  item activations: Cleanse 2  and inflict 4 .", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1, 1], [1, 1, 1, 1]]}, "Amethyst Egg": {"name": "Amethyst Egg", "rarity": "Legendary", "cost": 10, "class": "Pyromancer", "type": "Pet", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Pet", "Cost": "10", "Class": "Pyromancer", "Effect": "Start of battle: Inflict 4 random debuffs.\nHatches after 2 rounds in your backpack.", "In shop": "Dragon Nest needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1]]}, "Burning Blade": {"name": "Burning Blade", "rarity": "Legendary", "cost": 21, "class": "Pyromancer", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Weapon", "Cost": "21", "Class": "Pyromancer", "Effect": "On hit: Gain 1 .\n4  gained: This and  Weapons gain +1 damage.", "Damage": "7-9 (5/s)", "Stamina": "1 (0.6/s)", "Accuracy": "90%", "Cooldown": "1.6s", "Sockets": "2", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0], [1, 1, 1], [1, 1, 1], [1, 1, 1], [0, 1, 0]]}, "Book of Ice": {"name": "Book of Ice", "rarity": "Rare", "cost": 10, "class": "Pyromancer", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Accessory", "Cost": "10", "Class": "Pyromancer", "Effect": "Every 3.2s: Use 2  to inflict 3 .\n10% chance to cast the  Spell scroll for free.", "In shop": "Frozen Flame needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1]]}, "Holy Armor": {"name": "Holy Armor", "rarity": "Legendary", "cost": 13, "class": "Neutral", "type": "Armor", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Armor", "Cost": "13", "Class": "Neutral", "Effect": "Start of battle: <PERSON><PERSON> 65 . <PERSON><PERSON> 2  for each  -item.\nEvery 2.6s: Cleanse 2 .", "Sockets": "3", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 1, 0], [1, 1, 1, 1], [1, 1, 1, 1], [1, 1, 1, 1], [0, 1, 1, 0]]}, "Armored Courage Puppy": {"name": "Armored Courage Puppy", "rarity": "<PERSON><PERSON>", "cost": 7, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Weapon, Pet", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Weapon, Pet", "Cost": "7", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "Cannot be blocked by shields or trigger .\nDeals +2 damage for each  Pet.", "Damage": "7-9 (2.3/s)", "Stamina": "0 (0/s)", "Accuracy": "95%", "Cooldown": "3.5s", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 1, 0], [1, 1, 1, 1], [0, 1, 1, 0]]}, "Cheese Goobert": {"name": "Cheese Goobert", "rarity": "<PERSON><PERSON>", "cost": 14, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Pet", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Pet", "Cost": "14", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "5  item activations: <PERSON><PERSON> 18 maximum health and 2 random buffs.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1, 1], [1, 1, 1, 1]]}, "Strong Stone Skin Potion": {"name": "Strong Stone Skin Potion", "rarity": "Legendary", "cost": 9, "class": "Neutral", "type": "Potion", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Potion", "Cost": "9", "Class": "Neutral", "Effect": "45  reached: Consume this and convert 15 health to 35  and gain 2  for 4s.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 0, 0, 0, 0, 0, 0], [1, 0, 0, 1, 0, 1, 0], [1, 0, 1, 1, 0, 1, 1]]}, "Artifact Stone: Cold": {"name": "Artifact Stone: Cold", "rarity": "Unique", "cost": 10, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Weapon", "Cost": "10", "Class": "Neutral", "Effect": "Can only be thrown once per battle.\nOn hit: Inflict 3 .\n Weapon hits: Inflict 1 .", "Damage": "2-4 (0.8/s)", "Stamina": "0 (0/s)", "Accuracy": "90%", "Cooldown": "4s", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1]]}, "Blood Harvester": {"name": "Blood Harvester", "rarity": "Unique", "cost": 7, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Weapon", "Cost": "7", "Class": "Neutral", "Effect": "Items give +100% .\nAttacks 5% faster for every .", "Damage": "10-16 (3.3/s)", "Stamina": "0 (0/s)", "Accuracy": "90%", "Cooldown": "4s", "Sockets": "3", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1], [1, 1, 1], [1, 1, 1], [1, 1, 1]]}, "Shaman Mask": {"name": "Shaman <PERSON>", "rarity": "Unique", "cost": 10, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Accessory", "Cost": "10", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "Runes are offered in the shop.\nStart of battle: Gain 1  for each socketed Gemstone.\nEvery 3.4s: Use 2  to gain 5 random buffs.", "In shop": "Round 8 subclass item", "Adds to shop": "<PERSON><PERSON>, <PERSON>, <PERSON>"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1]]}, "Bloodthorne": {"name": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "<PERSON><PERSON>", "cost": 15, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Weapon", "Cost": "15", "Class": "Neutral", "Effect": "On hit: Use 1  to gain 1  and 1 . Deals +1 damage per  and .", "Damage": "4-8 (3.8/s)", "Stamina": "1.6 (1/s)", "Accuracy": "90%", "Cooldown": "1.6s", "Sockets": "2", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1]]}, "Bow and Arrow": {"name": "Bow and Arrow", "rarity": "Epic", "cost": 7, "class": "<PERSON>", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Weapon", "Cost": "7", "Class": "<PERSON>", "Effect": "Weapon hits: <PERSON><PERSON> gain +1 damage (up to 7).", "Damage": "6-9 (2.5/s)", "Stamina": "1.2 (0.4/s)", "Accuracy": "85%", "Cooldown": "3s", "Sockets": "1", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0, 0], [1, 1, 1, 1], [0, 1, 0, 0]]}, "Busted Blade": {"name": "Busted Blade", "rarity": "<PERSON><PERSON>", "cost": 14, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Weapon", "Cost": "14", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "During Battle Rage: Decrease stamina usage to 3 and cooldown to 3s.\nDeals +5 damage per .", "Damage": "50-60 (11/s)", "Stamina": "5 (1/s)", "Accuracy": "95%", "Cooldown": "5s", "Sockets": "6", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1], [1, 1], [1, 1], [1, 0]]}, "Shell Totem": {"name": "Shell Totem", "rarity": "Rare", "cost": 5, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Weapon", "Cost": "5", "Class": "Neutral", "Effect": "Every 3.4s: If your health is above 70%, gain 1 . Otherwise, heal for 8.\nUses -15% stamina for each  -item.", "Stamina": "2 (0.6/s)", "Cooldown": "3.4s", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1, 1, 1, 1, 1], [0, 0, 0, 1, 0, 0, 0]]}, "Jimbo": {"name": "Jimbo", "rarity": "<PERSON><PERSON>", "cost": 5, "class": "Reaper", "type": "Playing Card", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Playing Card", "Cost": "5", "Class": "Reaper", "Effect": "On reveal: <PERSON><PERSON> 6 random buffs.\nFor each pair before: Resist 1 critical hit.\nFor each three of a kind before: Your Weapons use -25% stamina.\nFor each four of a kind before: Activate 2 random revealed cards (except <PERSON><PERSON>).", "In shop": "Deck of Cards needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1]]}, "Pestilence Flask": {"name": "Pestilence Flask", "rarity": "Epic", "cost": 7, "class": "Neutral", "type": "Potion", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Potion", "Cost": "7", "Class": "Neutral", "Effect": "Opponent heals: Consume this and inflict 3  and 1  to yourself.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 0, 0, 0, 0, 0, 0], [1, 0, 0, 1, 0, 1, 0], [1, 0, 1, 1, 0, 1, 1]]}, "Djinn Lamp": {"name": "<PERSON><PERSON><PERSON>", "rarity": "<PERSON><PERSON>", "cost": 11, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Accessory", "Cost": "11", "Class": "Neutral", "Effect": "Every 1.6s: Gain 1  or 1  or 1 , depending on what you have the least of.\nUse 7 , 7 , 7 , 7  and 27 health: Give the  Weapon +27 damage (once).", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1]]}, "Gingerbread Jerry": {"name": "Gingerbread Jerry", "rarity": "Unique", "cost": 8, "class": "Neutral", "type": "Food", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Food", "Cost": "8", "Class": "Neutral", "Effect": "Start of battle: <PERSON><PERSON> 40 maximum health.\nEvery 3s: Use 1 , 1  and 1 : Gain 1 , 3  and 20 maximum health.\n\nFood: Triggers 10% faster for each  Food of a different type (not Gingerbread Jerry).", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0], [1, 1, 1], [1, 1, 1], [0, 1, 0]]}, "Amulet of Energy": {"name": "Amulet of Energy", "rarity": "Rare", "cost": 6, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Accessory", "Cost": "6", "Class": "Neutral", "Effect": "Start of battle: The  item triggers 100% faster for 1s.\n\n\nBuff used: Refund 25% of the used buffs.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1]]}, "Frostbite": {"name": "Frostbite", "rarity": "Legendary", "cost": 11, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Weapon", "Cost": "11", "Class": "Neutral", "Effect": "On hit: 60% chance to inflict 1 .\nOpponent reaches 30 : <PERSON><PERSON> 5  (once).\nDeals +1 damage per  and +0.4 per  of your opponent.", "Damage": "4-7 (3.4/s)", "Stamina": "1 (0.6/s)", "Accuracy": "90%", "Cooldown": "1.6s", "Sockets": "2", "In shop": "Frozen Flame needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1]]}, "Protective Purse": {"name": "Protective Purse", "rarity": "<PERSON><PERSON>", "cost": 2, "class": "Neutral", "type": "Bag", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Bag", "Cost": "2", "Class": "Neutral", "Effect": "Add 1 backpack slot.\nStart of battle: <PERSON><PERSON> 15 .", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Anvil": {"name": "An<PERSON>", "rarity": "Unique", "cost": 10, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Accessory", "Cost": "10", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "Item crafted: Generate a Flame.\nFor each  crafted item, the  Weapons deal +1 damage and use -5% stamina.", "In shop": "Round 8 subclass item"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1, 1, 1], [1, 1, 1, 1]]}, "Amulet of Alchemy": {"name": "Amulet of Alchemy", "rarity": "Rare", "cost": 6, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Accessory", "Cost": "6", "Class": "Neutral", "Effect": "Start of battle: Gain 3 random buffs.\n Potion consumed: 70% chance to repeat its effect after 2.5s.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1], [1, 1, 1], [1, 1, 1], [1, 1, 1], [1, 1, 1], [1, 1, 1], [1, 1, 1], [1, 1, 1], [1, 1, 1]]}, "Nocturnal Lock Lifter": {"name": "Nocturnal Lock Lifter", "rarity": "Unique", "cost": 10, "class": "Reaper", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Accessory", "Cost": "10", "Class": "Reaper", "Effect": "Start of battle: Gain 4 .\n Weapons steal 25% life + 8% per  -item.\nYour healing is increased by 25%.", "In shop": "Round 8 subclass item"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0], [0, 1, 0], [0, 1, 1, 1, 0], [0, 1, 0, 1, 0]]}, "Amulet of the Wild": {"name": "Amulet of the Wild", "rarity": "Rare", "cost": 6, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Accessory", "Cost": "6", "Class": "Neutral", "Effect": "After 5s: Trigger the  Pet and gain 4 .\nReturn damage limit of  against  and  attacks +50%.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1]]}, "Strong Mana Potion": {"name": "Strong Mana <PERSON>tion", "rarity": "Legendary", "cost": 7, "class": "Reaper", "type": "Potion", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Potion", "Cost": "7", "Class": "Reaper", "Effect": "used or health drops below 50%: Consume this and gain 9  and 25 maximum health.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 0, 0, 0, 0, 0, 0], [1, 0, 0, 1, 0, 1, 0], [1, 0, 1, 1, 0, 1, 1]]}, "Fortuna's Hope": {"name": "Fortuna's Hope", "rarity": "Epic", "cost": 8, "class": "<PERSON>", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Weapon", "Cost": "8", "Class": "<PERSON>", "Effect": "On hit: 70% chance to gain 1 .", "Damage": "2-3 (1.5/s)", "Stamina": "0.7 (0.4/s)", "Accuracy": "100%", "Cooldown": "1.7s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1]]}, "Moon Shield": {"name": "Moon Shield", "rarity": "<PERSON><PERSON>", "cost": 18, "class": "Neutral", "type": "Shield", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Shield", "Cost": "18", "Class": "Neutral", "Effect": "items give +30% .\n items gained 12 : Gain 1 .\nOn attacked (/): 30% chance to prevent 12 damage and remove 0.7 stamina from your opponent.", "Sockets": "2", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 1, 0], [1, 1, 1, 1], [1, 1, 1, 1], [1, 1, 1, 1], [0, 1, 1, 0]]}, "Strong Heroic Potion": {"name": "Strong Heroic Potion", "rarity": "Legendary", "cost": 9, "class": "Neutral", "type": "Potion", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Potion", "Cost": "9", "Class": "Neutral", "Effect": "Out of stamina: Consume this and regenerate 4 stamina and gain 1 .", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 0, 0, 0, 0, 0, 0], [1, 0, 0, 1, 0, 1, 0], [1, 0, 1, 1, 0, 1, 1]]}, "Piggybank": {"name": "Piggybank", "rarity": "Common", "cost": 3, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Common", "Type": "Accessory", "Cost": "3", "Class": "Neutral", "Effect": "Shop entered: Gain 1 .\nStart of battle: Gain 2 maximum health for each  Start of battle item.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1], [1, 1]]}, "Wolf Emblem": {"name": "<PERSON>", "rarity": "Unique", "cost": 10, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Accessory", "Cost": "10", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "Wolf companions are offered in the shop.\n Weapons have 10% critical hit chance (+12% for each  Pet).\nEvery 3s: If you have at least 10 , gain 1 . Otherwise, gain 10 .", "In shop": "Round 8 subclass item", "Adds to shop": "<PERSON>, <PERSON> Puppy, <PERSON>uppy"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1, 1, 1, 1, 1]]}, "Wonky Snowman": {"name": "Wonky Snowman", "rarity": "Epic", "cost": 8, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Accessory", "Cost": "8", "Class": "Neutral", "Effect": "On Buy: Split into 2 Snowballs.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1]]}, "Ranger Bag": {"name": "<PERSON>", "rarity": "Unique", "cost": 16, "class": "<PERSON>", "type": "Bag", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Bag", "Cost": "16", "Class": "<PERSON>", "Effect": "Add 6 backpack slots.\nItems inside gain 10% critical hit chance +3% for each .", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1], [1, 1]]}, "Critwood Staff": {"name": "Critwood Staff", "rarity": "Legendary", "cost": 16, "class": "<PERSON>", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Weapon", "Cost": "16", "Class": "<PERSON>", "Effect": "On attack: Use 3  to deal +7 damage and for the next 1.2s, all your attacks are critical.", "Damage": "6-8 (3.9/s)", "Stamina": "1 (0.6/s)", "Accuracy": "90%", "Cooldown": "1.8s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1], [1]]}, "Goobert": {"name": "Goobert", "rarity": "Rare", "cost": 6, "class": "Neutral", "type": "Pet", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Pet", "Cost": "6", "Class": "Neutral", "Effect": "5  item activations: Heal for 9.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1, 1], [1, 1, 1, 1]]}, "The Lovers": {"name": "The Lovers", "rarity": "Common", "cost": 3, "class": "Reaper", "type": "Playing Card", "description": "", "raw_stats": {"Rarity": "Common", "Type": "Playing Card", "Cost": "3", "Class": "Reaper", "Effect": "On reveal: Deal 10 -damage with 100% lifesteal.\nIf the number of cards before is even, gain 2  and your healing is increased by 10%.", "In shop": "Deck of Cards needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1]]}, "Reverse!": {"name": "Reverse!", "rarity": "Rare", "cost": 3, "class": "Reaper", "type": "Playing Card", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Playing Card", "Cost": "3", "Class": "Reaper", "Effect": "On reveal: Reflect 4 debuffs. If there are no duplicate cards before, steal 3 buffs.", "In shop": "Deck of Cards needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1]]}, "Phoenix": {"name": "Phoenix", "rarity": "Legendary", "cost": 11, "class": "Pyromancer", "type": "Weapon, Pet", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Weapon, Pet", "Cost": "11", "Class": "Pyromancer", "Effect": "On attack: Lose 11 health.\nBefore defeat: Use all your  to reincarnate with 6 health per  (once).", "Damage": "15-20 (7/s)", "Stamina": "0 (0/s)", "Accuracy": "85%", "Cooldown": "2.5s", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 0, 1], [1, 1, 1], [0, 1, 0]]}, "Fire Pit": {"name": "Fire Pit", "rarity": "Unique", "cost": 20, "class": "Pyromancer", "type": "Bag", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Bag", "Cost": "20", "Class": "Pyromancer", "Effect": "Add 9 backpack slots.\nShop entered: Spend 1 gold to generate Flame.\nStart battle: <PERSON>ain 4 maximum health for each -item inside.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1], [1, 1, 1], [1, 1, 1]]}, "Leaf Badge": {"name": "Leaf Badge", "rarity": "Unique", "cost": 5, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Accessory", "Cost": "5", "Class": "Neutral", "Effect": "Ranger items are offered in the shop.\n items gain 2% critical hit chance for each .\nEvery 2.2s: Gain 1 .", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0], [1, 1, 1], [0, 1, 0]]}, "Stamina Sack": {"name": "<PERSON><PERSON><PERSON>", "rarity": "Epic", "cost": 5, "class": "Neutral", "type": "Bag", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Bag", "Cost": "5", "Class": "Neutral", "Effect": "Add 3 backpack slots.\nGain 1 maximum stamina.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1]]}, "Sun Shield": {"name": "Sun Shield", "rarity": "<PERSON><PERSON>", "cost": 14, "class": "Pyromancer", "type": "Shield", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Shield", "Cost": "14", "Class": "Pyromancer", "Effect": "items gained 12 : Deal 4 -damage.\nOn attacked (/): 30% chance to prevent 14 damage and remove 0.7 stamina from your opponent.", "Sockets": "2", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 1, 0], [1, 1, 1, 1], [1, 1, 1, 1], [1, 1, 1, 1], [0, 1, 1, 0]]}, "Amulet of Feasting": {"name": "Amulet of Feasting", "rarity": "Rare", "cost": 6, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Accessory", "Cost": "6", "Class": "Neutral", "Effect": "Food triggers 40% faster.\nFood bought: Restock with a random Food.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 0, 0, 0, 1], [0, 0, 0, 1, 1], [0, 0, 1, 1, 1], [0, 1, 1, 1, 1], [1, 1, 1, 1, 1], [0, 1, 1, 1, 1], [0, 0, 1, 1, 1], [0, 0, 0, 1, 1], [0, 0, 0, 0, 1]]}, "Amethyst": {"name": "Amethyst", "rarity": "Varies", "cost": 1, "class": "Neutral", "type": "Gemstone", "description": "", "raw_stats": {"Rarity": "Varies", "Type": "Gemstone", "Cost": "1/2/4/8/16", "Class": "Neutral", "Effect": "Weapon sockets:\nOn hit: 20/30/45/65/100% chance to remove a random buff from your opponent.\n\nArmor & other sockets:\nReduce opponent's healing by 15/20/25/30/40%.\n\nBackpack:\nEvery 4/3/2.5/2/1.2s: Cleanse 1 debuff.", "In shop": "Box of Riches needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Rainbow Goobert Epicglob Uberviscous": {"name": "Rainbow Goobert Epicglob Uberviscous", "rarity": "<PERSON><PERSON>", "cost": 67, "class": "Pyromancer", "type": "Pet", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Pet", "Cost": "67", "Class": "Pyromancer", "Effect": "6  item activations: Heal for 20, gain 20 , 2  and 4 , inflict 4 , and  Weapons gain 4 damage.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1, 1], [1, 1, 1, 1], [1, 1, 1, 1], [1, 1, 1, 1]]}, "Glowing Crown": {"name": "Glowing Crown", "rarity": "<PERSON><PERSON>", "cost": 12, "class": "Neutral", "type": "<PERSON><PERSON><PERSON>", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "<PERSON><PERSON><PERSON>", "Cost": "12", "Class": "Neutral", "Effect": "Every 2.4s: Cleanse 1  and heal for 5.\nUse 10 : Become invulnerable for 2s (once).", "Sockets": "1", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1]]}, "Garlic": {"name": "<PERSON><PERSON><PERSON>", "rarity": "Common", "cost": 2, "class": "Neutral", "type": "Food", "description": "", "raw_stats": {"Rarity": "Common", "Type": "Food", "Cost": "2", "Class": "Neutral", "Effect": "Every 4s: <PERSON><PERSON> 3 . 30% chance to remove 1  from your opponent.\n\n\nFood: Triggers 10% faster for each  Food of a different type (not Garlic).", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0], [1, 1, 1], [1, 1, 1], [0, 1, 0]]}, "Belladonna's Shade": {"name": "Belladonna's Shade", "rarity": "Legendary", "cost": 11, "class": "<PERSON>", "type": "Weapon", "description": "It is all kinds of disgusting, every single one.", "raw_stats": {"Rarity": "Legendary", "Type": "Weapon", "Cost": "11", "Class": "<PERSON>", "Effect": "On hit: 70% chance to inflict 2  and a random debuff.", "Damage": "4-11 (4.4/s)", "Stamina": "0.7 (0.4/s)", "Accuracy": "85%", "Cooldown": "1.7s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1]]}, "Stone Skin Potion": {"name": "Stone Skin Potion", "rarity": "Epic", "cost": 6, "class": "Neutral", "type": "Potion", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Potion", "Cost": "6", "Class": "Neutral", "Effect": "45  reached: Consume this and convert 15 health to 30 .", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 0, 0, 0, 0, 0, 0], [1, 0, 0, 1, 0, 1, 0], [1, 0, 1, 1, 0, 1, 1]]}, "Rainbow Goobert Deathslushy Mansquisher": {"name": "<PERSON> Go<PERSON>rt <PERSON><PERSON>", "rarity": "<PERSON><PERSON>", "cost": 70, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Pet", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Pet", "Cost": "70", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "6  item activations: Gain 20 maximum health, 20 , 2  and 2 random buffs, inflict 4 , and  Weapons gain 4 damage.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1, 1], [1, 1, 1, 1], [1, 1, 1, 1], [1, 1, 1, 1]]}, "Strong Health Potion": {"name": "Strong Health Potion", "rarity": "Epic", "cost": 8, "class": "Neutral", "type": "Potion", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Potion", "Cost": "8", "Class": "Neutral", "Effect": "Health drops below 50%: Consume this and heal for 24, gain 3  and cleanse 4 .", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 0, 0, 0, 0, 0, 0], [1, 0, 0, 1, 0, 1, 0], [1, 0, 1, 1, 0, 1, 1]]}, "Bag of Stones": {"name": "Bag of Stones", "rarity": "Rare", "cost": 3, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Accessory", "Cost": "3", "Class": "Neutral", "Effect": "Stones above can be thrown repeatedly.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 0, 0, 1], [1, 1, 0, 1], [1, 1, 0, 1]]}, "Mega Clover": {"name": "Mega Clover", "rarity": "Unique", "cost": 10, "class": "<PERSON>", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Accessory", "Cost": "10", "Class": "<PERSON>", "Effect": "Sale chance +5%\nChance to find -items +20%.\nShop entered: Generate two Lucky Clovers\n15  reached: G<PERSON> 25 random other buffs.", "In shop": "Round 8 subclass item"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1]]}, "Chtulhu": {"name": "Ch<PERSON>l<PERSON>", "rarity": "Unique", "cost": 8, "class": "Neutral", "type": "Pet", "description": "They call him the \"Hungry Void\" for a reason. Go give him some snacks!", "raw_stats": {"Rarity": "Unique", "Type": "Pet", "Cost": "8", "Class": "Neutral", "Effect": "Every 3.3s: Deal 10 -damage with 100% lifesteal and trigger a random  Food.\n Food gains .\nTriggers 15% faster for each  -item.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 1, 1, 0], [1, 1, 1, 1, 1], [1, 1, 1, 1, 1], [0, 1, 1, 1, 0]]}, "Ruby Egg": {"name": "<PERSON>", "rarity": "Legendary", "cost": 10, "class": "Neutral", "type": "Pet", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Pet", "Cost": "10", "Class": "Neutral", "Effect": "Start of battle: <PERSON><PERSON> 4 . <PERSON><PERSON>ct 3 debuffs.\nHatches after 2 rounds in your backpack.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1]]}, "Unsettling Presence": {"name": "Unsettling Presence", "rarity": "Unique", "cost": 10, "class": "Neutral", "type": "Pet", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Pet", "Cost": "10", "Class": "Neutral", "Effect": "Deal +30% of your healing as -damage.\nEvery 3s: Use a random buff to heal for 12.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1]]}, "Heroic Potion": {"name": "Heroic Potion", "rarity": "Legendary", "cost": 6, "class": "Neutral", "type": "Potion", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Potion", "Cost": "6", "Class": "Neutral", "Effect": "Out of stamina: Consume this and regenerate 2 stamina and gain 1 .", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 0, 0, 0, 0, 0, 0], [1, 0, 0, 1, 0, 1, 0], [1, 0, 1, 1, 0, 1, 1]]}, "Dark Lantern": {"name": "Dark Lantern", "rarity": "Unique", "cost": 10, "class": "Pyromancer", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Accessory", "Cost": "10", "Class": "Pyromancer", "Effect": "Start of battle: Lose 50% health.\nBefore defeat: Reincarnate with 50% life and become invulnerable for 1.5s.\nOn reincarnation: Deal 5 -damage for each  -item and inflict 7 debuffs for each  -item.", "In shop": "Round 8 subclass item"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 0], [1, 1], [1, 1], [1, 0]]}, "Lump of Coal": {"name": "Lump of Coal", "rarity": "Common", "cost": 2, "class": "Neutral", "type": "Gemstone", "description": "", "raw_stats": {"Rarity": "Common", "Type": "Gemstone", "Cost": "2", "Class": "Neutral", "Effect": "Weapon sockets:\nOn attack: 70% chance to deal +1 damage.\n\nArmor & other sockets:\nStart of battle: Gain 8 .\nResist 1 debuff.\n\nBackpack:\nAfter 3s: <PERSON>ain a random buff, inflict a random debuff.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Pop": {"name": "Pop", "rarity": "Unique", "cost": 6, "class": "Neutral", "type": "Weapon, Pet", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Weapon, Pet", "Cost": "6", "Class": "Neutral", "Effect": "Attacks 3% faster for each  (up to 60%).", "Damage": "1-2 (1/s)", "Stamina": "0 (0/s)", "Accuracy": "90%", "Cooldown": "1.5s", "Sockets": "4", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1]]}, "Power Puppy": {"name": "Power Puppy", "rarity": "Legendary", "cost": 7, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Pet", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Pet", "Cost": "7", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "Every 3.2s: Randomly gain 1  or 1  or 1 .\nTriggers 10% faster for each  Pet.", "In shop": "<PERSON> needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 1, 0], [1, 1, 1, 1], [0, 1, 1, 0]]}, "Lightsaber": {"name": "Lightsaber", "rarity": "<PERSON><PERSON>", "cost": 12, "class": "Neutral", "type": "Weapon", "description": "Never look directly into it.", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Weapon", "Cost": "12", "Class": "Neutral", "Effect": "Use 3 : Inflict 8  for 6s (unstackable).\nDeals +1 damage for each  of your opponent.", "Damage": "8-12 (6.7/s)", "Stamina": "1 (0.7/s)", "Accuracy": "95%", "Cooldown": "1.5s", "Sockets": "2", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1], [1]]}, "Poison Dagger": {"name": "<PERSON><PERSON>", "rarity": "Epic", "cost": 11, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Weapon", "Cost": "11", "Class": "Neutral", "Effect": "On hit: Inflict 2 .\nOn stun: <PERSON>ggers extra attack.", "Damage": "3-6 (1.7/s)", "Stamina": "0 (0/s)", "Accuracy": "95%", "Cooldown": "2.7s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1]]}, "Blood Goobert": {"name": "Blood Goobert", "rarity": "Legendary", "cost": 15, "class": "Neutral", "type": "Pet", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Pet", "Cost": "15", "Class": "Neutral", "Effect": "Start of battle: Gain 2 .\n6  item activations: Deal 10 -damage with 100% lifesteal. Deal +1 for each .", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1, 1], [1, 1, 1, 1]]}, "Box of Riches": {"name": "Box of Riches", "rarity": "Rare", "cost": 5, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Accessory", "Cost": "5", "Class": "Neutral", "Effect": "Shop entered: Generate a low-quality gemstone. Gemstones are offered in the shop.", "In shop": "Yes", "Adds to shop": "Amethyst, Emerald, Ruby, Sapphire, Topaz"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1]]}, "Hawk Rune": {"name": "<PERSON>", "rarity": "Legendary", "cost": 4, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Gemstone", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Gemstone", "Cost": "4", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "Weapon sockets:\nCritical hit chance +15%.\nCritical damage +15%.\nArmor & other sockets:\n40% chance to resist .\n\nBackpack:\nEvery 4s: Inflict 1 .", "In shop": "Shaman <PERSON> needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Vineweave Basket": {"name": "Vineweave <PERSON>", "rarity": "Unique", "cost": 20, "class": "<PERSON>", "type": "Bag", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Bag", "Cost": "20", "Class": "<PERSON>", "Effect": "Add 9 backpack slots.\nYour healing is amplified by 10% + 3% per -item inside.\nIn rounds 1 and 10, sale chance is increased by 20%.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1], [1, 1, 1], [1, 1, 1]]}, "Gloves of Haste": {"name": "Gloves of Haste", "rarity": "Rare", "cost": 4, "class": "Neutral", "type": "Gloves", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Gloves", "Cost": "4", "Class": "Neutral", "Effect": "Start of battle:  items trigger 20% faster.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1, 1]]}, "Molten Spear": {"name": "<PERSON><PERSON>pear", "rarity": "Epic", "cost": 8, "class": "Pyromancer", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Weapon", "Cost": "8", "Class": "Pyromancer", "Effect": "Before miss: Use 1  to hit instead and deal +5 damage.\nOn hit: Destroy 5  for each  -item in front of it.", "Damage": "3-8 (3.7/s)", "Stamina": "1 (0.7/s)", "Accuracy": "65%", "Cooldown": "1.5s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1], [1], [1], [1], [1], [1], [1]]}, "White-Eyes Blue Dragon": {"name": "White-Eyes Blue Dragon", "rarity": "Epic", "cost": 4, "class": "Reaper", "type": "Playing Card", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Playing Card", "Cost": "4", "Class": "Reaper", "Effect": "On reveal: You take -10% -damage. <PERSON><PERSON> 12  + 6  for each card before. Inflict 4 .", "In shop": "Deck of Cards needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1]]}, "Badger Rune": {"name": "Badger Rune", "rarity": "Legendary", "cost": 4, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Gemstone", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Gemstone", "Cost": "4", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "Weapon sockets:\nOn hit: Attack 3% faster.\n\nArmor & other sockets:\nDuring Battle Rage: Reduce / damage taken by 7.\n\nBackpack:\nItems use -10% stamina.", "In shop": "Shaman <PERSON> needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Cursed Dagger": {"name": "Cursed <PERSON>", "rarity": "Unique", "cost": 10, "class": "Reaper", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Weapon", "Cost": "10", "Class": "Reaper", "Effect": "On stun: Tri<PERSON>s extra attack.\nOn hit: Inflict 2 random debuffs.\nThis and  items have +1% accuracy and +1% critical hit chance per debuff of your opponent.", "Damage": "4-7 (2.8/s)", "Stamina": "0 (0/s)", "Accuracy": "95%", "Cooldown": "2s", "Sockets": "1", "In shop": "Round 8 subclass item"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1], [1, 1]]}, "Wooden Sword": {"name": "Wooden Sword", "rarity": "Common", "cost": 3, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Common", "Type": "Weapon", "Cost": "3", "Class": "Neutral", "Damage": "1-3 (1.4/s)", "Stamina": "1 (0.7/s)", "Accuracy": "90%", "Cooldown": "1.4s", "Sockets": "1", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1]]}, "Darksaber": {"name": "Darksaber", "rarity": "<PERSON><PERSON>", "cost": 19, "class": "Neutral", "type": "Weapon", "description": "So you found the switch.", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Weapon", "Cost": "19", "Class": "Neutral", "Effect": "On attack: Use 1  to inflict 1 .\nDeals +0.5 damage for each debuff of your opponent.", "Damage": "10-15 (8.3/s)", "Stamina": "1 (0.7/s)", "Accuracy": "95%", "Cooldown": "1.5s", "Sockets": "2", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1], [1]]}, "Shepherds Crook": {"name": "<PERSON><PERSON>", "rarity": "Rare", "cost": 8, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Accessory", "Cost": "8", "Class": "Neutral", "Effect": "Start of battle:  Weapons gain 2 damage.\n25% chance to protect your buffs from removal.\n25% chance to resist  and .", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1], [1, 1], [1, 1]]}, "King Crown": {"name": "King Crown", "rarity": "<PERSON><PERSON>", "cost": 17, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Accessory", "Cost": "17", "Class": "Neutral", "Effect": "Every 2.4s: Heal for 5 and protect 1 buff from removal.\nUse 10 : Become invulnerable for 2.5s (once).\nEffects of Gemstones socketed in this are increased by 50%.", "Sockets": "2", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1]]}, "Sapphire": {"name": "Sapphire", "rarity": "Varies", "cost": 1, "class": "Neutral", "type": "Gemstone", "description": "", "raw_stats": {"Rarity": "Varies", "Type": "Gemstone", "Cost": "1/2/4/8/16", "Class": "Neutral", "Effect": "Weapon sockets:\nOn hit: 15/25/40/60/80% chance to ignore , gain 1  and inflict 1 .\n\nArmor & other sockets:\n5  gained: <PERSON>ain 2/3/4/5/7 .\n\nBackpack:\nAfter 3/4/4/3.5/3s: Inflict 1/2/3/4/6 .", "In shop": "Box of Riches needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Stone": {"name": "Stone", "rarity": "Common", "cost": 1, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Common", "Type": "Weapon", "Cost": "1", "Class": "Neutral", "Effect": "Can only be thrown once per battle.\nOn hit: Destroy 4 .", "Damage": "2-4 (1.2/s)", "Stamina": "0 (0/s)", "Accuracy": "70%", "Cooldown": "2.5s", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Pineapple": {"name": "Pineapple", "rarity": "Legendary", "cost": 7, "class": "Neutral", "type": "Food", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Food", "Cost": "7", "Class": "Neutral", "Effect": "Every 3.3s: Gain 1  and heal for 4.\n\n\nFood: Triggers 10% faster for each  Food of a different type (not Pineapple).", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0], [1, 1, 1], [1, 1, 1], [1, 1, 1], [0, 1, 0]]}, "Brass Knuckles": {"name": "<PERSON> Knuckles", "rarity": "Unique", "cost": 10, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Weapon", "Cost": "10", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "On hit: 30% chance to stun for 0.5s, this and  Items gain 5% accuracy and 5% critical hit chance.\nDuring Battle Rage: <PERSON>gger 50% faster.", "Damage": "3-5 (1.6/s)", "Stamina": "0 (0/s)", "Accuracy": "90%", "Cooldown": "2.5s", "In shop": "Round 8 subclass item"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1], [1, 1, 1]]}, "Emerald Whelp": {"name": "Emerald Whelp", "rarity": "<PERSON><PERSON>", "cost": 14, "class": "Pyromancer", "type": "Weapon, Pet", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Weapon, Pet", "Cost": "14", "Class": "Pyromancer", "Effect": "Start of battle: <PERSON><PERSON> 3 .\nOn hit: Inflict 3 .", "Damage": "5-10 (3.6/s)", "Stamina": "0 (0/s)", "Accuracy": "90%", "Cooldown": "2.1s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1]]}, "Darkest Lotus": {"name": "Darkest Lotus", "rarity": "<PERSON><PERSON>", "cost": 7, "class": "Reaper", "type": "Playing Card", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Playing Card", "Cost": "7", "Class": "Reaper", "Effect": "On reveal: For each card before, gain 3  and remove 3 random buffs from your opponent.", "In shop": "Deck of Cards needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1]]}, "Spiked Shield": {"name": "Spiked Shield", "rarity": "Rare", "cost": 8, "class": "Neutral", "type": "Shield", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Shield", "Cost": "8", "Class": "Neutral", "Effect": "On attacked (): 30% chance to prevent 7 damage, remove 0.3 stamina from opponent, and gain 1  (up to 4).", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1]]}, "Death Scythe": {"name": "<PERSON> Scythe", "rarity": "Legendary", "cost": 12, "class": "Reaper", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Weapon", "Cost": "12", "Class": "Reaper", "Effect": "Items inflict +100% .\nOpponent reaches 35 : Gain 50% critical hit chance.", "Damage": "6-10 (5.7/s)", "Stamina": "1.2 (0.9/s)", "Accuracy": "90%", "Cooldown": "1.4s", "Sockets": "3", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1], [1, 1, 1], [1, 1, 1], [1, 1, 1]]}, "Ruby": {"name": "<PERSON>", "rarity": "Varies", "cost": 1, "class": "Neutral", "type": "Gemstone", "description": "", "raw_stats": {"Rarity": "Varies", "Type": "Gemstone", "Cost": "1/2/4/8/16", "Class": "Neutral", "Effect": "Weapon sockets:\nOn hit: <PERSON><PERSON> 7/10/15/20/30% lifesteal.\n\nArmor & other sockets:\nYour healing is increased by 10/15/20/25/35%.\n\nBackpack:\nAfter 5s: Deal 4/6/10/15/30 -damage with 100% lifesteal.", "In shop": "Box of Riches needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Lucky Clover": {"name": "<PERSON> Clover", "rarity": "Rare", "cost": 2, "class": "<PERSON>", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Accessory", "Cost": "2", "Class": "<PERSON>", "Effect": "Start of battle: Gain 1 .", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Stable Recombobulator": {"name": "Stable Recombobulator", "rarity": "Unique", "cost": 6, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Accessory", "Cost": "6", "Class": "Neutral", "Effect": "Shop entered: Consume  items. Create different items based on the combined value.\nEvery 2.5s: Gain 1 random buff and cleanse 1 debuff.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 0, 1, 0, 1], [0, 1, 1, 1, 0], [1, 1, 1, 1, 1], [0, 1, 1, 1, 0], [1, 0, 1, 0, 1]]}, "Ice Armor": {"name": "Ice Armor", "rarity": "Epic", "cost": 11, "class": "Neutral", "type": "Armor", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Armor", "Cost": "11", "Class": "Neutral", "Effect": "Start of battle: <PERSON><PERSON> 45  and inflict 4 .\nEvery 5s: Use 1  to inflict 2  and gain 10 .", "Sockets": "2", "In shop": "Frozen Flame needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1], [1, 1]]}, "Pocket Sand": {"name": "Pocket Sand", "rarity": "Common", "cost": 2, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Common", "Type": "Accessory", "Cost": "2", "Class": "Neutral", "Effect": "Start of battle: Inflict 1 .", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Rainbow Goobert Megasludge Alphapuddle": {"name": "Rainbow Goobert Megasludge Alphapuddle", "rarity": "<PERSON><PERSON>", "cost": 68, "class": "<PERSON>", "type": "Pet", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Pet", "Cost": "68", "Class": "<PERSON>", "Effect": "6  item activations: Heal for 20, gain 20 , 2  and 2 , inflict 4 , and  Weapons gain 4 damage.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1, 1], [1, 1, 1, 1], [1, 1, 1, 1], [1, 1, 1, 1]]}, "Impractically Large Greatsword": {"name": "Impractically Large Greatsword", "rarity": "<PERSON><PERSON>", "cost": 14, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Weapon", "Cost": "14", "Class": "Neutral", "Effect": "While you have at least 5 , decrease stamina usage to 2 and cooldown to 2s.", "Damage": "40-50 (9/s)", "Stamina": "5 (1/s)", "Accuracy": "90%", "Cooldown": "5s", "Sockets": "4", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1], [1, 1], [1, 0]]}, "Staff of Fire": {"name": "Staff of Fire", "rarity": "Legendary", "cost": 18, "class": "Pyromancer", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Weapon", "Cost": "18", "Class": "Pyromancer", "Effect": "On attack: Use 2  and 2  to gain 6 damage.", "Damage": "10-12 (6.1/s)", "Stamina": "1 (0.6/s)", "Accuracy": "90%", "Cooldown": "1.8s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1], [1]]}, "Flame Badge": {"name": "Flame Badge", "rarity": "Unique", "cost": 5, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Accessory", "Cost": "5", "Class": "Neutral", "Effect": "Pyromancer items are offered in the shop.\nShop entered: 65% chance to spend 1  and generate a Flame.\nStart of battle: Gain 6 .", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Thorn Whip": {"name": "Thorn Whip", "rarity": "Epic", "cost": 8, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Weapon", "Cost": "8", "Class": "Neutral", "Effect": "On hit: <PERSON><PERSON> 1 .\nDeals +1 damage per .", "Damage": "4-9 (3/s)", "Stamina": "2.2 (1/s)", "Accuracy": "80%", "Cooldown": "2.2s", "Sockets": "2", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 0], [0, 1, 0], [0, 1, 1]]}, "Cap of Resilience": {"name": "Cap of Resilience", "rarity": "Epic", "cost": 7, "class": "Neutral", "type": "<PERSON><PERSON><PERSON>", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "<PERSON><PERSON><PERSON>", "Cost": "7", "Class": "Neutral", "Effect": "Start of battle: Reduce damage taken by 25% for 3s.\n15% chance to resist critical hits.\n15% chance to resist stuns.", "Sockets": "1", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1]]}, "Vampiric Armor": {"name": "Vampiric Armor", "rarity": "Legendary", "cost": 16, "class": "Neutral", "type": "Armor", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Armor", "Cost": "16", "Class": "Neutral", "Effect": "Start of battle: Convert 30 health into 65  and gain 2 .\nEvery 2.8s: Convert 10 health into 20 .", "Sockets": "2", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1], [1, 1]]}, "Divine Potion": {"name": "Divine Potion", "rarity": "<PERSON><PERSON>", "cost": 7, "class": "Neutral", "type": "Potion", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Potion", "Cost": "7", "Class": "Neutral", "Effect": "You reached 10 debuff: Consume this and cleanse 10 debuffs.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 0, 0, 0, 0, 0, 0], [1, 0, 0, 1, 0, 1, 0], [1, 0, 1, 1, 0, 1, 1]]}, "Corrupted Crystal": {"name": "Corrupted Crystal", "rarity": "Epic", "cost": 7, "class": "Neutral", "type": "Gemstone", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Gemstone", "Cost": "7", "Class": "Neutral", "Effect": "Weapon sockets:\nOpponent below 30% health: Deal +50% damage.\n\nArmor & other sockets:\n7 debuffs inflicted: Gain  6 .\n\nBackpack:\nEvery 5.5s: Inflict Fatigue damage.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Dragonskin Boots": {"name": "<PERSON><PERSON> Boots", "rarity": "Epic", "cost": 6, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Shoes", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Shoes", "Cost": "6", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "20% chance to resist .\nBattle Rage entered: Cleanse 3 debuffs, gain 1  and 20 .", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1]]}, "Bloody Dagger": {"name": "Bloody Dagger", "rarity": "Legendary", "cost": 12, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Weapon", "Cost": "12", "Class": "Neutral", "Effect": "On hit: <PERSON>ain 1  (up to 5 per battle). Heal 4 per  -item.\nOn stun: Triggers extra attack.", "Damage": "4-7 (2.3/s)", "Stamina": "0 (0/s)", "Accuracy": "95%", "Cooldown": "2.4s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0], [1, 1, 1], [1, 1, 1], [0, 1, 0]]}, "Shortbow": {"name": "Shortbow", "rarity": "Common", "cost": 4, "class": "<PERSON>", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Common", "Type": "Weapon", "Cost": "4", "Class": "<PERSON>", "Damage": "2-3 (1.5/s)", "Stamina": "0.7 (0.4/s)", "Accuracy": "85%", "Cooldown": "1.7s", "Sockets": "1", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1]]}, "Amulet of Life": {"name": "Amulet of Life", "rarity": "Rare", "cost": 6, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Accessory", "Cost": "6", "Class": "Neutral", "Effect": "Start of battle: <PERSON><PERSON> 20 maximum health.\nYour healing is increased by 20%.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Customer Card": {"name": "Customer Card", "rarity": "Rare", "cost": 4, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Accessory", "Cost": "4", "Class": "Neutral", "Effect": "Increases the rarity of 1 item in the shop every time it refreshes.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Yggdrasil Leaf": {"name": "Yggdrasil Leaf", "rarity": "Unique", "cost": 10, "class": "<PERSON>", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Accessory", "Cost": "10", "Class": "<PERSON>", "Effect": "Start of battle: <PERSON><PERSON> 2  and 1  for each  -item.\n5  used: Heal for 17 and cleanse 2 debuffs.", "In shop": "Round 8 subclass item"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 0, 1, 0, 1], [0, 1, 1, 1, 0], [0, 0, 1, 0, 0], [0, 1, 0, 1, 0]]}, "Mana Potion": {"name": "<PERSON><PERSON>", "rarity": "Epic", "cost": 6, "class": "Neutral", "type": "Potion", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Potion", "Cost": "6", "Class": "Neutral", "Effect": "used or health drops below 50%: Consume this and gain 4  and 18 maximum health.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 0, 0, 0, 0, 0, 0], [1, 0, 0, 1, 0, 1, 0], [1, 0, 1, 1, 0, 1, 1]]}, "Carrot Goobert": {"name": "Carrot Goobert", "rarity": "Epic", "cost": 12, "class": "<PERSON>", "type": "Pet", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Pet", "Cost": "12", "Class": "<PERSON>", "Effect": "6  item activations: Cleanse 4 random debuffs and gain 2  for 6s.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1, 1], [1, 1, 1, 1]]}, "Relic Case": {"name": "Relic Case", "rarity": "Unique", "cost": 12, "class": "Reaper", "type": "Bag", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Bag", "Cost": "12", "Class": "Reaper", "Effect": "Add 4 backpack slots.\nEvery 3.2s: Weapons inside deal +5% damage and use -5% stamina.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1], [1]]}, "Leather Boots": {"name": "<PERSON><PERSON>", "rarity": "Epic", "cost": 6, "class": "Neutral", "type": "Shoes", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Shoes", "Cost": "6", "Class": "Neutral", "Effect": "Health drops below 70%: Gain 1 , 1  and 15  (once).", "Sockets": "1", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1]]}, "Rainbow Badge": {"name": "<PERSON> Badge", "rarity": "Unique", "cost": 5, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Accessory", "Cost": "5", "Class": "Neutral", "Effect": "Items of all classes are offered in the shop.\nAfter 7s: Gain 1 of every type of buff.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Snowball": {"name": "Snowball", "rarity": "Epic", "cost": 4, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Accessory", "Cost": "4", "Class": "Neutral", "Effect": "Start of battle: Inflict 2 .\nYour opponent gains 15% less maximum health from items.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Fortuna's Grace": {"name": "Fortuna's Grace", "rarity": "Legendary", "cost": 11, "class": "<PERSON>", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Weapon", "Cost": "11", "Class": "<PERSON>", "Effect": "Start of battle: G<PERSON> 3 .\n Weapon crits: Attack twice on the next attack.", "Damage": "6-9 (2.5/s)", "Stamina": "1.2 (0.4/s)", "Accuracy": "85%", "Cooldown": "3s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0, 0], [1, 1, 1, 1], [0, 1, 0, 0]]}, "Artifact Stone: Heat": {"name": "Artifact Stone: Heat", "rarity": "Unique", "cost": 9, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Weapon", "Cost": "9", "Class": "Neutral", "Effect": "Can only be thrown once per battle.\nOn hit: Gain 3 .\n10  reached:  Weapons gain 8 damage.", "Damage": "4-6 (1.3/s)", "Stamina": "0 (0/s)", "Accuracy": "90%", "Cooldown": "4s", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1]]}, "Prismatic Sword": {"name": "Prismatic Sword", "rarity": "<PERSON><PERSON>", "cost": 15, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Weapon", "Cost": "15", "Class": "Neutral", "Effect": "For each...\n -item: +8% attack speed.\n -item: +15% lifesteal.\n -item: Gain +0.3 damage on hit.\n -item: +12% chance to inflict 4 random debuffs on hit.", "Damage": "8-14 (7.9/s)", "Stamina": "1 (0.7/s)", "Accuracy": "90%", "Cooldown": "1.4s", "Sockets": "2", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0], [1, 1, 1], [1, 1, 1], [1, 1, 1], [0, 1, 0]]}, "Burning Sword": {"name": "Burning Sword", "rarity": "Epic", "cost": 13, "class": "Pyromancer", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Weapon", "Cost": "13", "Class": "Pyromancer", "Effect": "On hit: 40% chance to gain 1 .\n5  gained: This and  Weapons gain +1 damage.", "Damage": "2-4 (1.9/s)", "Stamina": "1 (0.6/s)", "Accuracy": "90%", "Cooldown": "1.6s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0], [1, 1, 1], [1, 1, 1], [0, 1, 0]]}, "Health Potion": {"name": "Health Potion", "rarity": "Rare", "cost": 4, "class": "Neutral", "type": "Potion", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Potion", "Cost": "4", "Class": "Neutral", "Effect": "Health drops below 50%: Consume this and heal for 12 and cleanse 4 .", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 0, 0, 0, 0, 0, 0], [1, 0, 0, 1, 0, 1, 0], [1, 0, 1, 1, 0, 1, 1]]}, "Mr. Struggles": {"name": "Mr. <PERSON>", "rarity": "Unique", "cost": 10, "class": "Reaper", "type": "Accessory", "description": "This happens when you load all your bad emotions into your plushies.", "raw_stats": {"Rarity": "Unique", "Type": "Accessory", "Cost": "10", "Class": "Reaper", "Effect": "Every 3s: Inflict Fatigue damage.\nOn debuffed: 25% chance to inflict the same debuff.\nHealth drops below 50%:  Items trigger 140% faster for 8s (once).\nPlushies are offered in the shop.", "In shop": "Round 8 subclass item", "Adds to shop": "<PERSON>, Mrs. <PERSON>"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 0, 1], [0, 1, 0], [1, 1, 0]]}, "Jynx torquilla": {"name": "Jynx torquilla", "rarity": "Legendary", "cost": 8, "class": "Neutral", "type": "Pet", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Pet", "Cost": "8", "Class": "Neutral", "Effect": "Every 3s:  items trigger 5% faster (up to 40%). Remove 1  from your opponent.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 0, 0, 0, 0, 1], [0, 0, 0, 0, 1, 1], [0, 0, 0, 1, 1, 1], [0, 1, 1, 1, 1, 1], [1, 1, 0, 1, 1, 1], [0, 0, 0, 0, 1, 1], [0, 0, 0, 0, 0, 1]]}, "Tim": {"name": "<PERSON>", "rarity": "Unique", "cost": 10, "class": "Neutral", "type": "Gemstone", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Gemstone", "Cost": "10", "Class": "Neutral", "Effect": "Weapon sockets:\nOn hit: 50% chance to steal a random buff.\n\nArmor & other sockets:\n25% chance to resist debuffs or critical hits.\n\nBackpack:\nOpponent drops below 30%: Heal for 50 and gain 5 .", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Strong Divine Potion": {"name": "Strong Divine Potion", "rarity": "<PERSON><PERSON>", "cost": 15, "class": "Reaper", "type": "Potion", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Potion", "Cost": "15", "Class": "Reaper", "Effect": "You reached 10 debuffs: Consume this and cleanse 10 debuffs and gain 8 random buffs.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 0, 0, 0, 0, 0, 0], [1, 0, 0, 1, 0, 1, 0], [1, 0, 1, 1, 0, 1, 1]]}, "Lucky Piggy": {"name": "<PERSON> Piggy", "rarity": "Epic", "cost": 7, "class": "<PERSON>", "type": "Accessory", "description": "You are so lucky to have that piggy.", "raw_stats": {"Rarity": "Epic", "Type": "Accessory", "Cost": "7", "Class": "<PERSON>", "Effect": "Shop entered: Gain 1 .\nStart of battle: Gain 2 .\nChance-based effects of the  items are 15% more likely to trigger.", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0], [1, 1, 1]]}, "Potion Belt": {"name": "Potion Belt", "rarity": "Legendary", "cost": 5, "class": "Neutral", "type": "Bag", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Bag", "Cost": "5", "Class": "Neutral", "Effect": "Add 4 backpack slots.\nFirst Potion inside consumed: Gain a random buff.\n4 Potions inside consumed: Cleanse 8 debuffs.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1], [1]]}, "Bunch of Coins": {"name": "Bunch of Coins", "rarity": "Rare", "cost": 9, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Accessory", "Cost": "9", "Class": "Neutral", "Effect": "They don't do anything. But you can sell them for profit!", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Hero Sword": {"name": "Hero Sword", "rarity": "Epic", "cost": 7, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Weapon", "Cost": "7", "Class": "Neutral", "Effect": "Start of battle:  Weapons gain 1 damage.", "Damage": "2-4 (2.1/s)", "Stamina": "0.7 (0.5/s)", "Accuracy": "90%", "Cooldown": "1.4s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0], [1, 1, 1], [1, 1, 1], [0, 1, 0]]}, "Crossblades": {"name": "Crossblades", "rarity": "<PERSON><PERSON>", "cost": 38, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Weapon", "Cost": "38", "Class": "Neutral", "Effect": "Start of battle: The  Weapon gains 10 damage. The  item triggers 60% faster.\nOn hit: Gain +1 damage and trigger 4% faster.", "Damage": "16-19 (12.5/s)", "Stamina": "1 (0.7/s)", "Accuracy": "100%", "Cooldown": "1.4s", "Sockets": "3", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0, 0], [0, 1, 0, 0], [1, 1, 1], [0, 1, 0, 0]]}, "Amulet of Steel": {"name": "Amulet of Steel", "rarity": "Rare", "cost": 6, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Accessory", "Cost": "6", "Class": "Neutral", "Effect": "Start of battle: <PERSON><PERSON> 25 .\n items gained 35 : Gain 1 .", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1, 1, 1]]}, "Friendly Fire": {"name": "Friendly Fire", "rarity": "Unique", "cost": 10, "class": "Pyromancer", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Accessory", "Cost": "10", "Class": "Pyromancer", "Effect": "Every 3s: Use 1  to gain 2 .\nTriggers 10% faster for each  -item.\n20  reached: Gain 5 .\n40  reached: Gain 15 .\n60  reached: Deal 100 -damage.", "In shop": "Round 8 subclass item"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1], [1, 1, 1], [1, 1, 1], [1, 1, 1]]}, "Villain Sword": {"name": "<PERSON>in <PERSON>", "rarity": "Unique", "cost": 7, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Weapon", "Cost": "7", "Class": "Neutral", "Effect": "-Weapons deal -2 damage. Deals +4 damage per  -Weapon.", "Damage": "2-4 (2.3/s)", "Stamina": "0.3 (0.2/s)", "Accuracy": "90%", "Cooldown": "1.3s", "Sockets": "1", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 0, 1, 0, 0], [0, 1, 1, 1, 0], [1, 1, 1, 1, 1], [1, 1, 1, 1, 1], [0, 1, 1, 1, 0], [0, 0, 1, 0, 0]]}, "Draconic Orb": {"name": "Draconic Orb", "rarity": "Epic", "cost": 8, "class": "Pyromancer", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Accessory", "Cost": "8", "Class": "Pyromancer", "Effect": "15  reached: Your next 3 hits are critical.\nEvery 3.8s: Remove 1  from your opponent and gain 1  per removed .", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Topaz": {"name": "Topaz", "rarity": "Varies", "cost": 1, "class": "Neutral", "type": "Gemstone", "description": "", "raw_stats": {"Rarity": "Varies", "Type": "Gemstone", "Cost": "1/2/4/8/16", "Class": "Neutral", "Effect": "Weapon sockets:\nAttacks 10/15/20/25/35% faster.\n\nArmor & other sockets:\n10/15/20/30/35% chance to resist stuns.\n4/6/8/10/15% chance resist critical hits.\nBackpack:\nBase stamina regeneration +8/12/20/30/45%.", "In shop": "Box of Riches needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Artifact Stone: Death": {"name": "Artifact Stone: Death", "rarity": "Unique", "cost": 8, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Weapon", "Cost": "8", "Class": "Neutral", "Effect": "Can only be thrown once per battle.\nOn hit: Inflict Fatigue damage.\n items have +7% critical hit chance per Fatigue level of your opponent.", "Damage": "7-9 (3.5/s)", "Stamina": "0 (0/s)", "Accuracy": "90%", "Cooldown": "2.3s", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1]]}, "Prismatic Orb": {"name": "Prismatic Orb", "rarity": "<PERSON><PERSON>", "cost": 13, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Accessory", "Cost": "13", "Class": "Neutral", "Effect": "Start of battle: For each...\n -item: <PERSON>ain 2 .\n -item: Gain 1 .\n -item: Increase your healing by 4%.\n -item: Inflict a random debuff.\n\n\nEvery 8s: Gain 1 of every type of buff.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 0, 1, 0, 0], [0, 1, 0, 1, 0], [1, 0, 1, 0, 1], [0, 1, 0, 1, 0], [0, 0, 1, 0, 0]]}, "Cap of Discomfort": {"name": "Cap of Discomfort", "rarity": "Legendary", "cost": 14, "class": "Neutral", "type": "<PERSON><PERSON><PERSON>", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "<PERSON><PERSON><PERSON>", "Cost": "14", "Class": "Neutral", "Effect": "Start of battle: Reduce damage taken by 25% for 5s.\nOpponent gains buff: 15% chance to nullify it.\nYour opponent's healing is reduced by 30%.", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1]]}, "Fanfare": {"name": "Fanfare", "rarity": "<PERSON><PERSON>", "cost": 7, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Accessory", "Cost": "7", "Class": "Neutral", "Effect": "Every 3s: Randomly gain 1  or gain 3  and remove 2  from opponent or remove 1 stamina from opponent.\nTriggers 10% faster for each  item.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 0, 0, 0, 1, 0], [0, 0, 0, 1, 1, 0], [1, 1, 1, 1, 1, 1], [0, 0, 0, 1, 1, 0], [0, 0, 0, 0, 1, 0]]}, "Blueberries": {"name": "Blueberries", "rarity": "Rare", "cost": 2, "class": "Neutral", "type": "Food", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Food", "Cost": "2", "Class": "Neutral", "Effect": "Every 3.5s: <PERSON>ain 1 . If you have at least 10 : gain 1  instead.\n\n\nFood: Triggers 10% faster for each  Food of a different type (not Blueberries).", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0], [1, 1, 1], [0, 1, 0]]}, "Maneki-neko": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rarity": "Legendary", "cost": 10, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Accessory", "Cost": "10", "Class": "Neutral", "Effect": "Sale chance +3%.\nValue of  items > 20 \n15% chance to resist critical hits.\nValue of  items > 40 \nGodly and Unique items trigger 15% faster.", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0], [1, 1, 1], [1, 1, 1], [0, 1, 0]]}, "Katana": {"name": "<PERSON><PERSON>", "rarity": "<PERSON><PERSON>", "cost": 14, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Weapon", "Cost": "14", "Class": "Neutral", "Effect": "On hit: Remove 1 damage gained in battle from all opponent Weapons and gain 1 damage. If your opponent has at least 20 buffs, remove 2 of the type they have the most of.", "Damage": "8-10 (5/s)", "Stamina": "1.5 (0.8/s)", "Accuracy": "90%", "Cooldown": "1.8s", "Sockets": "3", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1], [1]]}, "Sapphire Egg": {"name": "Sapphire Egg", "rarity": "Legendary", "cost": 10, "class": "Pyromancer", "type": "Pet", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Pet", "Cost": "10", "Class": "Pyromancer", "Effect": "Start of battle: <PERSON><PERSON> 4 .\n<PERSON><PERSON> after 2 rounds in your backpack.", "In shop": "Dragon Nest needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1]]}, "Broom": {"name": "Broom", "rarity": "Common", "cost": 4, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Common", "Type": "Weapon", "Cost": "4", "Class": "Neutral", "Effect": "Opponent misses attack: <PERSON><PERSON> +2 damage for the next attack.\nOn hit: 33% chance to inflict 1 .", "Damage": "2-4 (1.8/s)", "Stamina": "1.2 (0.7/s)", "Accuracy": "95%", "Cooldown": "1.7s", "Sockets": "1", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1], [1]]}, "Falcon Blade": {"name": "Falcon Blade", "rarity": "Legendary", "cost": 19, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Weapon", "Cost": "19", "Class": "Neutral", "Effect": "Start of battle:  items trigger 30% faster.\nAttacks twice.", "Damage": "5-6 (3.2/s)", "Stamina": "1 (0.6/s)", "Accuracy": "100%", "Cooldown": "1.7s", "Sockets": "2", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0], [1, 1, 1], [1, 1, 1], [1, 1, 1], [0, 1, 0]]}, "Dragonscale Armor": {"name": "Dragonscale Armor", "rarity": "Epic", "cost": 7, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Armor", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Armor", "Cost": "7", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "Battle Rage entered: <PERSON><PERSON> 40 .\nDuring Battle Rage: Damage taken reduced by 10%.", "Sockets": "3", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1], [1, 1]]}, "Stone Helm": {"name": "<PERSON>", "rarity": "Legendary", "cost": 13, "class": "Neutral", "type": "<PERSON><PERSON><PERSON>", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "<PERSON><PERSON><PERSON>", "Cost": "13", "Class": "Neutral", "Effect": "Start of battle: Reduce damage taken by 25% for 5s and gain 35 .\n25% chance to resist critical hits.\n30% chance to resist stuns.", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1]]}, "Hero Longsword": {"name": "<PERSON> Longsword", "rarity": "Legendary", "cost": 19, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Weapon", "Cost": "19", "Class": "Neutral", "Effect": "Start of battle:  Weapons gain 4 damage.", "Damage": "10-12 (6.9/s)", "Stamina": "1 (0.6/s)", "Accuracy": "90%", "Cooldown": "1.6s", "Sockets": "2", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0], [1, 1, 1], [1, 1, 1], [1, 1, 1], [0, 1, 0]]}, "The Fool": {"name": "The Fool", "rarity": "Epic", "cost": 4, "class": "Reaper", "type": "Playing Card", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Playing Card", "Cost": "4", "Class": "Reaper", "Effect": "On reveal: Card are revealed 50% faster. If this is the first card in the chain: Gain 1 .", "In shop": "Deck of Cards needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1]]}, "Amethyst Whelp": {"name": "Amethyst Whelp", "rarity": "<PERSON><PERSON>", "cost": 14, "class": "Pyromancer", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Weapon", "Cost": "14", "Class": "Pyromancer", "Effect": "Start of battle: Inflict 4 random debuffs.\nOn hit: Remove a random buff from your opponent.", "Damage": "5-10 (3.6/s)", "Stamina": "0 (0/s)", "Accuracy": "90%", "Cooldown": "2.1s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1]]}, "Deerwood Guardian": {"name": "Deerwood Guardian", "rarity": "Unique", "cost": 10, "class": "<PERSON><PERSON><PERSON><PERSON>", "type": "Accessory", "description": "Channel your inner deer.", "raw_stats": {"Rarity": "Unique", "Type": "Accessory", "Cost": "10", "Class": "<PERSON><PERSON><PERSON><PERSON>", "Effect": "Damage taken reduced by 10%.\nBattle Rage lasts 0.8s longer for each  -item.\nEvery 1s during Battle Rage: Heal for 8 and gain 3 .", "In shop": "Round 8 subclass item"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 0, 1, 0, 1, 0, 1], [0, 1, 0, 0, 0, 1, 0], [0, 0, 1, 1, 1, 0, 0], [0, 0, 0, 1, 0, 0, 0], [0, 0, 0, 1, 0, 0, 0]]}, "Big Bowl of Treats": {"name": "Big Bowl of Treats", "rarity": "Unique", "cost": 10, "class": "<PERSON>", "type": "Food", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Food", "Cost": "10", "Class": "<PERSON>", "Effect": "Every 3.7s: <PERSON>ain 2 random buffs and make  Food trigger 25% faster (up to 100%).\nAll your Pets have a 20% chance to activate twice.\nFriends of the forest are offered in the shop.\n\nFood: Triggers 10% faster for each  Food of a different type (not Big Bowl of Treats).", "In shop": "Round 8 subclass item", "Adds to shop": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1, 1, 1], [1, 1, 1, 1], [0, 1, 1, 0]]}, "Frozen Flame": {"name": "Frozen Flame", "rarity": "Unique", "cost": 10, "class": "Pyromancer", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Accessory", "Cost": "10", "Class": "Pyromancer", "Effect": "Start of battle: <PERSON><PERSON> 8  for each  -item.\n6  gained: Inflict 2 .\nFor each  of your opponent, the  item has +1.5% critical hit chance and +2% critical damage.\nAdditional -items are offered in the shop.", "In shop": "Round 8 subclass item", "Adds to shop": "Book of Ice, Spell Scroll: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> Dragon"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 0], [1, 1, 1], [1, 1, 1], [1, 1, 1]]}, "Ice Dragon": {"name": "Ice Dragon", "rarity": "<PERSON><PERSON>", "cost": 15, "class": "<PERSON>, Pyromancer", "type": "Weapon, Pet", "description": "", "raw_stats": {"Rarity": "<PERSON><PERSON>", "Type": "Weapon, Pet", "Cost": "15", "Class": "<PERSON>, Pyromancer", "Effect": "On hit: Inflict 1 .\nOpponent reaches 12 : <PERSON><PERSON> 50 . You take -20% -damage.", "Damage": "15-20 (8.3/s)", "Stamina": "0 (0/s)", "Accuracy": "90%", "Cooldown": "2.1s", "Sockets": "2", "In shop": "Frozen Flame needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1], [1, 1]]}, "Fly Agaric": {"name": "Fly Agaric", "rarity": "Rare", "cost": 3, "class": "Reaper", "type": "Food", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Food", "Cost": "3", "Class": "Reaper", "Effect": "Every 4.3s: Inflict 1 .\n\n\nFood: Triggers 10% faster for each  Food of a different type (not Fly Agaric).", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0], [1, 1, 1], [1, 1, 1], [0, 1, 0]]}, "Manathirst": {"name": "<PERSON><PERSON><PERSON><PERSON>", "rarity": "Legendary", "cost": 13, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Weapon", "Cost": "13", "Class": "Neutral", "Effect": "On hit: <PERSON><PERSON> 2 .\n30  gained: Deal 10 -damage with 100% lifesteal. Deal +1 for each .", "Damage": "5-7 (4.3/s)", "Stamina": "0.5 (0.4/s)", "Accuracy": "200%", "Cooldown": "1.4s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1], [1]]}, "Pan": {"name": "Pan", "rarity": "Common", "cost": 4, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Common", "Type": "Weapon", "Cost": "4", "Class": "Neutral", "Effect": "Deals +1 damage for each  Food.", "Damage": "4-5 (2/s)", "Stamina": "2 (0.9/s)", "Accuracy": "85%", "Cooldown": "2.2s", "Sockets": "1", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 0, 1, 0], [0, 1, 1, 1], [1, 1, 1, 1], [0, 1, 1, 0]]}, "Hedgehog": {"name": "Hedgehog", "rarity": "Epic", "cost": 7, "class": "<PERSON>", "type": "Pet", "description": "", "raw_stats": {"Rarity": "Epic", "Type": "Pet", "Cost": "7", "Class": "<PERSON>", "Effect": "Every 5s: Deal 10 -damage + 0.5 for each .\nHealth drops below 70%: Gain 2  and 15  (once).\nTriggers 15% faster for each  Pet or Food.", "In shop": "Big Bowl of Treats needed"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1, 1], [1, 1], [1, 0]]}, "Dancing Dragon": {"name": "Dancing Dragon", "rarity": "Unique", "cost": 9, "class": "Neutral", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Weapon", "Cost": "9", "Class": "Neutral", "Effect": "You have a 2% chance to resist debuffs for each .\nStart of battle:  Gain 2  and 2  for each  -item.\nDeals +0.5 damage per .", "Damage": "15-20 (8/s)", "Stamina": "2 (0.9/s)", "Accuracy": "85%", "Cooldown": "2.2s", "Sockets": "2", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 0, 1, 1, 1, 0], [0, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 0], [0, 1, 1, 1, 0, 0]]}, "Stone Badge": {"name": "Stone Badge", "rarity": "Unique", "cost": 5, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Unique", "Type": "Accessory", "Cost": "5", "Class": "Neutral", "Effect": "Your starting class items are no longer offered in the shop (even when this item is in storage).\nShop entered: Generate items worth 1 .\nEvery 3s: Gain 4 .", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1]]}, "Walrus Tusk": {"name": "<PERSON><PERSON><PERSON>", "rarity": "Common", "cost": 4, "class": "Neutral", "type": "Accessory", "description": "", "raw_stats": {"Rarity": "Common", "Type": "Accessory", "Cost": "4", "Class": "Neutral", "Effect": "Start of battle: Gain 1 .", "In shop": "Yes"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1]]}, "Tusk Piercer": {"name": "Tusk Piercer", "rarity": "Legendary", "cost": 11, "class": "<PERSON>", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Legendary", "Type": "Weapon", "Cost": "11", "Class": "<PERSON>", "Effect": "Start of battle: <PERSON><PERSON> 4 .\n Weapon hits: Use 1  to deal +9 damage on the next attack.", "Damage": "9-12 (3.5/s)", "Stamina": "1.2 (0.4/s)", "Accuracy": "85%", "Cooldown": "3s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[0, 1, 0, 0], [1, 1, 1, 1], [0, 1, 0, 0]]}, "Tusk Poker": {"name": "Tusk Poker", "rarity": "Rare", "cost": 8, "class": "<PERSON>", "type": "Weapon", "description": "", "raw_stats": {"Rarity": "Rare", "Type": "Weapon", "Cost": "8", "Class": "<PERSON>", "Effect": "On hit: 50% chance to gain 1 .", "Damage": "2-3 (1.5/s)", "Stamina": "0.7 (0.4/s)", "Accuracy": "85%", "Cooldown": "1.7s", "Sockets": "1", "In shop": "No"}, "synergy_triggers": [], "synergy_effects": [], "shape": [[1], [1]]}}