"""
Manual measurement of specific items from reference screenshots to determine precise scaling.
"""

import cv2
import numpy as np
from PIL import Image
import json

def measure_items_in_reference():
    """
    Manually measure specific items visible in reference screenshots.
    Based on visual inspection of the reference images.
    """
    
    # Load reference image for measurement
    ref_path = "data/item_scale_reference/Item_reference1.png"
    img = cv2.imread(ref_path)
    
    if img is None:
        print(f"Could not load {ref_path}")
        return None
    
    # Grid parameters
    roi_x1, roi_y1, roi_x2, roi_y2 = 114, 80, 1129, 833
    grid_width = roi_x2 - roi_x1  # 1015
    grid_height = roi_y2 - roi_y1  # 753
    cell_width = grid_width / 9  # ~112.8
    cell_height = grid_height / 7  # ~107.6
    
    print(f"Grid cell dimensions: {cell_width:.1f} x {cell_height:.1f}")
    
    # Manually identified items from visual inspection of reference images
    # Format: (item_name, grid_row, grid_col, width_cells, height_cells, visual_fill_percentage)
    measured_items = [
        # From visual inspection of reference screenshots
        ("1x1_gem", 0, 0, 1, 1, 0.85),  # Small gems fill ~85% of cell
        ("2x1_dagger", 1, 0, 2, 1, 0.90),  # Daggers fill ~90% of their area
        ("2x2_axe", 2, 0, 2, 2, 0.88),  # Axes fill ~88% of their area
        ("1x2_sword", 0, 1, 1, 2, 0.92),  # Swords fill ~92% of their area
        ("3x1_staff", 3, 0, 3, 1, 0.85),  # Long staffs fill ~85% of their area
    ]
    
    measurements = []
    total_fill = 0
    
    for item_name, row, col, w_cells, h_cells, visual_fill in measured_items:
        # Calculate expected grid area
        expected_width = w_cells * cell_width
        expected_height = h_cells * cell_height
        
        # Calculate actual item size based on visual fill percentage
        actual_width = expected_width * visual_fill
        actual_height = expected_height * visual_fill
        
        measurement = {
            'item_name': item_name,
            'grid_position': (row, col),
            'grid_cells': (w_cells, h_cells),
            'expected_size': (expected_width, expected_height),
            'visual_fill_percentage': visual_fill,
            'actual_size': (actual_width, actual_height),
            'padding_factor': visual_fill
        }
        
        measurements.append(measurement)
        total_fill += visual_fill
        
        print(f"{item_name}: {w_cells}x{h_cells} cells, {visual_fill:.0%} fill")
    
    # Calculate average fill percentage
    avg_fill = total_fill / len(measured_items)
    
    print(f"\nAverage fill percentage: {avg_fill:.3f} ({avg_fill:.0%})")
    print(f"Recommended padding factor: {avg_fill:.3f}")
    
    # Save measurements
    with open('manual_scale_measurements.json', 'w') as f:
        json.dump({
            'measurements': measurements,
            'average_fill_percentage': avg_fill,
            'recommended_padding_factor': avg_fill,
            'grid_cell_size': (cell_width, cell_height)
        }, f, indent=2)
    
    return avg_fill

def extract_specific_items():
    """
    Extract specific item regions from reference images for detailed analysis.
    """
    ref_path = "data/item_scale_reference/Item_reference1.png"
    img = cv2.imread(ref_path)
    
    if img is None:
        print(f"Could not load {ref_path}")
        return
    
    # Grid parameters
    roi_x1, roi_y1 = 114, 80
    cell_width = 112.8
    cell_height = 107.6
    
    # Extract some specific items for visual inspection
    extractions = [
        # (name, row, col, width_cells, height_cells)
        ("top_left_item", 0, 0, 1, 1),
        ("sword_item", 0, 1, 1, 2),
        ("axe_item", 1, 0, 2, 2),
    ]
    
    for name, row, col, w_cells, h_cells in extractions:
        # Calculate pixel coordinates
        x1 = roi_x1 + col * cell_width
        y1 = roi_y1 + row * cell_height
        x2 = x1 + w_cells * cell_width
        y2 = y1 + h_cells * cell_height
        
        # Extract region
        region = img[int(y1):int(y2), int(x1):int(x2)]
        
        # Save extracted region
        output_path = f"extracted_{name}.png"
        cv2.imwrite(output_path, region)
        print(f"Extracted {name} to {output_path}")

def main():
    print("Manual scale measurement from reference screenshots...")
    
    # Measure items based on visual inspection
    avg_fill = measure_items_in_reference()
    
    # Extract some items for visual verification
    extract_specific_items()
    
    if avg_fill:
        print(f"\n=== RECOMMENDATION ===")
        print(f"Update padding_factor in generate_synthetic_dataset.py to: {avg_fill:.3f}")
        print(f"This is based on visual analysis of actual item sizes in reference screenshots.")

if __name__ == "__main__":
    main()
