import cv2
import mss
import numpy as np

# Global variables
drawing = False
start_x, start_y = -1, -1
end_x, end_y = -1, -1
screenshot_np = None
window_name = "Select ROI"

def select_roi(event, x, y, flags, param):
    """Mouse callback function to select a Region of Interest (ROI)."""
    global start_x, start_y, end_x, end_y, drawing, screenshot_np

    if event == cv2.EVENT_LBUTTONDOWN:
        drawing = True
        start_x, start_y = x, y
        end_x, end_y = x, y

    elif event == cv2.EVENT_MOUSEMOVE:
        if drawing:
            end_x, end_y = x, y

    elif event == cv2.EVENT_LBUTTONUP:
        drawing = False
        end_x, end_y = x, y
        
        # Ensure start coordinates are top-left and end coordinates are bottom-right
        final_start_x = min(start_x, end_x)
        final_start_y = min(start_y, end_y)
        final_end_x = max(start_x, end_x)
        final_end_y = max(start_y, end_y)

        roi = {
            'left': final_start_x,
            'top': final_start_y,
            'width': final_end_x - final_start_x,
            'height': final_end_y - final_start_y
        }
        print(f"ROI selected: {roi}")


def main():
    global screenshot_np, window_name

    with mss.mss() as sct:
        # Get information of monitor 1. You might need to change this if you have multiple monitors.
        monitor_number = 1
        mon = sct.monitors[monitor_number]

        # Get a screenshot of the monitor
        sct_img = sct.grab(mon)
        
        # Convert to an array that OpenCV can work with
        screenshot_np = np.array(sct_img)

    # Create a window and set the mouse callback
    cv2.namedWindow(window_name, cv2.WND_PROP_FULLSCREEN)
    cv2.setWindowProperty(window_name, cv2.WND_PROP_FULLSCREEN, cv2.WINDOW_FULLSCREEN)
    cv2.setMouseCallback(window_name, select_roi)

    while True:
        # Make a copy of the original screenshot to draw on
        display_img = screenshot_np.copy()
        
        if drawing:
            # Draw the rectangle on the display image while dragging
            cv2.rectangle(display_img, (start_x, start_y), (end_x, end_y), (0, 255, 0), 2)
        elif start_x != -1: # Draw the final rectangle after selection
            final_start_x = min(start_x, end_x)
            final_start_y = min(start_y, end_y)
            final_end_x = max(start_x, end_x)
            final_end_y = max(start_y, end_y)
            cv2.rectangle(display_img, (final_start_x, final_start_y), (final_end_x, final_end_y), (0, 0, 255), 2)


        cv2.imshow(window_name, display_img)

        # Press 'q' or 'esc' to quit
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q') or key == 27: # 27 is the ansidecimal for esc
            break

    cv2.destroyAllWindows()

if __name__ == "__main__":
    main()