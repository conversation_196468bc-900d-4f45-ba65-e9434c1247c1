import random
import numpy as np
from typing import List, <PERSON><PERSON>, Optional
from copy import deepcopy

from simulator.core import Item, ItemInstance, Backpack, Player, load_all_item_data
from simulator.battle import BattleSimulator
from bbagent.config import BAC<PERSON><PERSON>CK_DIMENSIONS, DB_PATH
# from bbagent.game_manager import GameManager  # Assuming GameManager is importable

class InventoryOptimizer:
    """
    Optimizes inventory layout using a genetic algorithm to maximize battle performance.
    """

    def __init__(self, game_manager: 'GameManager'):
        """
        Initializes the optimizer with a reference to the GameManager.

        Args:
            game_manager: An instance of the GameManager to access game state.
        """
        self.game_manager = game_manager
        self.item_definitions = load_all_item_data(DB_PATH)

    def _create_random_valid_layout(self, items: List[Item]) -> Optional[List[ItemInstance]]:
        """
        Tries to create a single random, valid layout for a given list of items.
        Returns the layout as a list of ItemInstances, or None if it fails.
        """
        backpack = Backpack(width=BACKPACK_DIMENSIONS[0], height=BACKPACK_DIMENSIONS[1])
        layout = []
        
        # Shuffle items to randomize placement order
        random.shuffle(items)
        
        for item_def in items:
            placed = False
            
            # Create a list of all possible valid placements (pos, rot)
            possible_placements = []
            for angle in [0, 1, 2, 3]: # 0, 90, 180, 270 degrees
                temp_instance = ItemInstance(item_def, 0, 0, angle)
                rotated_shape = temp_instance.get_rotated_shape()
                shape_h = len(rotated_shape)
                shape_w = len(rotated_shape[0]) if shape_h > 0 else 0

                for y in range(backpack.height - shape_h + 1):
                    for x in range(backpack.width - shape_w + 1):
                        temp_instance.x = x
                        temp_instance.y = y
                        if backpack.can_place_item(temp_instance):
                            possible_placements.append(((x, y), angle))
            
            # If there's a valid spot, pick one at random and place the item
            if possible_placements:
                (x, y), angle = random.choice(possible_placements)
                instance = ItemInstance(item_def, x, y, angle)
                backpack.place_item(instance)
                layout.append(instance)
                placed = True
        
        # For simplicity, we ensure all items are placed.
        # A more complex implementation could handle partial layouts.
        if len(layout) == len(items):
            return layout
        return None

    def initialize_population(self, population_size: int) -> List[List[ItemInstance]]:
        """
        Creates an initial population of random, valid inventory layouts.
        """
        population = []
        
        # Get item definitions from the current inventory
        current_item_names = [item.name for item in self.game_manager.current_inventory_items]
        items_to_place = [self.item_definitions[name] for name in current_item_names if name in self.item_definitions]
        
        attempts = 0
        while len(population) < population_size and attempts < population_size * 5:
            layout = self._create_random_valid_layout(items_to_place)
            if layout:
                population.append(layout)
            attempts += 1
            
        if not population:
            raise ValueError("Could not generate any valid layouts for the given items.")
            
        return population

    def calculate_fitness(self, layout: List[ItemInstance], simulations: int = 10) -> float:
        """
        Calculates the fitness of a layout by running it through the BattleSimulator.
        The fitness is the win rate against a standard opponent.
        """
        wins = 0
        for _ in range(simulations):
            # Create Player 1 with the specified layout
            player1 = Player(name="Player1", player_class="Reaper")
            for item_instance in layout:
                player1.backpack.place_item(deepcopy(item_instance))

            # Create a standard opponent for benchmarking
            player2 = Player(name="Opponent", player_class="Reaper")
            # TODO: Define a more sophisticated standard opponent
             opponent_item = self.item_definitions.get("Wooden Sword")
            if opponent_item:
                sword_instance = ItemInstance(opponent_item, x=0, y=0)
                player2.backpack.place_item(sword_instance)
            
            # Reset health before each battle
            player1.health = 100
            player2.health = 100

            simulator = BattleSimulator(player1, player2, db_connection=None) # DB connection not used in battle itself
            winner = simulator.run_battle()

            if winner == player1.name:
                wins += 1
        
        return wins / simulations

    def selection(self, population: List[List[ItemInstance]], fitness_scores: List[float], k: int = 3) -> List[List[ItemInstance]]:
        """
        Selects the best layouts from the population using tournament selection.
        """
        selected = []
        for _ in range(len(population)):
            tournament_indices = random.sample(range(len(population)), k)
            tournament_fitness = [(fitness_scores[i], population[i]) for i in tournament_indices]
            # Find the best individual in the tournament
            winner = max(tournament_fitness, key=lambda item: item[0])[1]
            selected.append(winner)
        return selected

    def crossover(self, parent1: List[ItemInstance], parent2: List[ItemInstance]) -> Optional[List[ItemInstance]]:
        """
        Creates a new layout by combining two parent layouts.
        Uses a random crossover point and attempts to create a valid new layout.
        """
        if not parent1 or not parent2:
            return None

        # Combine all unique items from both parents
        all_items_map = {item.item.name: item.item for item in parent1}
        all_items_map.update({item.item.name: item.item for item in parent2})
        
        # Attempt to create a new valid layout from the combined item set
        return self._create_random_valid_layout(list(all_items_map.values()))

    def mutation(self, layout: List[ItemInstance], mutation_rate: float) -> List[ItemInstance]:
        """
        Introduces small, random changes to a layout by attempting to move an item.
        """
        if not layout or random.random() > mutation_rate:
            return layout
        
        mutated_layout = deepcopy(layout)
        
        # Pick a random item to mutate
        item_to_move_idx = random.randint(0, len(mutated_layout) - 1)
        item_to_move = mutated_layout.pop(item_to_move_idx)
        
        # Create a temporary backpack with the other items
        temp_backpack = Backpack()
        for item in mutated_layout:
            temp_backpack.place_item(item)
            
        # Find a new random valid placement for the item
        possible_placements = []
        for angle in [0, 1, 2, 3]:
            temp_instance = ItemInstance(item_to_move.item, 0, 0, angle)
            rotated_shape = temp_instance.get_rotated_shape()
            shape_h = len(rotated_shape)
            shape_w = len(rotated_shape[0]) if shape_h > 0 else 0
            
            for y in range(temp_backpack.height - shape_h + 1):
                for x in range(temp_backpack.width - shape_w + 1):
                    temp_instance.x, temp_instance.y = x, y
                    if temp_backpack.can_place_item(temp_instance):
                        possible_placements.append(((x,y), angle))

        # If a new spot is found, place it and return the new layout
        if possible_placements:
            (x, y), angle = random.choice(possible_placements)
            item_to_move.x, item_to_move.y, item_to_move.rotation = x, y, angle
            mutated_layout.append(item_to_move)
            return mutated_layout
        
        # If mutation fails, return the original layout
        return layout

    def optimize_inventory(self, generations: int, population_size: int) -> Optional[List[ItemInstance]]:
        """
        Orchestrates the genetic algorithm to find the best inventory layout.
        """
        print("Starting inventory optimization...")
        try:
            population = self.initialize_population(population_size)
        except ValueError as e:
            print(f"Error: {e}")
            return None

        best_layout_overall = None
        best_fitness_overall = -1.0

        for gen in range(generations):
            # 1. Calculate fitness for the current population
            fitness_scores = [self.calculate_fitness(layout) for layout in population]
            
            # 2. Track the best layout found so far
            max_fitness_this_gen = max(fitness_scores)
            if max_fitness_this_gen > best_fitness_overall:
                best_fitness_overall = max_fitness_this_gen
                best_layout_overall = deepcopy(population[fitness_scores.index(max_fitness_this_gen)])
                print(f"Gen {gen}: New best layout found with fitness {best_fitness_overall:.2f}")

            # 3. Selection
            parents = self.selection(population, fitness_scores)
            
            # 4. Crossover and Mutation
            next_population = []
            while len(next_population) < population_size:
                parent1, parent2 = random.sample(parents, 2)
                child = self.crossover(parent1, parent2)
                if child:
                    child = self.mutation(child, mutation_rate=0.1)
                    next_population.append(child)
            
            population = next_population
        
        print("Optimization finished.")
        return best_layout_overall