import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk, ImageDraw, ImageEnhance
import json
import os
import cv2
import numpy as np

class AutoPlacementAnalyzer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Auto Item Detection & Approval")
        self.root.geometry("1400x900")
        
        # Data
        self.inventory_image = None
        self.detected_items = []
        self.approved_items = []
        self.current_index = 0
        
        # Load item templates
        self.item_templates = self.load_item_templates()
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface."""
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Control panel (left side)
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        control_frame.configure(width=300)
        
        # File loading
        ttk.Label(control_frame, text="1. Load Inventory Screenshot:", font=('Arial', 10, 'bold')).pack(anchor=tk.W, pady=(0, 5))
        ttk.Button(control_frame, text="📁 Load Screenshot", command=self.load_and_analyze).pack(fill=tk.X, pady=(0, 15))
        
        # Current item info
        self.info_frame = ttk.LabelFrame(control_frame, text="Current Detection", padding=10)
        self.info_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.item_label = ttk.Label(self.info_frame, text="No item selected", font=('Arial', 12, 'bold'))
        self.item_label.pack(anchor=tk.W)
        
        self.size_label = ttk.Label(self.info_frame, text="Size: --")
        self.size_label.pack(anchor=tk.W)
        
        self.confidence_label = ttk.Label(self.info_frame, text="Confidence: --")
        self.confidence_label.pack(anchor=tk.W)
        
        # Item name correction
        ttk.Label(self.info_frame, text="Correct item name:").pack(anchor=tk.W, pady=(10, 0))
        self.name_var = tk.StringVar()
        self.name_combo = ttk.Combobox(self.info_frame, textvariable=self.name_var, width=25)
        self.name_combo['values'] = list(self.item_templates.keys())
        self.name_combo.pack(fill=tk.X, pady=(0, 10))
        
        # Action buttons
        button_frame = ttk.Frame(self.info_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="✅ Approve", command=self.approve_current, 
                  style='Accent.TButton').pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="❌ Reject", command=self.reject_current).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="📝 Update", command=self.update_current).pack(side=tk.LEFT)
        
        # Navigation
        nav_frame = ttk.LabelFrame(control_frame, text="Navigation", padding=10)
        nav_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.progress_label = ttk.Label(nav_frame, text="0 / 0")
        self.progress_label.pack()
        
        nav_buttons = ttk.Frame(nav_frame)
        nav_buttons.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(nav_buttons, text="⬅️ Prev", command=self.prev_item).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(nav_buttons, text="➡️ Next", command=self.next_item).pack(side=tk.LEFT)
        
        # Bulk actions
        bulk_frame = ttk.LabelFrame(control_frame, text="Bulk Actions", padding=10)
        bulk_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Button(bulk_frame, text="🚀 Approve All Remaining", 
                  command=self.approve_all, style='Accent.TButton').pack(fill=tk.X, pady=(0, 5))
        ttk.Button(bulk_frame, text="💾 Save Approved Items", 
                  command=self.save_results).pack(fill=tk.X)
        
        # Results summary
        self.summary_frame = ttk.LabelFrame(control_frame, text="Summary", padding=10)
        self.summary_frame.pack(fill=tk.X)
        
        self.summary_label = ttk.Label(self.summary_frame, text="No items processed yet")
        self.summary_label.pack()
        
        # Canvas (right side)
        canvas_frame = ttk.Frame(main_frame)
        canvas_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        self.canvas = tk.Canvas(canvas_frame, bg='white')
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        h_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)
        self.canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
    def load_item_templates(self):
        """Load item templates for matching."""
        templates = {}
        item_dir = "data/item_images"
        
        if os.path.exists(item_dir):
            for filename in os.listdir(item_dir):
                if filename.endswith('.png'):
                    item_name = filename.replace('.png', '').replace('_', ' ')
                    try:
                        img_path = os.path.join(item_dir, filename)
                        img = cv2.imread(img_path, cv2.IMREAD_UNCHANGED)
                        if img is not None:
                            templates[item_name] = img
                    except Exception as e:
                        print(f"Error loading {filename}: {e}")
        
        print(f"Loaded {len(templates)} item templates")
        return templates
        
    def load_and_analyze(self):
        """Load screenshot and automatically detect items."""
        file_path = filedialog.askopenfilename(
            title="Select Inventory Screenshot",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.bmp *.gif")]
        )
        
        if file_path:
            self.inventory_image = Image.open(file_path)
            print(f"Loaded screenshot: {self.inventory_image.size[0]}x{self.inventory_image.size[1]} pixels")
            
            # Auto-detect items
            self.auto_detect_items()
            
            # Show first item
            if self.detected_items:
                self.current_index = 0
                self.show_current_item()
            else:
                messagebox.showinfo("No Items", "No items were automatically detected. Try a different screenshot.")
    
    def auto_detect_items(self):
        """Automatically detect items in the inventory."""
        print("Auto-detecting items...")
        
        # Convert PIL to CV2
        img_cv = cv2.cvtColor(np.array(self.inventory_image), cv2.COLOR_RGB2BGR)
        
        # Define inventory grid area (you may need to adjust these coordinates)
        # These are rough estimates - we'll detect items anywhere in the image for now
        
        detected = []
        
        # Try to find each item template
        for item_name, template in self.item_templates.items():
            matches = self.find_item_matches(img_cv, template, item_name)
            detected.extend(matches)
        
        # Sort by confidence
        detected.sort(key=lambda x: x['confidence'], reverse=True)
        
        # Remove overlapping detections (keep highest confidence)
        self.detected_items = self.remove_overlaps(detected)
        
        print(f"Detected {len(self.detected_items)} items")
        
    def find_item_matches(self, img, template, item_name):
        """Find matches for a specific item template."""
        matches = []
        
        # Handle transparency if present
        if template.shape[2] == 4:  # Has alpha channel
            template_bgr = template[:, :, :3]
            mask = template[:, :, 3]
        else:
            template_bgr = template
            mask = None
        
        # Try multiple scales
        scales = [0.3, 0.4, 0.5, 0.6, 0.7, 0.8]
        
        for scale in scales:
            # Resize template
            h, w = template_bgr.shape[:2]
            new_h, new_w = int(h * scale), int(w * scale)
            
            if new_h < 20 or new_w < 20:
                continue
                
            scaled_template = cv2.resize(template_bgr, (new_w, new_h))
            scaled_mask = cv2.resize(mask, (new_w, new_h)) if mask is not None else None
            
            # Template matching
            if scaled_mask is not None:
                result = cv2.matchTemplate(img, scaled_template, cv2.TM_CCORR_NORMED, mask=scaled_mask)
            else:
                result = cv2.matchTemplate(img, scaled_template, cv2.TM_CCORR_NORMED)
            
            # Find peaks
            threshold = 0.6  # Adjust as needed
            locations = np.where(result >= threshold)
            
            for pt in zip(*locations[::-1]):
                confidence = result[pt[1], pt[0]]
                
                matches.append({
                    'item': item_name,
                    'bbox': [pt[0], pt[1], pt[0] + new_w, pt[1] + new_h],
                    'confidence': float(confidence),
                    'scale': scale,
                    'width': new_w,
                    'height': new_h
                })
        
        return matches
    
    def remove_overlaps(self, detections):
        """Remove overlapping detections, keeping the highest confidence."""
        if not detections:
            return []
            
        # Sort by confidence (highest first)
        detections.sort(key=lambda x: x['confidence'], reverse=True)
        
        filtered = []
        
        for detection in detections:
            bbox1 = detection['bbox']
            
            # Check if this detection overlaps significantly with any already accepted
            overlaps = False
            for accepted in filtered:
                bbox2 = accepted['bbox']
                
                # Calculate intersection over union (IoU)
                x1 = max(bbox1[0], bbox2[0])
                y1 = max(bbox1[1], bbox2[1])
                x2 = min(bbox1[2], bbox2[2])
                y2 = min(bbox1[3], bbox2[3])
                
                if x1 < x2 and y1 < y2:
                    intersection = (x2 - x1) * (y2 - y1)
                    area1 = (bbox1[2] - bbox1[0]) * (bbox1[3] - bbox1[1])
                    area2 = (bbox2[2] - bbox2[0]) * (bbox2[3] - bbox2[1])
                    union = area1 + area2 - intersection
                    
                    iou = intersection / union if union > 0 else 0
                    
                    if iou > 0.3:  # 30% overlap threshold
                        overlaps = True
                        break
            
            if not overlaps:
                filtered.append(detection)
        
        return filtered
    
    def show_current_item(self):
        """Display the current item for review."""
        if not self.detected_items or self.current_index >= len(self.detected_items):
            return
            
        item = self.detected_items[self.current_index]
        
        # Update info labels
        self.item_label.config(text=f"Item: {item['item']}")
        self.size_label.config(text=f"Size: {item['width']}x{item['height']} pixels")
        self.confidence_label.config(text=f"Confidence: {item['confidence']:.2f}")
        self.progress_label.config(text=f"{self.current_index + 1} / {len(self.detected_items)}")
        
        # Set combo box
        self.name_var.set(item['item'])
        
        # Update display
        self.display_image_with_highlight()
        
        # Update summary
        approved_count = len(self.approved_items)
        total_count = len(self.detected_items)
        self.summary_label.config(text=f"Approved: {approved_count} / {total_count}")
        
    def display_image_with_highlight(self):
        """Display image with current item highlighted."""
        if not self.inventory_image or not self.detected_items:
            return
            
        # Create display image
        display_img = self.inventory_image.copy()
        draw = ImageDraw.Draw(display_img)
        
        # Draw all approved items in green
        for item in self.approved_items:
            bbox = item['bbox']
            draw.rectangle(bbox, outline='green', width=3)
            draw.text((bbox[0], bbox[1]-20), f"✅ {item['item']}", fill='green')
        
        # Draw current item in bright blue
        if self.current_index < len(self.detected_items):
            current = self.detected_items[self.current_index]
            bbox = current['bbox']
            draw.rectangle(bbox, outline='blue', width=4)
            draw.text((bbox[0], bbox[1]-20), f"👀 {current['item']}", fill='blue')
        
        # Draw remaining items in red
        for i, item in enumerate(self.detected_items):
            if i != self.current_index and item not in self.approved_items:
                bbox = item['bbox']
                draw.rectangle(bbox, outline='red', width=2)
                draw.text((bbox[0], bbox[1]-20), f"❓ {item['item']}", fill='red')
        
        # Display
        self.photo = ImageTk.PhotoImage(display_img)
        self.canvas.delete("all")
        self.canvas.create_image(0, 0, anchor=tk.NW, image=self.photo)
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
    
    def approve_current(self):
        """Approve the current item."""
        if self.current_index < len(self.detected_items):
            item = self.detected_items[self.current_index].copy()
            item['item'] = self.name_var.get()  # Use corrected name
            self.approved_items.append(item)
            self.next_item()
    
    def reject_current(self):
        """Reject the current item."""
        self.next_item()
    
    def update_current(self):
        """Update the current item name."""
        if self.current_index < len(self.detected_items):
            self.detected_items[self.current_index]['item'] = self.name_var.get()
            self.show_current_item()
    
    def prev_item(self):
        """Go to previous item."""
        if self.current_index > 0:
            self.current_index -= 1
            self.show_current_item()
    
    def next_item(self):
        """Go to next item."""
        if self.current_index < len(self.detected_items) - 1:
            self.current_index += 1
            self.show_current_item()
        elif self.current_index == len(self.detected_items) - 1:
            # Last item - show completion message
            messagebox.showinfo("Complete!", "All items reviewed! Click 'Save Approved Items' to finish.")
    
    def approve_all(self):
        """Approve all remaining items."""
        for i in range(self.current_index, len(self.detected_items)):
            item = self.detected_items[i].copy()
            if item not in self.approved_items:
                self.approved_items.append(item)
        
        self.show_current_item()
        messagebox.showinfo("Approved", f"Approved {len(self.approved_items)} items total!")
    
    def save_results(self):
        """Save the approved items."""
        if not self.approved_items:
            messagebox.showwarning("Warning", "No approved items to save!")
            return
        
        # Calculate average sizes by item type
        item_sizes = {}
        for item in self.approved_items:
            name = item['item']
            if name not in item_sizes:
                item_sizes[name] = []
            item_sizes[name].append({
                'width': item['width'],
                'height': item['height']
            })
        
        # Create final results
        results = {}
        for name, sizes in item_sizes.items():
            avg_w = sum(s['width'] for s in sizes) / len(sizes)
            avg_h = sum(s['height'] for s in sizes) / len(sizes)
            
            results[name] = {
                'width': round(avg_w),
                'height': round(avg_h),
                'samples': len(sizes)
            }
        
        # Save files
        with open('auto_detected_items.json', 'w') as f:
            json.dump(self.approved_items, f, indent=2)
            
        with open('auto_item_sizes.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        messagebox.showinfo("Saved!", 
                          f"Saved {len(self.approved_items)} items!\n"
                          f"Files: auto_detected_items.json, auto_item_sizes.json")
    
    def run(self):
        """Run the application."""
        self.root.mainloop()

if __name__ == "__main__":
    app = AutoPlacementAnalyzer()
    app.run()
