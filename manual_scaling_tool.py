import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk, ImageDraw
import os
import json
import math

class ManualScalingTool:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Manual Item Scaling Tool")
        self.root.geometry("1400x900")
        
        # Data storage
        self.reference_images = []
        self.item_images = {}
        self.current_ref_index = 0
        self.current_item = None
        self.scaling_data = {}
        
        # UI state
        self.overlay_scale = 1.0
        self.overlay_alpha = 0.5
        self.overlay_x = 100
        self.overlay_y = 100
        self.dragging = False
        self.last_mouse_x = 0
        self.last_mouse_y = 0
        
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """Setup the user interface."""
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Control panel (left side)
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # Reference image selection
        ttk.Label(control_frame, text="Reference Image:").pack(anchor=tk.W)
        self.ref_var = tk.StringVar()
        self.ref_combo = ttk.Combobox(control_frame, textvariable=self.ref_var, width=30)
        self.ref_combo.pack(fill=tk.X, pady=(0, 10))
        self.ref_combo.bind('<<ComboboxSelected>>', self.on_reference_changed)
        
        # Item selection
        ttk.Label(control_frame, text="Item to Scale:").pack(anchor=tk.W)
        self.item_var = tk.StringVar()
        self.item_combo = ttk.Combobox(control_frame, textvariable=self.item_var, width=30)
        self.item_combo.pack(fill=tk.X, pady=(0, 10))
        self.item_combo.bind('<<ComboboxSelected>>', self.on_item_changed)
        
        # Scale controls
        ttk.Label(control_frame, text="Scale:").pack(anchor=tk.W)
        self.scale_var = tk.DoubleVar(value=1.0)
        self.scale_slider = ttk.Scale(control_frame, from_=0.1, to=3.0, 
                                     variable=self.scale_var, orient=tk.HORIZONTAL)
        self.scale_slider.pack(fill=tk.X, pady=(0, 5))
        self.scale_slider.bind('<Motion>', self.on_scale_changed)
        
        self.scale_label = ttk.Label(control_frame, text="Scale: 1.00")
        self.scale_label.pack(anchor=tk.W)
        
        # Alpha controls
        ttk.Label(control_frame, text="Transparency:").pack(anchor=tk.W, pady=(10, 0))
        self.alpha_var = tk.DoubleVar(value=0.5)
        self.alpha_slider = ttk.Scale(control_frame, from_=0.1, to=1.0,
                                     variable=self.alpha_var, orient=tk.HORIZONTAL)
        self.alpha_slider.pack(fill=tk.X, pady=(0, 5))
        self.alpha_slider.bind('<Motion>', self.on_alpha_changed)
        
        # Position info
        self.pos_label = ttk.Label(control_frame, text="Position: (0, 0)")
        self.pos_label.pack(anchor=tk.W, pady=(10, 0))
        
        # Buttons
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Button(button_frame, text="Save Scale", command=self.save_current_scale).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="Reset Position", command=self.reset_position).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="Export Data", command=self.export_scaling_data).pack(fill=tk.X, pady=(0, 5))
        
        # Instructions
        instructions = """
Instructions:
1. Select a reference image
2. Select an item to scale
3. Drag the overlay to position it over the same item in the reference
4. Use the scale slider to match the size
5. Click 'Save Scale' when perfect
6. Repeat for other items
7. Export data when done

Controls:
- Drag overlay to move
- Scale slider to resize
- Transparency to see through overlay
        """
        ttk.Label(control_frame, text=instructions, justify=tk.LEFT, 
                 wraplength=250, font=('Arial', 8)).pack(pady=(20, 0))
        
        # Canvas (right side)
        canvas_frame = ttk.Frame(main_frame)
        canvas_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        self.canvas = tk.Canvas(canvas_frame, bg='white')
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # Bind mouse events
        self.canvas.bind('<Button-1>', self.on_mouse_down)
        self.canvas.bind('<B1-Motion>', self.on_mouse_drag)
        self.canvas.bind('<ButtonRelease-1>', self.on_mouse_up)
        
    def load_data(self):
        """Load reference images and item images."""
        # Load reference images
        ref_dir = "data/item_scale_reference"
        if os.path.exists(ref_dir):
            ref_files = [f for f in os.listdir(ref_dir) if f.endswith('.png')]
            self.reference_images = ref_files
            self.ref_combo['values'] = ref_files
            if ref_files:
                self.ref_combo.set(ref_files[0])
        
        # Load item images
        item_dir = "data/item_images"
        if os.path.exists(item_dir):
            item_files = [f for f in os.listdir(item_dir) if f.endswith('.png')]
            # Focus on key items first
            priority_items = ['Axe.png', 'Dagger.png', 'Hero_Sword.png', 'Banana.png', 
                            'Shield.png', 'Bow.png', 'Sword.png', 'Apple.png']
            
            # Put priority items first, then others
            sorted_items = []
            for item in priority_items:
                if item in item_files:
                    sorted_items.append(item)
            for item in item_files:
                if item not in priority_items:
                    sorted_items.append(item)
            
            self.item_combo['values'] = sorted_items
            if sorted_items:
                self.item_combo.set(sorted_items[0])
        
        # Load existing scaling data if available
        if os.path.exists('manual_scaling_data.json'):
            with open('manual_scaling_data.json', 'r') as f:
                self.scaling_data = json.load(f)
        
        # Initial load
        self.load_reference_image()
        self.load_item_image()
        
    def load_reference_image(self):
        """Load and display the current reference image."""
        if not self.reference_images:
            return
            
        ref_file = self.ref_var.get()
        if not ref_file:
            return
            
        ref_path = os.path.join("data/item_scale_reference", ref_file)
        self.ref_image = Image.open(ref_path)
        
        # Scale down if too large for display
        canvas_width = 1000
        canvas_height = 700
        
        img_ratio = self.ref_image.width / self.ref_image.height
        canvas_ratio = canvas_width / canvas_height
        
        if img_ratio > canvas_ratio:
            # Image is wider
            display_width = canvas_width
            display_height = int(canvas_width / img_ratio)
        else:
            # Image is taller
            display_height = canvas_height
            display_width = int(canvas_height * img_ratio)
        
        self.display_scale = display_width / self.ref_image.width
        self.ref_display = self.ref_image.resize((display_width, display_height), Image.LANCZOS)
        self.ref_photo = ImageTk.PhotoImage(self.ref_display)
        
        self.canvas.delete("all")
        self.canvas.create_image(0, 0, anchor=tk.NW, image=self.ref_photo)
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        
        self.update_overlay()
        
    def load_item_image(self):
        """Load the current item image."""
        item_file = self.item_var.get()
        if not item_file:
            return
            
        item_path = os.path.join("data/item_images", item_file)
        self.item_image = Image.open(item_path).convert('RGBA')
        
        # Load existing scale if available
        item_name = item_file.replace('.png', '')
        if item_name in self.scaling_data:
            self.scale_var.set(self.scaling_data[item_name]['scale'])
            self.overlay_x = self.scaling_data[item_name].get('x', 100)
            self.overlay_y = self.scaling_data[item_name].get('y', 100)
        
        self.update_overlay()
        
    def update_overlay(self):
        """Update the overlay display."""
        if not hasattr(self, 'item_image') or not hasattr(self, 'ref_display'):
            return
            
        # Remove old overlay
        self.canvas.delete("overlay")
        
        # Calculate scaled size
        scale = self.scale_var.get()
        display_scale = scale * self.display_scale
        
        scaled_width = int(self.item_image.width * display_scale)
        scaled_height = int(self.item_image.height * display_scale)
        
        if scaled_width <= 0 or scaled_height <= 0:
            return
            
        # Create scaled item image
        scaled_item = self.item_image.resize((scaled_width, scaled_height), Image.LANCZOS)
        
        # Apply transparency
        alpha = int(self.alpha_var.get() * 255)
        if scaled_item.mode == 'RGBA':
            # Modify alpha channel
            r, g, b, a = scaled_item.split()
            a = a.point(lambda x: min(x, alpha))
            scaled_item = Image.merge('RGBA', (r, g, b, a))
        else:
            # Add alpha channel
            scaled_item.putalpha(alpha)
        
        # Convert to PhotoImage
        self.overlay_photo = ImageTk.PhotoImage(scaled_item)
        
        # Display overlay
        display_x = int(self.overlay_x * self.display_scale)
        display_y = int(self.overlay_y * self.display_scale)
        
        self.canvas.create_image(display_x, display_y, anchor=tk.NW, 
                               image=self.overlay_photo, tags="overlay")
        
        # Update labels
        self.scale_label.config(text=f"Scale: {scale:.2f}")
        self.pos_label.config(text=f"Position: ({self.overlay_x}, {self.overlay_y})")
        
    def on_reference_changed(self, event=None):
        """Handle reference image change."""
        self.load_reference_image()
        
    def on_item_changed(self, event=None):
        """Handle item change."""
        self.load_item_image()
        
    def on_scale_changed(self, event=None):
        """Handle scale change."""
        self.update_overlay()
        
    def on_alpha_changed(self, event=None):
        """Handle alpha change."""
        self.update_overlay()
        
    def on_mouse_down(self, event):
        """Handle mouse down for dragging."""
        self.dragging = True
        self.last_mouse_x = event.x
        self.last_mouse_y = event.y
        
    def on_mouse_drag(self, event):
        """Handle mouse drag to move overlay."""
        if self.dragging:
            dx = event.x - self.last_mouse_x
            dy = event.y - self.last_mouse_y
            
            # Convert to original image coordinates
            self.overlay_x += dx / self.display_scale
            self.overlay_y += dy / self.display_scale
            
            self.last_mouse_x = event.x
            self.last_mouse_y = event.y
            
            self.update_overlay()
            
    def on_mouse_up(self, event):
        """Handle mouse up."""
        self.dragging = False
        
    def reset_position(self):
        """Reset overlay position to center."""
        self.overlay_x = 100
        self.overlay_y = 100
        self.update_overlay()
        
    def save_current_scale(self):
        """Save the current scale settings."""
        item_file = self.item_var.get()
        if not item_file:
            messagebox.showwarning("Warning", "No item selected!")
            return
            
        item_name = item_file.replace('.png', '')
        
        # Calculate actual pixel dimensions
        scale = self.scale_var.get()
        actual_width = int(self.item_image.width * scale)
        actual_height = int(self.item_image.height * scale)
        
        self.scaling_data[item_name] = {
            'scale': scale,
            'width': actual_width,
            'height': actual_height,
            'x': self.overlay_x,
            'y': self.overlay_y,
            'reference_image': self.ref_var.get()
        }
        
        messagebox.showinfo("Saved", f"Saved scaling for {item_name}:\n"
                                   f"Scale: {scale:.3f}\n"
                                   f"Size: {actual_width}x{actual_height} pixels")
        
    def export_scaling_data(self):
        """Export all scaling data to JSON."""
        if not self.scaling_data:
            messagebox.showwarning("Warning", "No scaling data to export!")
            return
            
        # Save to file
        with open('manual_scaling_data.json', 'w') as f:
            json.dump(self.scaling_data, f, indent=2)
            
        # Also create a simplified lookup for the main script
        simple_lookup = {}
        for item_name, data in self.scaling_data.items():
            simple_lookup[item_name] = {
                'width': data['width'],
                'height': data['height']
            }
            
        with open('manual_item_sizes.json', 'w') as f:
            json.dump(simple_lookup, f, indent=2)
            
        messagebox.showinfo("Exported", f"Exported scaling data for {len(self.scaling_data)} items to:\n"
                                      f"- manual_scaling_data.json (detailed)\n"
                                      f"- manual_item_sizes.json (for main script)")
        
    def run(self):
        """Run the application."""
        self.root.mainloop()

if __name__ == "__main__":
    app = ManualScalingTool()
    app.run()
