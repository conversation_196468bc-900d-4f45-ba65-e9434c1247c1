from typing import Dict
from simulator.core import GameState, Player, ItemInstance, Item

# These are placeholder values and will likely need to be adjusted based on the
# actual screen layout from the vision system's perspective.
# These values should define the region of interest for the backpack grid.
BACKPACK_ROI_X_START = 0  # Top-left X coordinate of the backpack area
BACKPACK_ROI_Y_START = 0  # Top-left Y coordinate of the backpack area
BACKPACK_COLS = 9
BACKPACK_ROWS = 7
# These pixel dimensions should correspond to the full 9x7 grid area
BACKPACK_GRID_WIDTH_PX = 630
BACKPACK_GRID_HEIGHT_PX = 490

# Calculate the size of a single grid cell in pixels
CELL_WIDTH = BACKPACK_GRID_WIDTH_PX / BACKPACK_COLS
CELL_HEIGHT = BACKPACK_GRID_HEIGHT_PX / BACKPACK_ROWS


def translate_to_gamestate(vision_output: Dict, all_items: Dict[str, Item]) -> GameState:
    """
    Converts the raw vision system output into a structured GameState object.

    Args:
        vision_output: The dictionary from the vision system, containing 'backpack'
                       and 'shop' items with their names and pixel positions.
        all_items: A dictionary mapping item names to their Item data objects,
                   as loaded by simulator.core.load_all_item_data().

    Returns:
        A GameState object representing the current state of the game.
    """
    player = Player()

    if 'backpack' in vision_output:
        for vision_item in vision_output['backpack']:
            item_name = vision_item.get('name')
            pixel_position = vision_item.get('position')

            if not item_name or not pixel_position:
                continue

            item_data = all_items.get(item_name)

            if item_data:
                # Convert pixel position to grid coordinates.
                # Assumes the pixel_position is relative to the top-left of the screen or ROI.
                relative_x = pixel_position[0] - BACKPACK_ROI_X_START
                relative_y = pixel_position[1] - BACKPACK_ROI_Y_START

                grid_x = int(relative_x / CELL_WIDTH)
                grid_y = int(relative_y / CELL_HEIGHT)

                # Clamp coordinates to be within the backpack grid bounds.
                grid_x = max(0, min(grid_x, BACKPACK_COLS - 1))
                grid_y = max(0, min(grid_y, BACKPACK_ROWS - 1))

                # Create an ItemInstance and add it to the player's backpack.
                # Rotation is assumed to be 0 by default.
                item_instance = ItemInstance(item=item_data, x=grid_x, y=grid_y, rotation=0)
                player.backpack.add_item(item_instance)
            else:
                # This can be noisy, but useful for debugging vision or item data mismatches.
                print(f"Warning: Item '{item_name}' from vision output not found in all_items.")

    # The shop and player's gold/stats are ignored for now as per requirements.
    game_state = GameState(player=player)

    return game_state