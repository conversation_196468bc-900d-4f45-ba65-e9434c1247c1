# Vision Pipeline Technical Design

This document outlines the architecture and implementation details for a new object detection pipeline to replace the existing template-matching system.

---

## 1. Model Selection

**Model:** `YOLOv8` (You Only Look Once version 8)

**Justification:**
YOLOv8 is a state-of-the-art, real-time object detection model known for its exceptional balance of speed and accuracy. It is well-suited for this task because:
- **High Accuracy:** It can achieve near-perfect detection rates, which is critical for correctly identifying in-game items.
- **Real-Time Performance:** Its speed ensures that the vision system can process game screenshots without introducing significant lag.
- **Pre-trained Weights:** It comes with pre-trained weights from the COCO dataset, which can be fine-tuned on our custom item dataset. This transfer learning approach will significantly reduce training time and improve performance.

**Required Libraries:** `ultralytics`

---

## 2. Data Format and Structure

The training dataset will be organized as follows to be compatible with the YOLOv8 training pipeline:

```
/data/object_detection/
|-- /images/       (contains all raw screenshot .jpg files)
|-- /labels/       (contains corresponding .txt label files)
|-- classes.txt    (a file listing each item name on a new line)
|-- dataset.yaml   (the configuration file for the dataset)
```

**Label File Format:**
Each `.txt` label file will correspond to a `.jpg` image file and contain one line for each object instance in the image. The format for each line will be the standard YOLO format:

`<class_id> <x_center_norm> <y_center_norm> <width_norm> <height_norm>`

- `<class_id>`: An integer representing the class of the item, starting from 0.
- `<x_center_norm>`: The normalized x-coordinate of the center of the bounding box.
- `<y_center_norm>`: The normalized y-coordinate of the center of the bounding box.
- `<width_norm>`: The normalized width of the bounding box.
- `<height_norm>`: The normalized height of the bounding box.

**`classes.txt`:**
A simple text file where each line is the name of an item. The line number (0-indexed) corresponds to the `<class_id>`.

**`dataset.yaml`:**
This file will configure the training and validation data paths and class names for the YOLOv8 trainer.

```yaml
path: ../data/object_detection
train: images/
val: images/

names:
  0: item_name_1
  1: item_name_2
  ...
```

---

## 3. Data Labeling Workflow

**Recommended Tool:** `LabelImg`

`LabelImg` is a free, open-source graphical image annotation tool that is easy to use and directly supports exporting labels in the required YOLO format.

**Workflow:**
1.  **Gather Data:** Collect a diverse set of in-game screenshots containing various items.
2.  **Load Images:** Open the folder of screenshots in `LabelImg`.
3.  **Draw Bounding Boxes:** For each item in an image, draw a rectangle (bounding box) around it.
4.  **Assign Labels:** Assign the appropriate class label to each bounding box from the `classes.txt` list.
5.  **Save and Export:** `LabelImg` will automatically save a `.txt` file for each image in the correct YOLO format.

---

## 4. Training Pipeline (`train_vision_model.py`)

A new Python script will be created to handle the model training process.

**Responsibilities:**
-   **Load Dataset:** Load the dataset configuration from `dataset.yaml`.
-   **Initialize Model:** Load the pre-trained `YOLOv8` model.
-   **Train Model:** Start the training process with configurable hyperparameters (e.g., epochs, batch size, learning rate).
-   **Save Model:** The training process will automatically save the best-performing model weights to a `models/` directory (e.g., `best.pt`).

**Example Code Structure:**
```python
from ultralytics import YOLO

# Load a pre-trained YOLOv8 model
model = YOLO('yolov8n.pt')

# Train the model
results = model.train(data='data/object_detection/dataset.yaml', epochs=100, imgsz=640)

# The best model is automatically saved as 'runs/detect/train/weights/best.pt'
# We will move this to 'models/best.pt'
```

---

## 5. Inference Script (`new_item_recognizer.py`)

This script will replace the current `item_recognizer.py` and will be responsible for real-time item detection.

**Responsibilities:**
-   **Load Model:** Load the fine-tuned `best.pt` model weights.
-   **Process Image:** Take a game screenshot as input.
-   **Run Inference:** Run the model on the screenshot to get detections.
-   **Format Output:** Convert the raw model output into the same list of dictionaries format as the old system: `{'item_name': str, 'position': (x, y, w, h), 'confidence': float}`.

**Example Code Structure:**
```python
from ultralytics import YOLO

class NewItemRecognizer:
    def __init__(self, model_path='models/best.pt'):
        self.model = YOLO(model_path)

    def find_all_items(self, screenshot_image):
        results = self.model(screenshot_image)
        detections = []
        for result in results:
            for box in result.boxes:
                x1, y1, x2, y2 = box.xyxy[0]
                detections.append({
                    'item_name': self.model.names[int(box.cls)],
                    'position': (int(x1), int(y1), int(x2 - x1), int(y2 - y1)),
                    'confidence': float(box.conf)
                })
        return detections