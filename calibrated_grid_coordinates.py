# Manually calibrated grid coordinates based on actual game screenshots
# Each coordinate represents one grid cell in the 9x7 inventory
# Robust for both training dataset generation and in-game use

# Calibration parameters
INVENTORY_ROI = (114, 80, 1129, 833)
GRID_OFFSET_LEFT = 74
GRID_OFFSET_TOP = 20
GRID_OFFSET_RIGHT = 20
GRID_OFFSET_BOTTOM = 20
GRID_COLS = 9
GRID_ROWS = 7

CALIBRATED_GRID_COORDS = [
    {'row': 0, 'col': 0, 'local_x1': 74, 'local_y1': 20, 'local_x2': 176, 'local_y2': 121, 'abs_x1': 188, 'abs_y1': 100, 'abs_x2': 290, 'abs_y2': 201, 'center_x': 239, 'center_y': 150, 'width': 102, 'height': 101},
    {'row': 0, 'col': 1, 'local_x1': 176, 'local_y1': 20, 'local_x2': 278, 'local_y2': 121, 'abs_x1': 290, 'abs_y1': 100, 'abs_x2': 392, 'abs_y2': 201, 'center_x': 341, 'center_y': 150, 'width': 102, 'height': 101},
    {'row': 0, 'col': 2, 'local_x1': 278, 'local_y1': 20, 'local_x2': 380, 'local_y2': 121, 'abs_x1': 392, 'abs_y1': 100, 'abs_x2': 494, 'abs_y2': 201, 'center_x': 443, 'center_y': 150, 'width': 102, 'height': 101},
    {'row': 0, 'col': 3, 'local_x1': 381, 'local_y1': 20, 'local_x2': 483, 'local_y2': 121, 'abs_x1': 495, 'abs_y1': 100, 'abs_x2': 597, 'abs_y2': 201, 'center_x': 546, 'center_y': 150, 'width': 102, 'height': 101},
    {'row': 0, 'col': 4, 'local_x1': 483, 'local_y1': 20, 'local_x2': 585, 'local_y2': 121, 'abs_x1': 597, 'abs_y1': 100, 'abs_x2': 699, 'abs_y2': 201, 'center_x': 648, 'center_y': 150, 'width': 102, 'height': 101},
    {'row': 0, 'col': 5, 'local_x1': 585, 'local_y1': 20, 'local_x2': 688, 'local_y2': 121, 'abs_x1': 699, 'abs_y1': 100, 'abs_x2': 802, 'abs_y2': 201, 'center_x': 750, 'center_y': 150, 'width': 102, 'height': 101},
    {'row': 0, 'col': 6, 'local_x1': 688, 'local_y1': 20, 'local_x2': 790, 'local_y2': 121, 'abs_x1': 802, 'abs_y1': 100, 'abs_x2': 904, 'abs_y2': 201, 'center_x': 853, 'center_y': 150, 'width': 102, 'height': 101},
    {'row': 0, 'col': 7, 'local_x1': 790, 'local_y1': 20, 'local_x2': 892, 'local_y2': 121, 'abs_x1': 904, 'abs_y1': 100, 'abs_x2': 1006, 'abs_y2': 201, 'center_x': 955, 'center_y': 150, 'width': 102, 'height': 101},
    {'row': 0, 'col': 8, 'local_x1': 892, 'local_y1': 20, 'local_x2': 995, 'local_y2': 121, 'abs_x1': 1006, 'abs_y1': 100, 'abs_x2': 1109, 'abs_y2': 201, 'center_x': 1057, 'center_y': 150, 'width': 102, 'height': 101},
    {'row': 1, 'col': 0, 'local_x1': 74, 'local_y1': 121, 'local_x2': 176, 'local_y2': 223, 'abs_x1': 188, 'abs_y1': 201, 'abs_x2': 290, 'abs_y2': 303, 'center_x': 239, 'center_y': 252, 'width': 102, 'height': 101},
    {'row': 1, 'col': 1, 'local_x1': 176, 'local_y1': 121, 'local_x2': 278, 'local_y2': 223, 'abs_x1': 290, 'abs_y1': 201, 'abs_x2': 392, 'abs_y2': 303, 'center_x': 341, 'center_y': 252, 'width': 102, 'height': 101},
    {'row': 1, 'col': 2, 'local_x1': 278, 'local_y1': 121, 'local_x2': 380, 'local_y2': 223, 'abs_x1': 392, 'abs_y1': 201, 'abs_x2': 494, 'abs_y2': 303, 'center_x': 443, 'center_y': 252, 'width': 102, 'height': 101},
    {'row': 1, 'col': 3, 'local_x1': 381, 'local_y1': 121, 'local_x2': 483, 'local_y2': 223, 'abs_x1': 495, 'abs_y1': 201, 'abs_x2': 597, 'abs_y2': 303, 'center_x': 546, 'center_y': 252, 'width': 102, 'height': 101},
    {'row': 1, 'col': 4, 'local_x1': 483, 'local_y1': 121, 'local_x2': 585, 'local_y2': 223, 'abs_x1': 597, 'abs_y1': 201, 'abs_x2': 699, 'abs_y2': 303, 'center_x': 648, 'center_y': 252, 'width': 102, 'height': 101},
    {'row': 1, 'col': 5, 'local_x1': 585, 'local_y1': 121, 'local_x2': 688, 'local_y2': 223, 'abs_x1': 699, 'abs_y1': 201, 'abs_x2': 802, 'abs_y2': 303, 'center_x': 750, 'center_y': 252, 'width': 102, 'height': 101},
    {'row': 1, 'col': 6, 'local_x1': 688, 'local_y1': 121, 'local_x2': 790, 'local_y2': 223, 'abs_x1': 802, 'abs_y1': 201, 'abs_x2': 904, 'abs_y2': 303, 'center_x': 853, 'center_y': 252, 'width': 102, 'height': 101},
    {'row': 1, 'col': 7, 'local_x1': 790, 'local_y1': 121, 'local_x2': 892, 'local_y2': 223, 'abs_x1': 904, 'abs_y1': 201, 'abs_x2': 1006, 'abs_y2': 303, 'center_x': 955, 'center_y': 252, 'width': 102, 'height': 101},
    {'row': 1, 'col': 8, 'local_x1': 892, 'local_y1': 121, 'local_x2': 995, 'local_y2': 223, 'abs_x1': 1006, 'abs_y1': 201, 'abs_x2': 1109, 'abs_y2': 303, 'center_x': 1057, 'center_y': 252, 'width': 102, 'height': 101},
    {'row': 2, 'col': 0, 'local_x1': 74, 'local_y1': 223, 'local_x2': 176, 'local_y2': 325, 'abs_x1': 188, 'abs_y1': 303, 'abs_x2': 290, 'abs_y2': 405, 'center_x': 239, 'center_y': 354, 'width': 102, 'height': 101},
    {'row': 2, 'col': 1, 'local_x1': 176, 'local_y1': 223, 'local_x2': 278, 'local_y2': 325, 'abs_x1': 290, 'abs_y1': 303, 'abs_x2': 392, 'abs_y2': 405, 'center_x': 341, 'center_y': 354, 'width': 102, 'height': 101},
    {'row': 2, 'col': 2, 'local_x1': 278, 'local_y1': 223, 'local_x2': 380, 'local_y2': 325, 'abs_x1': 392, 'abs_y1': 303, 'abs_x2': 494, 'abs_y2': 405, 'center_x': 443, 'center_y': 354, 'width': 102, 'height': 101},
    {'row': 2, 'col': 3, 'local_x1': 381, 'local_y1': 223, 'local_x2': 483, 'local_y2': 325, 'abs_x1': 495, 'abs_y1': 303, 'abs_x2': 597, 'abs_y2': 405, 'center_x': 546, 'center_y': 354, 'width': 102, 'height': 101},
    {'row': 2, 'col': 4, 'local_x1': 483, 'local_y1': 223, 'local_x2': 585, 'local_y2': 325, 'abs_x1': 597, 'abs_y1': 303, 'abs_x2': 699, 'abs_y2': 405, 'center_x': 648, 'center_y': 354, 'width': 102, 'height': 101},
    {'row': 2, 'col': 5, 'local_x1': 585, 'local_y1': 223, 'local_x2': 688, 'local_y2': 325, 'abs_x1': 699, 'abs_y1': 303, 'abs_x2': 802, 'abs_y2': 405, 'center_x': 750, 'center_y': 354, 'width': 102, 'height': 101},
    {'row': 2, 'col': 6, 'local_x1': 688, 'local_y1': 223, 'local_x2': 790, 'local_y2': 325, 'abs_x1': 802, 'abs_y1': 303, 'abs_x2': 904, 'abs_y2': 405, 'center_x': 853, 'center_y': 354, 'width': 102, 'height': 101},
    {'row': 2, 'col': 7, 'local_x1': 790, 'local_y1': 223, 'local_x2': 892, 'local_y2': 325, 'abs_x1': 904, 'abs_y1': 303, 'abs_x2': 1006, 'abs_y2': 405, 'center_x': 955, 'center_y': 354, 'width': 102, 'height': 101},
    {'row': 2, 'col': 8, 'local_x1': 892, 'local_y1': 223, 'local_x2': 995, 'local_y2': 325, 'abs_x1': 1006, 'abs_y1': 303, 'abs_x2': 1109, 'abs_y2': 405, 'center_x': 1057, 'center_y': 354, 'width': 102, 'height': 101},
    {'row': 3, 'col': 0, 'local_x1': 74, 'local_y1': 325, 'local_x2': 176, 'local_y2': 427, 'abs_x1': 188, 'abs_y1': 405, 'abs_x2': 290, 'abs_y2': 507, 'center_x': 239, 'center_y': 456, 'width': 102, 'height': 101},
    {'row': 3, 'col': 1, 'local_x1': 176, 'local_y1': 325, 'local_x2': 278, 'local_y2': 427, 'abs_x1': 290, 'abs_y1': 405, 'abs_x2': 392, 'abs_y2': 507, 'center_x': 341, 'center_y': 456, 'width': 102, 'height': 101},
    {'row': 3, 'col': 2, 'local_x1': 278, 'local_y1': 325, 'local_x2': 380, 'local_y2': 427, 'abs_x1': 392, 'abs_y1': 405, 'abs_x2': 494, 'abs_y2': 507, 'center_x': 443, 'center_y': 456, 'width': 102, 'height': 101},
    {'row': 3, 'col': 3, 'local_x1': 381, 'local_y1': 325, 'local_x2': 483, 'local_y2': 427, 'abs_x1': 495, 'abs_y1': 405, 'abs_x2': 597, 'abs_y2': 507, 'center_x': 546, 'center_y': 456, 'width': 102, 'height': 101},
    {'row': 3, 'col': 4, 'local_x1': 483, 'local_y1': 325, 'local_x2': 585, 'local_y2': 427, 'abs_x1': 597, 'abs_y1': 405, 'abs_x2': 699, 'abs_y2': 507, 'center_x': 648, 'center_y': 456, 'width': 102, 'height': 101},
    {'row': 3, 'col': 5, 'local_x1': 585, 'local_y1': 325, 'local_x2': 688, 'local_y2': 427, 'abs_x1': 699, 'abs_y1': 405, 'abs_x2': 802, 'abs_y2': 507, 'center_x': 750, 'center_y': 456, 'width': 102, 'height': 101},
    {'row': 3, 'col': 6, 'local_x1': 688, 'local_y1': 325, 'local_x2': 790, 'local_y2': 427, 'abs_x1': 802, 'abs_y1': 405, 'abs_x2': 904, 'abs_y2': 507, 'center_x': 853, 'center_y': 456, 'width': 102, 'height': 101},
    {'row': 3, 'col': 7, 'local_x1': 790, 'local_y1': 325, 'local_x2': 892, 'local_y2': 427, 'abs_x1': 904, 'abs_y1': 405, 'abs_x2': 1006, 'abs_y2': 507, 'center_x': 955, 'center_y': 456, 'width': 102, 'height': 101},
    {'row': 3, 'col': 8, 'local_x1': 892, 'local_y1': 325, 'local_x2': 995, 'local_y2': 427, 'abs_x1': 1006, 'abs_y1': 405, 'abs_x2': 1109, 'abs_y2': 507, 'center_x': 1057, 'center_y': 456, 'width': 102, 'height': 101},
    {'row': 4, 'col': 0, 'local_x1': 74, 'local_y1': 427, 'local_x2': 176, 'local_y2': 529, 'abs_x1': 188, 'abs_y1': 507, 'abs_x2': 290, 'abs_y2': 609, 'center_x': 239, 'center_y': 558, 'width': 102, 'height': 101},
    {'row': 4, 'col': 1, 'local_x1': 176, 'local_y1': 427, 'local_x2': 278, 'local_y2': 529, 'abs_x1': 290, 'abs_y1': 507, 'abs_x2': 392, 'abs_y2': 609, 'center_x': 341, 'center_y': 558, 'width': 102, 'height': 101},
    {'row': 4, 'col': 2, 'local_x1': 278, 'local_y1': 427, 'local_x2': 380, 'local_y2': 529, 'abs_x1': 392, 'abs_y1': 507, 'abs_x2': 494, 'abs_y2': 609, 'center_x': 443, 'center_y': 558, 'width': 102, 'height': 101},
    {'row': 4, 'col': 3, 'local_x1': 381, 'local_y1': 427, 'local_x2': 483, 'local_y2': 529, 'abs_x1': 495, 'abs_y1': 507, 'abs_x2': 597, 'abs_y2': 609, 'center_x': 546, 'center_y': 558, 'width': 102, 'height': 101},
    {'row': 4, 'col': 4, 'local_x1': 483, 'local_y1': 427, 'local_x2': 585, 'local_y2': 529, 'abs_x1': 597, 'abs_y1': 507, 'abs_x2': 699, 'abs_y2': 609, 'center_x': 648, 'center_y': 558, 'width': 102, 'height': 101},
    {'row': 4, 'col': 5, 'local_x1': 585, 'local_y1': 427, 'local_x2': 688, 'local_y2': 529, 'abs_x1': 699, 'abs_y1': 507, 'abs_x2': 802, 'abs_y2': 609, 'center_x': 750, 'center_y': 558, 'width': 102, 'height': 101},
    {'row': 4, 'col': 6, 'local_x1': 688, 'local_y1': 427, 'local_x2': 790, 'local_y2': 529, 'abs_x1': 802, 'abs_y1': 507, 'abs_x2': 904, 'abs_y2': 609, 'center_x': 853, 'center_y': 558, 'width': 102, 'height': 101},
    {'row': 4, 'col': 7, 'local_x1': 790, 'local_y1': 427, 'local_x2': 892, 'local_y2': 529, 'abs_x1': 904, 'abs_y1': 507, 'abs_x2': 1006, 'abs_y2': 609, 'center_x': 955, 'center_y': 558, 'width': 102, 'height': 101},
    {'row': 4, 'col': 8, 'local_x1': 892, 'local_y1': 427, 'local_x2': 995, 'local_y2': 529, 'abs_x1': 1006, 'abs_y1': 507, 'abs_x2': 1109, 'abs_y2': 609, 'center_x': 1057, 'center_y': 558, 'width': 102, 'height': 101},
    {'row': 5, 'col': 0, 'local_x1': 74, 'local_y1': 529, 'local_x2': 176, 'local_y2': 631, 'abs_x1': 188, 'abs_y1': 609, 'abs_x2': 290, 'abs_y2': 711, 'center_x': 239, 'center_y': 660, 'width': 102, 'height': 101},
    {'row': 5, 'col': 1, 'local_x1': 176, 'local_y1': 529, 'local_x2': 278, 'local_y2': 631, 'abs_x1': 290, 'abs_y1': 609, 'abs_x2': 392, 'abs_y2': 711, 'center_x': 341, 'center_y': 660, 'width': 102, 'height': 101},
    {'row': 5, 'col': 2, 'local_x1': 278, 'local_y1': 529, 'local_x2': 380, 'local_y2': 631, 'abs_x1': 392, 'abs_y1': 609, 'abs_x2': 494, 'abs_y2': 711, 'center_x': 443, 'center_y': 660, 'width': 102, 'height': 101},
    {'row': 5, 'col': 3, 'local_x1': 381, 'local_y1': 529, 'local_x2': 483, 'local_y2': 631, 'abs_x1': 495, 'abs_y1': 609, 'abs_x2': 597, 'abs_y2': 711, 'center_x': 546, 'center_y': 660, 'width': 102, 'height': 101},
    {'row': 5, 'col': 4, 'local_x1': 483, 'local_y1': 529, 'local_x2': 585, 'local_y2': 631, 'abs_x1': 597, 'abs_y1': 609, 'abs_x2': 699, 'abs_y2': 711, 'center_x': 648, 'center_y': 660, 'width': 102, 'height': 101},
    {'row': 5, 'col': 5, 'local_x1': 585, 'local_y1': 529, 'local_x2': 688, 'local_y2': 631, 'abs_x1': 699, 'abs_y1': 609, 'abs_x2': 802, 'abs_y2': 711, 'center_x': 750, 'center_y': 660, 'width': 102, 'height': 101},
    {'row': 5, 'col': 6, 'local_x1': 688, 'local_y1': 529, 'local_x2': 790, 'local_y2': 631, 'abs_x1': 802, 'abs_y1': 609, 'abs_x2': 904, 'abs_y2': 711, 'center_x': 853, 'center_y': 660, 'width': 102, 'height': 101},
    {'row': 5, 'col': 7, 'local_x1': 790, 'local_y1': 529, 'local_x2': 892, 'local_y2': 631, 'abs_x1': 904, 'abs_y1': 609, 'abs_x2': 1006, 'abs_y2': 711, 'center_x': 955, 'center_y': 660, 'width': 102, 'height': 101},
    {'row': 5, 'col': 8, 'local_x1': 892, 'local_y1': 529, 'local_x2': 995, 'local_y2': 631, 'abs_x1': 1006, 'abs_y1': 609, 'abs_x2': 1109, 'abs_y2': 711, 'center_x': 1057, 'center_y': 660, 'width': 102, 'height': 101},
    {'row': 6, 'col': 0, 'local_x1': 74, 'local_y1': 631, 'local_x2': 176, 'local_y2': 733, 'abs_x1': 188, 'abs_y1': 711, 'abs_x2': 290, 'abs_y2': 813, 'center_x': 239, 'center_y': 762, 'width': 102, 'height': 101},
    {'row': 6, 'col': 1, 'local_x1': 176, 'local_y1': 631, 'local_x2': 278, 'local_y2': 733, 'abs_x1': 290, 'abs_y1': 711, 'abs_x2': 392, 'abs_y2': 813, 'center_x': 341, 'center_y': 762, 'width': 102, 'height': 101},
    {'row': 6, 'col': 2, 'local_x1': 278, 'local_y1': 631, 'local_x2': 380, 'local_y2': 733, 'abs_x1': 392, 'abs_y1': 711, 'abs_x2': 494, 'abs_y2': 813, 'center_x': 443, 'center_y': 762, 'width': 102, 'height': 101},
    {'row': 6, 'col': 3, 'local_x1': 381, 'local_y1': 631, 'local_x2': 483, 'local_y2': 733, 'abs_x1': 495, 'abs_y1': 711, 'abs_x2': 597, 'abs_y2': 813, 'center_x': 546, 'center_y': 762, 'width': 102, 'height': 101},
    {'row': 6, 'col': 4, 'local_x1': 483, 'local_y1': 631, 'local_x2': 585, 'local_y2': 733, 'abs_x1': 597, 'abs_y1': 711, 'abs_x2': 699, 'abs_y2': 813, 'center_x': 648, 'center_y': 762, 'width': 102, 'height': 101},
    {'row': 6, 'col': 5, 'local_x1': 585, 'local_y1': 631, 'local_x2': 688, 'local_y2': 733, 'abs_x1': 699, 'abs_y1': 711, 'abs_x2': 802, 'abs_y2': 813, 'center_x': 750, 'center_y': 762, 'width': 102, 'height': 101},
    {'row': 6, 'col': 6, 'local_x1': 688, 'local_y1': 631, 'local_x2': 790, 'local_y2': 733, 'abs_x1': 802, 'abs_y1': 711, 'abs_x2': 904, 'abs_y2': 813, 'center_x': 853, 'center_y': 762, 'width': 102, 'height': 101},
    {'row': 6, 'col': 7, 'local_x1': 790, 'local_y1': 631, 'local_x2': 892, 'local_y2': 733, 'abs_x1': 904, 'abs_y1': 711, 'abs_x2': 1006, 'abs_y2': 813, 'center_x': 955, 'center_y': 762, 'width': 102, 'height': 101},
    {'row': 6, 'col': 8, 'local_x1': 892, 'local_y1': 631, 'local_x2': 995, 'local_y2': 733, 'abs_x1': 1006, 'abs_y1': 711, 'abs_x2': 1109, 'abs_y2': 813, 'center_x': 1057, 'center_y': 762, 'width': 102, 'height': 101},
]

def get_calibrated_cell(row, col):
    """Get calibrated coordinates for a specific grid cell (0-indexed)."""
    if not (0 <= row < GRID_ROWS and 0 <= col < GRID_COLS):
        return None
    for cell in CALIBRATED_GRID_COORDS:
        if cell['row'] == row and cell['col'] == col:
            return cell
    return None

def get_cell_center(row, col):
    """Get the center coordinates of a grid cell."""
    cell = get_calibrated_cell(row, col)
    if cell:
        return (cell['center_x'], cell['center_y'])
    return None

def get_item_placement_coords(row, col, item_width, item_height):
    """Get coordinates for placing an item of given size at grid position."""
    if not can_place_item(row, col, item_width, item_height):
        return None
    
    top_left_cell = get_calibrated_cell(row, col)
    bottom_right_cell = get_calibrated_cell(row + item_height - 1, col + item_width - 1)
    
    if not (top_left_cell and bottom_right_cell):
        return None
    
    return {
        'x1': top_left_cell['abs_x1'],
        'y1': top_left_cell['abs_y1'],
        'x2': bottom_right_cell['abs_x2'],
        'y2': bottom_right_cell['abs_y2'],
        'center_x': (top_left_cell['abs_x1'] + bottom_right_cell['abs_x2']) // 2,
        'center_y': (top_left_cell['abs_y1'] + bottom_right_cell['abs_y2']) // 2,
        'width': bottom_right_cell['abs_x2'] - top_left_cell['abs_x1'],
        'height': bottom_right_cell['abs_y2'] - top_left_cell['abs_y1']
    }

def can_place_item(row, col, item_width, item_height):
    """Check if an item of given size can be placed at grid position."""
    return (0 <= row < GRID_ROWS and 
            0 <= col < GRID_COLS and 
            row + item_height <= GRID_ROWS and 
            col + item_width <= GRID_COLS)

def recalibrate_grid(new_offset_left=None, new_offset_top=None):
    """Recalibrate grid with new offsets if needed for fine-tuning."""
    # This function allows runtime adjustment without regenerating the whole file
    # Implementation would update the global coordinates
    pass
