# Manually calibrated grid coordinates based on actual game screenshots
# Each coordinate represents one grid cell in the 9x7 inventory

CALIBRATED_GRID_COORDS = [
    {'row': 0, 'col': 0, 'local_x1': 20, 'local_y1': 20, 'local_x2': 128, 'local_y2': 121, 'abs_x1': 134, 'abs_y1': 100, 'abs_x2': 242, 'abs_y2': 201, 'center_x': 188, 'center_y': 150, 'width': 108, 'height': 101},
    {'row': 0, 'col': 1, 'local_x1': 128, 'local_y1': 20, 'local_x2': 236, 'local_y2': 121, 'abs_x1': 242, 'abs_y1': 100, 'abs_x2': 350, 'abs_y2': 201, 'center_x': 296, 'center_y': 150, 'width': 108, 'height': 101},
    {'row': 0, 'col': 2, 'local_x1': 236, 'local_y1': 20, 'local_x2': 345, 'local_y2': 121, 'abs_x1': 350, 'abs_y1': 100, 'abs_x2': 459, 'abs_y2': 201, 'center_x': 404, 'center_y': 150, 'width': 108, 'height': 101},
    {'row': 0, 'col': 3, 'local_x1': 345, 'local_y1': 20, 'local_x2': 453, 'local_y2': 121, 'abs_x1': 459, 'abs_y1': 100, 'abs_x2': 567, 'abs_y2': 201, 'center_x': 513, 'center_y': 150, 'width': 108, 'height': 101},
    {'row': 0, 'col': 4, 'local_x1': 453, 'local_y1': 20, 'local_x2': 561, 'local_y2': 121, 'abs_x1': 567, 'abs_y1': 100, 'abs_x2': 675, 'abs_y2': 201, 'center_x': 621, 'center_y': 150, 'width': 108, 'height': 101},
    {'row': 0, 'col': 5, 'local_x1': 561, 'local_y1': 20, 'local_x2': 670, 'local_y2': 121, 'abs_x1': 675, 'abs_y1': 100, 'abs_x2': 784, 'abs_y2': 201, 'center_x': 729, 'center_y': 150, 'width': 108, 'height': 101},
    {'row': 0, 'col': 6, 'local_x1': 670, 'local_y1': 20, 'local_x2': 778, 'local_y2': 121, 'abs_x1': 784, 'abs_y1': 100, 'abs_x2': 892, 'abs_y2': 201, 'center_x': 838, 'center_y': 150, 'width': 108, 'height': 101},
    {'row': 0, 'col': 7, 'local_x1': 778, 'local_y1': 20, 'local_x2': 886, 'local_y2': 121, 'abs_x1': 892, 'abs_y1': 100, 'abs_x2': 1000, 'abs_y2': 201, 'center_x': 946, 'center_y': 150, 'width': 108, 'height': 101},
    {'row': 0, 'col': 8, 'local_x1': 886, 'local_y1': 20, 'local_x2': 995, 'local_y2': 121, 'abs_x1': 1000, 'abs_y1': 100, 'abs_x2': 1109, 'abs_y2': 201, 'center_x': 1054, 'center_y': 150, 'width': 108, 'height': 101},
    {'row': 1, 'col': 0, 'local_x1': 20, 'local_y1': 121, 'local_x2': 128, 'local_y2': 223, 'abs_x1': 134, 'abs_y1': 201, 'abs_x2': 242, 'abs_y2': 303, 'center_x': 188, 'center_y': 252, 'width': 108, 'height': 101},
    {'row': 1, 'col': 1, 'local_x1': 128, 'local_y1': 121, 'local_x2': 236, 'local_y2': 223, 'abs_x1': 242, 'abs_y1': 201, 'abs_x2': 350, 'abs_y2': 303, 'center_x': 296, 'center_y': 252, 'width': 108, 'height': 101},
    {'row': 1, 'col': 2, 'local_x1': 236, 'local_y1': 121, 'local_x2': 345, 'local_y2': 223, 'abs_x1': 350, 'abs_y1': 201, 'abs_x2': 459, 'abs_y2': 303, 'center_x': 404, 'center_y': 252, 'width': 108, 'height': 101},
    {'row': 1, 'col': 3, 'local_x1': 345, 'local_y1': 121, 'local_x2': 453, 'local_y2': 223, 'abs_x1': 459, 'abs_y1': 201, 'abs_x2': 567, 'abs_y2': 303, 'center_x': 513, 'center_y': 252, 'width': 108, 'height': 101},
    {'row': 1, 'col': 4, 'local_x1': 453, 'local_y1': 121, 'local_x2': 561, 'local_y2': 223, 'abs_x1': 567, 'abs_y1': 201, 'abs_x2': 675, 'abs_y2': 303, 'center_x': 621, 'center_y': 252, 'width': 108, 'height': 101},
    {'row': 1, 'col': 5, 'local_x1': 561, 'local_y1': 121, 'local_x2': 670, 'local_y2': 223, 'abs_x1': 675, 'abs_y1': 201, 'abs_x2': 784, 'abs_y2': 303, 'center_x': 729, 'center_y': 252, 'width': 108, 'height': 101},
    {'row': 1, 'col': 6, 'local_x1': 670, 'local_y1': 121, 'local_x2': 778, 'local_y2': 223, 'abs_x1': 784, 'abs_y1': 201, 'abs_x2': 892, 'abs_y2': 303, 'center_x': 838, 'center_y': 252, 'width': 108, 'height': 101},
    {'row': 1, 'col': 7, 'local_x1': 778, 'local_y1': 121, 'local_x2': 886, 'local_y2': 223, 'abs_x1': 892, 'abs_y1': 201, 'abs_x2': 1000, 'abs_y2': 303, 'center_x': 946, 'center_y': 252, 'width': 108, 'height': 101},
    {'row': 1, 'col': 8, 'local_x1': 886, 'local_y1': 121, 'local_x2': 995, 'local_y2': 223, 'abs_x1': 1000, 'abs_y1': 201, 'abs_x2': 1109, 'abs_y2': 303, 'center_x': 1054, 'center_y': 252, 'width': 108, 'height': 101},
    {'row': 2, 'col': 0, 'local_x1': 20, 'local_y1': 223, 'local_x2': 128, 'local_y2': 325, 'abs_x1': 134, 'abs_y1': 303, 'abs_x2': 242, 'abs_y2': 405, 'center_x': 188, 'center_y': 354, 'width': 108, 'height': 101},
    {'row': 2, 'col': 1, 'local_x1': 128, 'local_y1': 223, 'local_x2': 236, 'local_y2': 325, 'abs_x1': 242, 'abs_y1': 303, 'abs_x2': 350, 'abs_y2': 405, 'center_x': 296, 'center_y': 354, 'width': 108, 'height': 101},
    {'row': 2, 'col': 2, 'local_x1': 236, 'local_y1': 223, 'local_x2': 345, 'local_y2': 325, 'abs_x1': 350, 'abs_y1': 303, 'abs_x2': 459, 'abs_y2': 405, 'center_x': 404, 'center_y': 354, 'width': 108, 'height': 101},
    {'row': 2, 'col': 3, 'local_x1': 345, 'local_y1': 223, 'local_x2': 453, 'local_y2': 325, 'abs_x1': 459, 'abs_y1': 303, 'abs_x2': 567, 'abs_y2': 405, 'center_x': 513, 'center_y': 354, 'width': 108, 'height': 101},
    {'row': 2, 'col': 4, 'local_x1': 453, 'local_y1': 223, 'local_x2': 561, 'local_y2': 325, 'abs_x1': 567, 'abs_y1': 303, 'abs_x2': 675, 'abs_y2': 405, 'center_x': 621, 'center_y': 354, 'width': 108, 'height': 101},
    {'row': 2, 'col': 5, 'local_x1': 561, 'local_y1': 223, 'local_x2': 670, 'local_y2': 325, 'abs_x1': 675, 'abs_y1': 303, 'abs_x2': 784, 'abs_y2': 405, 'center_x': 729, 'center_y': 354, 'width': 108, 'height': 101},
    {'row': 2, 'col': 6, 'local_x1': 670, 'local_y1': 223, 'local_x2': 778, 'local_y2': 325, 'abs_x1': 784, 'abs_y1': 303, 'abs_x2': 892, 'abs_y2': 405, 'center_x': 838, 'center_y': 354, 'width': 108, 'height': 101},
    {'row': 2, 'col': 7, 'local_x1': 778, 'local_y1': 223, 'local_x2': 886, 'local_y2': 325, 'abs_x1': 892, 'abs_y1': 303, 'abs_x2': 1000, 'abs_y2': 405, 'center_x': 946, 'center_y': 354, 'width': 108, 'height': 101},
    {'row': 2, 'col': 8, 'local_x1': 886, 'local_y1': 223, 'local_x2': 995, 'local_y2': 325, 'abs_x1': 1000, 'abs_y1': 303, 'abs_x2': 1109, 'abs_y2': 405, 'center_x': 1054, 'center_y': 354, 'width': 108, 'height': 101},
    {'row': 3, 'col': 0, 'local_x1': 20, 'local_y1': 325, 'local_x2': 128, 'local_y2': 427, 'abs_x1': 134, 'abs_y1': 405, 'abs_x2': 242, 'abs_y2': 507, 'center_x': 188, 'center_y': 456, 'width': 108, 'height': 101},
    {'row': 3, 'col': 1, 'local_x1': 128, 'local_y1': 325, 'local_x2': 236, 'local_y2': 427, 'abs_x1': 242, 'abs_y1': 405, 'abs_x2': 350, 'abs_y2': 507, 'center_x': 296, 'center_y': 456, 'width': 108, 'height': 101},
    {'row': 3, 'col': 2, 'local_x1': 236, 'local_y1': 325, 'local_x2': 345, 'local_y2': 427, 'abs_x1': 350, 'abs_y1': 405, 'abs_x2': 459, 'abs_y2': 507, 'center_x': 404, 'center_y': 456, 'width': 108, 'height': 101},
    {'row': 3, 'col': 3, 'local_x1': 345, 'local_y1': 325, 'local_x2': 453, 'local_y2': 427, 'abs_x1': 459, 'abs_y1': 405, 'abs_x2': 567, 'abs_y2': 507, 'center_x': 513, 'center_y': 456, 'width': 108, 'height': 101},
    {'row': 3, 'col': 4, 'local_x1': 453, 'local_y1': 325, 'local_x2': 561, 'local_y2': 427, 'abs_x1': 567, 'abs_y1': 405, 'abs_x2': 675, 'abs_y2': 507, 'center_x': 621, 'center_y': 456, 'width': 108, 'height': 101},
    {'row': 3, 'col': 5, 'local_x1': 561, 'local_y1': 325, 'local_x2': 670, 'local_y2': 427, 'abs_x1': 675, 'abs_y1': 405, 'abs_x2': 784, 'abs_y2': 507, 'center_x': 729, 'center_y': 456, 'width': 108, 'height': 101},
    {'row': 3, 'col': 6, 'local_x1': 670, 'local_y1': 325, 'local_x2': 778, 'local_y2': 427, 'abs_x1': 784, 'abs_y1': 405, 'abs_x2': 892, 'abs_y2': 507, 'center_x': 838, 'center_y': 456, 'width': 108, 'height': 101},
    {'row': 3, 'col': 7, 'local_x1': 778, 'local_y1': 325, 'local_x2': 886, 'local_y2': 427, 'abs_x1': 892, 'abs_y1': 405, 'abs_x2': 1000, 'abs_y2': 507, 'center_x': 946, 'center_y': 456, 'width': 108, 'height': 101},
    {'row': 3, 'col': 8, 'local_x1': 886, 'local_y1': 325, 'local_x2': 995, 'local_y2': 427, 'abs_x1': 1000, 'abs_y1': 405, 'abs_x2': 1109, 'abs_y2': 507, 'center_x': 1054, 'center_y': 456, 'width': 108, 'height': 101},
    {'row': 4, 'col': 0, 'local_x1': 20, 'local_y1': 427, 'local_x2': 128, 'local_y2': 529, 'abs_x1': 134, 'abs_y1': 507, 'abs_x2': 242, 'abs_y2': 609, 'center_x': 188, 'center_y': 558, 'width': 108, 'height': 101},
    {'row': 4, 'col': 1, 'local_x1': 128, 'local_y1': 427, 'local_x2': 236, 'local_y2': 529, 'abs_x1': 242, 'abs_y1': 507, 'abs_x2': 350, 'abs_y2': 609, 'center_x': 296, 'center_y': 558, 'width': 108, 'height': 101},
    {'row': 4, 'col': 2, 'local_x1': 236, 'local_y1': 427, 'local_x2': 345, 'local_y2': 529, 'abs_x1': 350, 'abs_y1': 507, 'abs_x2': 459, 'abs_y2': 609, 'center_x': 404, 'center_y': 558, 'width': 108, 'height': 101},
    {'row': 4, 'col': 3, 'local_x1': 345, 'local_y1': 427, 'local_x2': 453, 'local_y2': 529, 'abs_x1': 459, 'abs_y1': 507, 'abs_x2': 567, 'abs_y2': 609, 'center_x': 513, 'center_y': 558, 'width': 108, 'height': 101},
    {'row': 4, 'col': 4, 'local_x1': 453, 'local_y1': 427, 'local_x2': 561, 'local_y2': 529, 'abs_x1': 567, 'abs_y1': 507, 'abs_x2': 675, 'abs_y2': 609, 'center_x': 621, 'center_y': 558, 'width': 108, 'height': 101},
    {'row': 4, 'col': 5, 'local_x1': 561, 'local_y1': 427, 'local_x2': 670, 'local_y2': 529, 'abs_x1': 675, 'abs_y1': 507, 'abs_x2': 784, 'abs_y2': 609, 'center_x': 729, 'center_y': 558, 'width': 108, 'height': 101},
    {'row': 4, 'col': 6, 'local_x1': 670, 'local_y1': 427, 'local_x2': 778, 'local_y2': 529, 'abs_x1': 784, 'abs_y1': 507, 'abs_x2': 892, 'abs_y2': 609, 'center_x': 838, 'center_y': 558, 'width': 108, 'height': 101},
    {'row': 4, 'col': 7, 'local_x1': 778, 'local_y1': 427, 'local_x2': 886, 'local_y2': 529, 'abs_x1': 892, 'abs_y1': 507, 'abs_x2': 1000, 'abs_y2': 609, 'center_x': 946, 'center_y': 558, 'width': 108, 'height': 101},
    {'row': 4, 'col': 8, 'local_x1': 886, 'local_y1': 427, 'local_x2': 995, 'local_y2': 529, 'abs_x1': 1000, 'abs_y1': 507, 'abs_x2': 1109, 'abs_y2': 609, 'center_x': 1054, 'center_y': 558, 'width': 108, 'height': 101},
    {'row': 5, 'col': 0, 'local_x1': 20, 'local_y1': 529, 'local_x2': 128, 'local_y2': 631, 'abs_x1': 134, 'abs_y1': 609, 'abs_x2': 242, 'abs_y2': 711, 'center_x': 188, 'center_y': 660, 'width': 108, 'height': 101},
    {'row': 5, 'col': 1, 'local_x1': 128, 'local_y1': 529, 'local_x2': 236, 'local_y2': 631, 'abs_x1': 242, 'abs_y1': 609, 'abs_x2': 350, 'abs_y2': 711, 'center_x': 296, 'center_y': 660, 'width': 108, 'height': 101},
    {'row': 5, 'col': 2, 'local_x1': 236, 'local_y1': 529, 'local_x2': 345, 'local_y2': 631, 'abs_x1': 350, 'abs_y1': 609, 'abs_x2': 459, 'abs_y2': 711, 'center_x': 404, 'center_y': 660, 'width': 108, 'height': 101},
    {'row': 5, 'col': 3, 'local_x1': 345, 'local_y1': 529, 'local_x2': 453, 'local_y2': 631, 'abs_x1': 459, 'abs_y1': 609, 'abs_x2': 567, 'abs_y2': 711, 'center_x': 513, 'center_y': 660, 'width': 108, 'height': 101},
    {'row': 5, 'col': 4, 'local_x1': 453, 'local_y1': 529, 'local_x2': 561, 'local_y2': 631, 'abs_x1': 567, 'abs_y1': 609, 'abs_x2': 675, 'abs_y2': 711, 'center_x': 621, 'center_y': 660, 'width': 108, 'height': 101},
    {'row': 5, 'col': 5, 'local_x1': 561, 'local_y1': 529, 'local_x2': 670, 'local_y2': 631, 'abs_x1': 675, 'abs_y1': 609, 'abs_x2': 784, 'abs_y2': 711, 'center_x': 729, 'center_y': 660, 'width': 108, 'height': 101},
    {'row': 5, 'col': 6, 'local_x1': 670, 'local_y1': 529, 'local_x2': 778, 'local_y2': 631, 'abs_x1': 784, 'abs_y1': 609, 'abs_x2': 892, 'abs_y2': 711, 'center_x': 838, 'center_y': 660, 'width': 108, 'height': 101},
    {'row': 5, 'col': 7, 'local_x1': 778, 'local_y1': 529, 'local_x2': 886, 'local_y2': 631, 'abs_x1': 892, 'abs_y1': 609, 'abs_x2': 1000, 'abs_y2': 711, 'center_x': 946, 'center_y': 660, 'width': 108, 'height': 101},
    {'row': 5, 'col': 8, 'local_x1': 886, 'local_y1': 529, 'local_x2': 995, 'local_y2': 631, 'abs_x1': 1000, 'abs_y1': 609, 'abs_x2': 1109, 'abs_y2': 711, 'center_x': 1054, 'center_y': 660, 'width': 108, 'height': 101},
    {'row': 6, 'col': 0, 'local_x1': 20, 'local_y1': 631, 'local_x2': 128, 'local_y2': 733, 'abs_x1': 134, 'abs_y1': 711, 'abs_x2': 242, 'abs_y2': 813, 'center_x': 188, 'center_y': 762, 'width': 108, 'height': 101},
    {'row': 6, 'col': 1, 'local_x1': 128, 'local_y1': 631, 'local_x2': 236, 'local_y2': 733, 'abs_x1': 242, 'abs_y1': 711, 'abs_x2': 350, 'abs_y2': 813, 'center_x': 296, 'center_y': 762, 'width': 108, 'height': 101},
    {'row': 6, 'col': 2, 'local_x1': 236, 'local_y1': 631, 'local_x2': 345, 'local_y2': 733, 'abs_x1': 350, 'abs_y1': 711, 'abs_x2': 459, 'abs_y2': 813, 'center_x': 404, 'center_y': 762, 'width': 108, 'height': 101},
    {'row': 6, 'col': 3, 'local_x1': 345, 'local_y1': 631, 'local_x2': 453, 'local_y2': 733, 'abs_x1': 459, 'abs_y1': 711, 'abs_x2': 567, 'abs_y2': 813, 'center_x': 513, 'center_y': 762, 'width': 108, 'height': 101},
    {'row': 6, 'col': 4, 'local_x1': 453, 'local_y1': 631, 'local_x2': 561, 'local_y2': 733, 'abs_x1': 567, 'abs_y1': 711, 'abs_x2': 675, 'abs_y2': 813, 'center_x': 621, 'center_y': 762, 'width': 108, 'height': 101},
    {'row': 6, 'col': 5, 'local_x1': 561, 'local_y1': 631, 'local_x2': 670, 'local_y2': 733, 'abs_x1': 675, 'abs_y1': 711, 'abs_x2': 784, 'abs_y2': 813, 'center_x': 729, 'center_y': 762, 'width': 108, 'height': 101},
    {'row': 6, 'col': 6, 'local_x1': 670, 'local_y1': 631, 'local_x2': 778, 'local_y2': 733, 'abs_x1': 784, 'abs_y1': 711, 'abs_x2': 892, 'abs_y2': 813, 'center_x': 838, 'center_y': 762, 'width': 108, 'height': 101},
    {'row': 6, 'col': 7, 'local_x1': 778, 'local_y1': 631, 'local_x2': 886, 'local_y2': 733, 'abs_x1': 892, 'abs_y1': 711, 'abs_x2': 1000, 'abs_y2': 813, 'center_x': 946, 'center_y': 762, 'width': 108, 'height': 101},
    {'row': 6, 'col': 8, 'local_x1': 886, 'local_y1': 631, 'local_x2': 995, 'local_y2': 733, 'abs_x1': 1000, 'abs_y1': 711, 'abs_x2': 1109, 'abs_y2': 813, 'center_x': 1054, 'center_y': 762, 'width': 108, 'height': 101},
]

def get_calibrated_cell(row, col):
    """Get calibrated coordinates for a specific grid cell (0-indexed)."""
    for cell in CALIBRATED_GRID_COORDS:
        if cell['row'] == row and cell['col'] == col:
            return cell
    return None

def get_cell_center(row, col):
    """Get the center coordinates of a grid cell."""
    cell = get_calibrated_cell(row, col)
    if cell:
        return (cell['center_x'], cell['center_y'])
    return None
