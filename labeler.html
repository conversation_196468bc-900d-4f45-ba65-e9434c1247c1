<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Action Labeler</title>
    <style>
        body { font-family: sans-serif; display: flex; justify-content: center; padding-top: 20px; }
        .container { width: 90%; max-width: 1200px; }
        .states { display: flex; justify-content: space-between; gap: 20px; }
        .state { border: 1px solid #ccc; padding: 15px; width: 48%; }
        h2 { text-align: center; }
        pre { background-color: #f4f4f4; padding: 10px; border-radius: 5px; white-space: pre-wrap; }
        .action-form { margin-top: 20px; text-align: center; }
        input[type="text"] { width: 70%; padding: 8px; }
        button { padding: 8px 15px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Action Labeler</h1>
        <div id="labeling-section">
            <div class="states">
                <div class="state" id="state1">
                    <h2>State 1 (Before Action)</h2>
                    <pre id="state1-stats"></pre>
                    <h3>Backpack</h3>
                    <ul id="state1-backpack"></ul>
                    <h3>Shop</h3>
                    <ul id="state1-shop"></ul>
                </div>
                <div class="state" id="state2">
                    <h2>State 2 (After Action)</h2>
                    <pre id="state2-stats"></pre>
                    <h3>Backpack</h3>
                    <ul id="state2-backpack"></ul>
                    <h3>Shop</h3>
                    <ul id="state2-shop"></ul>
                </div>
            </div>
            <div class="action-form">
                <input type="text" id="action-input" placeholder="Describe the action (e.g., 'buy sword', 'end turn')...">
                <button onclick="saveAction()">Save Action & Next</button>
                <button onclick="skip()">Skip</button>
            </div>
        </div>
        <div id="completion-message" style="display:none; text-align:center;">
            <h2>Labeling Complete!</h2>
            <p>You have successfully labeled all the data.</p>
        </div>
    </div>

    <script>
        let currentRecordId = null;

        async function fetchNextPair() {
            const response = await fetch('/next-pair');
            if (response.status === 404) {
                document.getElementById('labeling-section').style.display = 'none';
                document.getElementById('completion-message').style.display = 'block';
                return;
            }
            const data = await response.json();
            currentRecordId = data.state1.id;
            displayStates(data);
        }

        function displayStates({ state1, state2 }) {
            // State 1
            document.getElementById('state1-stats').textContent = JSON.stringify(state1.game_state.stats, null, 2);
            const bp1 = document.getElementById('state1-backpack');
            bp1.innerHTML = '';
            state1.game_state.backpack.forEach(item => {
                const li = document.createElement('li');
                li.textContent = item.item_name;
                bp1.appendChild(li);
            });
            const shop1 = document.getElementById('state1-shop');
            shop1.innerHTML = '';
            state1.game_state.shop.forEach(item => {
                const li = document.createElement('li');
                li.textContent = item.item_name;
                shop1.appendChild(li);
            });

            // State 2
            document.getElementById('state2-stats').textContent = JSON.stringify(state2.game_state.stats, null, 2);
            const bp2 = document.getElementById('state2-backpack');
            bp2.innerHTML = '';
            state2.game_state.backpack.forEach(item => {
                const li = document.createElement('li');
                li.textContent = item.item_name;
                bp2.appendChild(li);
            });
            const shop2 = document.getElementById('state2-shop');
            shop2.innerHTML = '';
            state2.game_state.shop.forEach(item => {
                const li = document.createElement('li');
                li.textContent = item.item_name;
                shop2.appendChild(li);
            });
            
            document.getElementById('action-input').value = '';
            document.getElementById('action-input').focus();
        }

        async function saveAction() {
            const action = document.getElementById('action-input').value;
            if (!action || !currentRecordId) return;

            await fetch('/save-action', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ id: currentRecordId, action: action })
            });
            fetchNextPair();
        }

        function skip() {
            fetchNextPair();
        }
        
        document.getElementById('action-input').addEventListener('keydown', (event) => {
            if (event.key === 'Enter') {
                saveAction();
            }
        });

        window.onload = fetchNextPair;
    </script>
</body>
</html>