import sqlite3

def check_database_names():
    conn = sqlite3.connect('GameData.db')
    cursor = conn.cursor()

    # Check table structure
    cursor.execute("PRAGMA table_info(Items)")
    columns = cursor.fetchall()
    print("Database columns:")
    for col in columns:
        print(f"  {col[1]} ({col[2]})")

    print("\n" + "="*50)

    # Get sample items with their names and shapes
    cursor.execute('SELECT * FROM Items LIMIT 5')
    rows = cursor.fetchall()

    print("Sample items from database:")
    for row in rows:
        print(f'Row: {row}')

    print("\n" + "="*50)

    # Count items with and without shapes
    cursor.execute('SELECT COUNT(*) FROM Items WHERE Shape IS NOT NULL AND Shape != ""')
    items_with_shape = cursor.fetchone()[0]

    cursor.execute('SELECT COUNT(*) FROM Items')
    total_items = cursor.fetchone()[0]

    print(f"Items with shape: {items_with_shape}")
    print(f"Total items: {total_items}")
    print(f"Items without shape: {total_items - items_with_shape}")

    # Check specific items
    print("\n" + "="*50)
    cursor.execute('SELECT Name, Shape FROM Items WHERE Name IN ("Hero Sword", "Axe", "Dagger") LIMIT 10')
    specific_items = cursor.fetchall()
    print("Specific items:")
    for item in specific_items:
        print(f'Name: "{item[0]}" | Shape: "{item[1]}"')

    conn.close()

if __name__ == "__main__":
    check_database_names()
