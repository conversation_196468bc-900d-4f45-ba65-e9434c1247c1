import json
import os
from pprint import pprint
from collections import Counter
import numpy as np

DATA_DIR = 'cloning_data'

class NumpyEncoder(json.JSONEncoder):
    """ Custom encoder for numpy data types """
    def default(self, obj):
        if isinstance(obj, (np.int_, np.intc, np.intp, np.int8,
                            np.int16, np.int32, np.int64, np.uint8,
                            np.uint16, np.uint32, np.uint64)):
            return int(obj)
        elif isinstance(obj, (np.float_, np.float16, np.float32,
                              np.float64)):
            return float(obj)
        elif isinstance(obj, (np.ndarray,)):
            return obj.tolist()
        return json.JSONEncoder.default(self, obj)

def get_sorted_data_files():
    """Gets all JSON data files, sorted by timestamp."""
    files = [os.path.join(DATA_DIR, f) for f in os.listdir(DATA_DIR) if f.endswith('.json')]
    records = []
    for file_path in files:
        with open(file_path, 'r') as f:
            try:
                records.append(json.load(f))
            except json.JSONDecodeError:
                print(f"Warning: Skipping corrupted file: {file_path}")
                continue
    records.sort(key=lambda x: x['timestamp'])
    return records

def infer_action(before_state, after_state, recipes):
    """
    Infers the action taken between two game states by comparing item counts,
    positions, and shop contents.
    """
    before_items = before_state['game_state']['backpack']
    after_items = after_state['game_state']['backpack']
    before_shop = before_state['game_state']['shop']

    before_counts = Counter(item['item_name'] for item in before_items)
    after_counts = Counter(item['item_name'] for item in after_items)

    # Helper to find an item's position
    def find_item_position(item_name, items):
        for item in items:
            if item['item_name'] == item_name:
                return item.get('position')
        return None

    # --- 1. Check for SELL action ---
    for item_name, count in before_counts.items():
        if after_counts[item_name] < count:
            return {"action_type": "SELL", "item_name": item_name}

    # --- 2. Check for BUY action ---
    for item_name, count in after_counts.items():
        if count > before_counts[item_name]:
            # Verify if the item was in the shop before
            if any(shop_item['item_name'] == item_name for shop_item in before_shop):
                 return {"action_type": "BUY", "item_name": item_name}

    # --- 3. Check for COMBINE action ---
    removed_items = before_counts - after_counts
    added_items = after_counts - before_counts
    
    if len(added_items) == 1 and len(removed_items) >= 2:
        output_item = list(added_items.keys())[0]
        ingredients = list(removed_items.keys())
        
        for recipe in recipes.get('recipes', []):
            if recipe['result'] == output_item and sorted(recipe['ingredients']) == sorted(ingredients):
                return {
                    "action_type": "COMBINE",
                    "output_item": output_item,
                    "ingredients": ingredients
                }

    # --- 4. Check for MOVE action ---
    if before_counts == after_counts and len(before_items) == len(after_items):
        before_positions = {item['item_name']: item.get('position') for item in before_items}
        after_positions = {item['item_name']: item.get('position') for item in after_items}

        # This simple check works if all item names are unique.
        # For duplicates, a more complex mapping would be needed.
        if before_positions != after_positions:
            moved_item_name = None
            from_pos = None
            to_pos = None
            for name in before_positions:
                if before_positions.get(name) != after_positions.get(name):
                    moved_item_name = name
                    from_pos = before_positions[name]
                    to_pos = after_positions[name]
                    break
            if moved_item_name:
                return {
                    "action_type": "MOVE",
                    "item_name": moved_item_name,
                    "from_position": from_pos,
                    "to_position": to_pos,
                }
    
    return {"action_type": "unknown_action"}

def main():
    """
    Main loop for automatically labeling actions between captured states.
    """
    print("Starting automatic action labeling process.")
    
    try:
        with open('data/recipes.json', 'r') as f:
            recipes = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        print("Warning: recipes.json not found or corrupted. Combine actions will not be labeled.")
        recipes = {"recipes": []}

    records = get_sorted_data_files()
    
    labeled_count = 0
    for i in range(len(records) - 1):
        record1 = records[i]
        record2 = records[i+1]

         #Skip if already labeled, unless you want to force re-labeling
        action_obj = record1.get('action')
        if isinstance(action_obj, dict) and action_obj.get('action_type') != 'unknown_action':
             continue
        if isinstance(action_obj, str) and action_obj != 'unknown_action' and action_obj is not None:
             continue

        action = infer_action(record1, record2, recipes)
        record1['action'] = action
        
        file_path = os.path.join(DATA_DIR, f"{record1['id']}.json")
        with open(file_path, 'w') as f:
            json.dump(record1, f, indent=4, cls=NumpyEncoder)
        
        if action.get('action_type') != "unknown_action":
            print(f"Labeled {record1['id']} with action: {action}")
            labeled_count += 1
        
    print(f"\nAutomatic labeling complete. Labeled {labeled_count} records.")

if __name__ == "__main__":
    main()