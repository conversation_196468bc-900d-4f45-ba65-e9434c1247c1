import heapq
import random
from typing import List, Optional

from simulator.core import Player, Item, ItemInstance


class Event:
    """Base class for events in the battle simulation."""
    def __init__(self, time: float, priority: int = 0):
        self.time = time
        self.priority = priority

    def __lt__(self, other):
        """Sort by time, then priority."""
        if self.time == other.time:
            return self.priority < other.priority
        return self.time < other.time

# Event Subclasses
class ItemActivationEvent(Event):
    def __init__(self, time: float, item_instance_id: int, source_player_id: int, priority: int = 10):
        super().__init__(time, priority)
        self.item_instance_id = item_instance_id
        self.source_player_id = source_player_id

class DamageEvent(Event):
    def __init__(self, time: float, target_player_id: int, amount: float, damage_type: str, priority: int = 5):
        super().__init__(time, priority)
        self.target_player_id = target_player_id
        self.amount = amount
        self.damage_type = damage_type

class ApplyBuffEvent(Event):
    def __init__(self, time: float, target_player_id: int, buff_name: str, stacks: int, priority: int = 7):
        super().__init__(time, priority)
        self.target_player_id = target_player_id
        self.buff_name = buff_name
        self.stacks = stacks

class BuffTickEvent(Event):
    def __init__(self, time: float, target_player_id: int, buff_name: str, priority: int = 6):
        super().__init__(time, priority)
        self.target_player_id = target_player_id
        self.buff_name = buff_name

class HealEvent(Event):
    def __init__(self, time: float, target_player_id: int, amount: float, priority: int = 5):
        super().__init__(time, priority)
        self.target_player_id = target_player_id
        self.amount = amount

class BattleSimulator:
    """Manages the event-driven battle between two players."""

    def __init__(self, player1: Player, player2: Player, db_connection):
        self.player1 = player1
        self.player2 = player2
        self.db_connection = db_connection
        self.event_queue = []
        self.time = 0.0
        self.battle_log = []
        # A simple way to get players by an ID
        self._players = {id(p): p for p in [player1, player2]}

    def _get_player(self, player_id: int) -> Optional[Player]:
        return self._players.get(player_id)

    def _get_opponent(self, player_id: int) -> Optional[Player]:
        return self.player2 if id(self.player1) == player_id else self.player1
    
    def _schedule_event(self, event: Event):
        """Pushes an event onto the priority queue."""
        heapq.heappush(self.event_queue, event)

    def _initialize_battle(self):
        """Sets up the initial state of the battle, scheduling first item activations."""
        for player in [self.player1, self.player2]:
            for item_instance in player.backpack.items.values():
                cooldown = item_instance.item.raw_stats.get("cooldown")
                if cooldown:
                    # Add a small random jitter (~0.01s) to break ties
                    initial_activation_time = cooldown + random.uniform(0.001, 0.01)
                    event = ItemActivationEvent(
                        time=initial_activation_time,
                        item_instance_id=item_instance.instance_id,
                        source_player_id=id(player)
                    )
                    self._schedule_event(event)

    def _process_event(self, event: Event):
        """Processes a single event from the queue."""
        self.battle_log.append(f"{self.time:.2f}s: Processing {event.__class__.__name__}")
        
        if isinstance(event, ItemActivationEvent):
            player = self._get_player(event.source_player_id)
            opponent = self._get_opponent(event.source_player_id)
            item_instance = player.backpack.items.get(event.item_instance_id)

            if not item_instance: return 

            stats = item_instance.item.raw_stats
            
            # Basic damage effect
            if "damage" in stats:
                self._schedule_event(DamageEvent(self.time, id(opponent), stats["damage"], "physical"))
            
            # Basic healing effect
            if "heal" in stats:
                self._schedule_event(HealEvent(self.time, id(player), stats["heal"]))

            # Reschedule the item's next activation
            cooldown = stats.get("cooldown")
            if cooldown:
                next_time = self.time + cooldown
                self._schedule_event(ItemActivationEvent(next_time, event.item_instance_id, event.source_player_id))

        elif isinstance(event, DamageEvent):
            target = self._get_player(event.target_player_id)
            if target:
                # Simple damage application for now
                target.health -= event.amount
                self.battle_log.append(f"  > {target.name} takes {event.amount} damage, health is now {target.health:.2f}")

        elif isinstance(event, HealEvent):
            target = self._get_player(event.target_player_id)
            if target:
                target.health = min(target.max_health, target.health + event.amount)
                self.battle_log.append(f"  > {target.name} heals for {event.amount}, health is now {target.health:.2f}")

        # Placeholder for buff events
        elif isinstance(event, ApplyBuffEvent):
            self.battle_log.append(f"  > Applying {event.buff_name} to player...")
        
        elif isinstance(event, BuffTickEvent):
            self.battle_log.append(f"  > Buff {event.buff_name} ticks...")


    def run_battle(self) -> str:
        """The main simulation loop."""
        self._initialize_battle()
        
        timeout = 60.0  # 60-second battle timer

        while self.player1.health > 0 and self.player2.health > 0 and self.event_queue and self.time < timeout:
            event = heapq.heappop(self.event_queue)
            
            # If event is in the past, skip it (can happen with complex interactions)
            if event.time < self.time:
                continue

            self.time = event.time
            self._process_event(event)

        # If the loop ended because the event queue is empty, fast-forward time to the timeout
        if not self.event_queue and self.player1.health > 0 and self.player2.health > 0:
            self.time = timeout
            self.battle_log.append(f"Timeout reached at {self.time:.2f}s")
        # Determine winner
        if self.player1.health <= 0:
            return self.player2.name
        elif self.player2.health <= 0:
            return self.player1.name
        else:
            # If timeout, winner is one with more health %
            p1_health_percent = self.player1.health / self.player1.max_health
            p2_health_percent = self.player2.health / self.player2.max_health
            if p1_health_percent > p2_health_percent:
                return self.player1.name
            elif p2_health_percent > p1_health_percent:
                return self.player2.name
            return "Draw"