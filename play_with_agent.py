import time
import joblib
import numpy as np
from vision_system import get_game_state
from item_recognizer import load_item_templates
from train_agent import extract_features

MODEL_FILE = 'backpack_battles_agent.joblib'
TEMPLATE_FOLDER = 'data/item_images/'

def main():
    """
    Main loop to load the agent, watch the game, and predict actions.
    """
    print("Loading the trained agent...")
    try:
        model_data = joblib.load(MODEL_FILE)
        agent = model_data['agent']
        scaler = model_data['scaler']
    except FileNotFoundError:
        print(f"Error: Model file not found at '{MODEL_FILE}'.")
        print("Please run train_agent.py to train and save the agent first.")
        return

    print("Agent loaded successfully. Starting prediction loop...")
    print("The agent will now watch your game and suggest the next best action.")
    print("Press Ctrl+C to stop.")

    item_templates = load_item_templates(TEMPLATE_FOLDER)

    try:
        last_predicted_action = None
        while True:
            # 1. Capture the current game state
            game_state = get_game_state(item_templates)

            # 2. Extract features and scale them
            features = extract_features(game_state)
            features_scaled = scaler.transform([features]) # Note: transform expects a 2D array

            # 3. Predict the action
            predicted_action = agent.predict(features_scaled)[0]

            # 4. Display the prediction only if it changes
            if predicted_action != last_predicted_action:
                print(f"\n--- Agent Suggests ---")
                print(f"==>  {predicted_action.replace('_', ' ').upper()}  <==")
                print("----------------------")
                last_predicted_action = predicted_action

            time.sleep(2) # Pause between predictions
            
    except KeyboardInterrupt:
        print("\nAgent stopped. Have a great game!")

if __name__ == "__main__":
    main()