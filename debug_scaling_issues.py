import json
import os
from PIL import Image
from calibrated_grid_coordinates import get_item_placement_coords
from generate_synthetic_dataset import convert_grid_layout_to_shape

def analyze_scaling_issues():
    """
    Analyze scaling issues by comparing item shapes, image sizes, and grid dimensions.
    """
    print("Analyzing scaling issues...")
    
    # Load JSON data
    with open('Backpack Battle Items.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Load some sample items to analyze
    sample_items = ["Axe", "Dagger", "Hero Sword", "Banana", "Shield", "Bow"]
    
    print("Sample item analysis:")
    print("=" * 80)
    
    for item_name in sample_items:
        # Find item in JSON
        item_data = None
        for item in data.get('enhanced_items', []):
            if item.get('name') == item_name:
                item_data = item
                break
        
        if not item_data:
            print(f"❌ {item_name}: Not found in JSON")
            continue
        
        # Get grid layout
        grid_layout = item_data.get('grid_layout', [])
        grid_width = item_data.get('grid_width', 0)
        grid_height = item_data.get('grid_height', 0)
        
        # Calculate actual shape dimensions using the new conversion function
        if grid_layout:
            converted_shape = convert_grid_layout_to_shape(grid_layout)
            actual_width = len(converted_shape[0]) if converted_shape and converted_shape[0] else 0
            actual_height = len(converted_shape) if converted_shape else 0
        else:
            converted_shape = [[1]]
            actual_width = 1
            actual_height = 1
        
        # Check image file
        image_filename = item_name.replace(' ', '_') + '.png'
        image_path = os.path.join('data/item_images', image_filename)
        
        if os.path.exists(image_path):
            img = Image.open(image_path)
            img_w, img_h = img.size
        else:
            img_w, img_h = 0, 0
            print(f"❌ Image not found: {image_path}")
        
        # Get grid placement coordinates for this item size
        if actual_width > 0 and actual_height > 0:
            placement_coords = get_item_placement_coords(0, 0, actual_width, actual_height)
            if placement_coords:
                grid_area_w = placement_coords['width']
                grid_area_h = placement_coords['height']
            else:
                grid_area_w = grid_area_h = 0
        else:
            grid_area_w = grid_area_h = 0
        
        print(f"\n📦 {item_name}:")
        print(f"   JSON grid_width/height: {grid_width}x{grid_height}")
        print(f"   Actual layout size: {actual_width}x{actual_height}")
        print(f"   Converted shape: {converted_shape}")
        print(f"   Original grid layout: {grid_layout}")
        print(f"   Image file size: {img_w}x{img_h} pixels")
        print(f"   Grid area size: {grid_area_w}x{grid_area_h} pixels")
        
        if grid_area_w > 0 and grid_area_h > 0:
            # Calculate current scaling
            padding_factor = 0.85
            target_w = int(grid_area_w * padding_factor)
            target_h = int(grid_area_h * padding_factor)
            
            if img_w > 0 and img_h > 0:
                scale_w = target_w / img_w
                scale_h = target_h / img_h
                scale_factor = min(scale_w, scale_h)
                
                final_w = int(img_w * scale_factor)
                final_h = int(img_h * scale_factor)
                
                print(f"   Target size (85% of grid): {target_w}x{target_h}")
                print(f"   Scale factor: {scale_factor:.3f}")
                print(f"   Final scaled size: {final_w}x{final_h}")
                
                # Check if this seems reasonable
                if scale_factor < 0.1:
                    print(f"   ⚠️  Scale factor very small - image might be too large")
                elif scale_factor > 5.0:
                    print(f"   ⚠️  Scale factor very large - image might be too small")
                else:
                    print(f"   ✅ Scale factor seems reasonable")
    
    print("\n" + "=" * 80)
    print("Grid cell analysis:")
    
    # Analyze individual grid cells
    single_cell = get_item_placement_coords(0, 0, 1, 1)
    if single_cell:
        print(f"Single cell size: {single_cell['width']}x{single_cell['height']} pixels")
    
    double_cell_h = get_item_placement_coords(0, 0, 2, 1)
    if double_cell_h:
        print(f"2x1 cell size: {double_cell_h['width']}x{double_cell_h['height']} pixels")
    
    double_cell_v = get_item_placement_coords(0, 0, 1, 2)
    if double_cell_v:
        print(f"1x2 cell size: {double_cell_v['width']}x{double_cell_v['height']} pixels")
    
    quad_cell = get_item_placement_coords(0, 0, 2, 2)
    if quad_cell:
        print(f"2x2 cell size: {quad_cell['width']}x{quad_cell['height']} pixels")

def compare_with_reference():
    """
    Compare our scaling with what we can see in reference screenshots.
    """
    print("\n" + "=" * 80)
    print("Reference comparison suggestions:")
    print("1. Look at your reference screenshots in data/screenshots/bbss/")
    print("2. Find items like Axe (2x2), Dagger (2x1), etc.")
    print("3. Measure how much of the grid cell they actually occupy")
    print("4. Compare with our calculated scaling above")
    print("5. The padding_factor might need adjustment (currently 0.85 = 85%)")

if __name__ == "__main__":
    analyze_scaling_issues()
    compare_with_reference()
