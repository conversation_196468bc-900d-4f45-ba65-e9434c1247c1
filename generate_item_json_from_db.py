import sqlite3
import json
import os
import sys

def generate_item_json(db_path, json_output_path):
    """
    Reads item data from the SQLite database and generates a JSON file.
    """
    conn = None
    items_data = {}
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row  # This allows accessing columns by name
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM Items")
        
        for row in cursor.fetchall():
            item_name = row['Name']
            item_data = {
                "name": item_name,
                "rarity": row['Rarity'],
                "cost": row['Cost'],
                "class": row['Class'],
                "type": row['Type'],
                "description": row['Description'],
                "raw_stats": json.loads(row['RawStats']) if row['RawStats'] else {},
                "synergy_triggers": json.loads(row['SynergyTriggers']) if row['SynergyTriggers'] else [],
                "synergy_effects": json.loads(row['SynergyEffects']) if row['SynergyEffects'] else [],
            }
            
            # Deserialize shape from JSON string to Python list/list of lists
            # The shape should already be correct JSON when scraped by wiki_scraper.py
            if row['Shape']:
                try:
                    item_data['shape'] = json.loads(row['Shape'])
                except json.JSONDecodeError:
                    print(f"Warning: Could not decode shape for {item_name}. Storing as raw string.")
                    item_data['shape'] = row['Shape']
            else:
                item_data['shape'] = [] # Default to empty list if no shape
                
            items_data[item_name] = item_data

    except sqlite3.Error as e:
        print(f"Database error: {e}")
        sys.exit(1)
    finally:
        if conn:
            conn.close()

    # Ensure the directory for the JSON output exists
    os.makedirs(os.path.dirname(json_output_path), exist_ok=True)

    with open(json_output_path, 'w', encoding='utf-8') as f:
        json.dump(items_data, f, indent=4, ensure_ascii=False)
    print(f"Successfully generated {json_output_path} with data for {len(items_data)} items.")

if __name__ == "__main__":
    DB_PATH = 'GameData.db'
    JSON_OUTPUT_PATH = 'data/Backpack Battle Items.json'
    generate_item_json(DB_PATH, JSON_OUTPUT_PATH)