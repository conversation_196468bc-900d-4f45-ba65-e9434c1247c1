from PIL import Image, ImageDraw
import os

def manual_grid_calibration():
    """
    Manually calibrate grid coordinates based on visual inspection of game screenshots.
    This requires examining the actual screenshots to find the precise grid boundaries.
    """
    print("Manual grid calibration based on visual inspection...")
    
    # Load a clear screenshot for analysis
    screenshot_files = [f for f in os.listdir("data/screenshots/bbss") if f.endswith('.png')]
    
    # Let's examine a few different screenshots to find one with clear grid visibility
    for i, screenshot_file in enumerate(screenshot_files[:3]):
        print(f"\nExamining {screenshot_file}...")
        
        screenshot_path = os.path.join("data/screenshots/bbss", screenshot_file)
        img = Image.open(screenshot_path)
        
        # Current inventory ROI
        inventory_roi = (114, 80, 1129, 833)
        inventory_area = img.crop(inventory_roi)
        
        # Save for manual inspection
        inventory_area.save(f"manual_inspect_{i+1}.png")
        print(f"Saved manual_inspect_{i+1}.png for visual inspection")
    
    # Based on manual inspection of the game screenshots, I need to determine:
    # 1. The exact boundaries of the visible grid
    # 2. The spacing between grid lines
    # 3. The offset from the inventory ROI edges
    
    # THESE VALUES NEED TO BE DETERMINED BY VISUAL INSPECTION
    # Placeholder values - to be updated after examining the debug images
    
    # Estimated grid parameters (to be refined based on visual inspection)
    inventory_roi = (114, 80, 1129, 833)
    
    # Within the inventory area, find the actual grid
    # These offsets are from the inventory ROI, not the full screen
    grid_offset_left = 20    # Distance from left edge of inventory to first grid line
    grid_offset_top = 20     # Distance from top edge of inventory to first grid line
    grid_offset_right = 20   # Distance from right edge of inventory to last grid line
    grid_offset_bottom = 20  # Distance from bottom edge of inventory to last grid line
    
    # Calculate actual grid area
    inventory_width = inventory_roi[2] - inventory_roi[0]  # 1015
    inventory_height = inventory_roi[3] - inventory_roi[1]  # 753
    
    actual_grid_width = inventory_width - grid_offset_left - grid_offset_right
    actual_grid_height = inventory_height - grid_offset_top - grid_offset_bottom
    
    # 9 columns, 7 rows
    cell_width = actual_grid_width / 9
    cell_height = actual_grid_height / 7
    
    print(f"\nCalculated grid parameters:")
    print(f"Inventory size: {inventory_width} x {inventory_height}")
    print(f"Grid area: {actual_grid_width} x {actual_grid_height}")
    print(f"Cell size: {cell_width:.2f} x {cell_height:.2f}")
    
    # Generate precise grid coordinates
    grid_cells = []
    
    for row in range(7):
        for col in range(9):
            # Calculate cell boundaries within inventory area
            local_x1 = grid_offset_left + col * cell_width
            local_y1 = grid_offset_top + row * cell_height
            local_x2 = local_x1 + cell_width
            local_y2 = local_y1 + cell_height
            
            # Convert to absolute screen coordinates
            abs_x1 = inventory_roi[0] + local_x1
            abs_y1 = inventory_roi[1] + local_y1
            abs_x2 = inventory_roi[0] + local_x2
            abs_y2 = inventory_roi[1] + local_y2
            
            cell = {
                'row': row,
                'col': col,
                'local_x1': int(local_x1),
                'local_y1': int(local_y1),
                'local_x2': int(local_x2),
                'local_y2': int(local_y2),
                'abs_x1': int(abs_x1),
                'abs_y1': int(abs_y1),
                'abs_x2': int(abs_x2),
                'abs_y2': int(abs_y2),
                'center_x': int(abs_x1 + cell_width/2),
                'center_y': int(abs_y1 + cell_height/2),
                'width': int(cell_width),
                'height': int(cell_height)
            }
            grid_cells.append(cell)
    
    # Create a visual verification image
    print(f"\nCreating visual verification...")
    
    # Load the first screenshot and draw grid overlay
    first_screenshot = Image.open(os.path.join("data/screenshots/bbss", screenshot_files[0]))
    draw = ImageDraw.Draw(first_screenshot)
    
    # Draw grid lines
    for cell in grid_cells:
        # Draw cell boundaries
        draw.rectangle([cell['abs_x1'], cell['abs_y1'], cell['abs_x2'], cell['abs_y2']], 
                      outline='red', width=1)
        
        # Draw cell center point
        center_x, center_y = cell['center_x'], cell['center_y']
        draw.ellipse([center_x-2, center_y-2, center_x+2, center_y+2], fill='blue')
    
    # Save verification image
    first_screenshot.save("grid_verification.png")
    print("Saved grid_verification.png - check if grid aligns with actual game grid")
    
    return grid_cells

def save_calibrated_grid(grid_cells):
    """Save the calibrated grid coordinates."""
    
    with open('calibrated_grid_coordinates.py', 'w') as f:
        f.write("# Manually calibrated grid coordinates based on actual game screenshots\n")
        f.write("# Each coordinate represents one grid cell in the 9x7 inventory\n\n")
        f.write("CALIBRATED_GRID_COORDS = [\n")
        for cell in grid_cells:
            f.write(f"    {cell},\n")
        f.write("]\n\n")
        f.write("def get_calibrated_cell(row, col):\n")
        f.write("    \"\"\"Get calibrated coordinates for a specific grid cell (0-indexed).\"\"\"\n")
        f.write("    for cell in CALIBRATED_GRID_COORDS:\n")
        f.write("        if cell['row'] == row and cell['col'] == col:\n")
        f.write("            return cell\n")
        f.write("    return None\n\n")
        f.write("def get_cell_center(row, col):\n")
        f.write("    \"\"\"Get the center coordinates of a grid cell.\"\"\"\n")
        f.write("    cell = get_calibrated_cell(row, col)\n")
        f.write("    if cell:\n")
        f.write("        return (cell['center_x'], cell['center_y'])\n")
        f.write("    return None\n")
    
    print("Saved calibrated coordinates to calibrated_grid_coordinates.py")
    print("\nNext steps:")
    print("1. Examine grid_verification.png to see if the red grid aligns with the game grid")
    print("2. If not aligned, adjust the grid_offset values in this script")
    print("3. Re-run until the grid perfectly matches the game's visible grid")

if __name__ == "__main__":
    grid_cells = manual_grid_calibration()
    save_calibrated_grid(grid_cells)
