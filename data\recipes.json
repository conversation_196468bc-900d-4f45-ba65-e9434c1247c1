{"recipes": [{"result": "Amulet of Alchemy", "ingredients": ["Unidentified Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Amulet of Energy", "ingredients": ["Unidentified Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Amulet of Feasting", "ingredients": ["Unidentified Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Amulet of Life", "ingredients": ["Unidentified Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Amulet of Steel", "ingredients": ["Unidentified Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Box of Prosperity", "ingredients": ["Box of Riches"], "catalyst": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Bunch of Coins", "ingredients": ["Piggybank"], "catalyst": "Hammer", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Gloves of Power", "ingredients": ["Gloves of Haste", "Stone Skin Potion"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Goobert", "ingredients": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Platinum Customer Card", "ingredients": ["Customer Card", "Customer Card"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Shepherd's Crook", "ingredients": ["Broom", "Whetstone"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Spiked Shield", "ingredients": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Steel Goobert", "ingredients": ["Goobert", "Hero Sword"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Stone Armor", "ingredients": ["<PERSON><PERSON> Armor", "Stone Skin Potion"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON>", "ingredients": ["Cap of Resilience", "Stone Skin Potion"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Stone Shoes", "ingredients": ["<PERSON><PERSON>", "Stone Skin Potion"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Health Potion", "ingredients": ["Health Potion"], "catalyst": "<PERSON><PERSON><PERSON>", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Health Potion", "ingredients": ["Health Potion", "Healing Herbs"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Heroic Potion", "ingredients": ["Heroic Potion"], "catalyst": "<PERSON><PERSON><PERSON>", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Heroic Potion", "ingredients": ["Heroic Potion", "Banana"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Stone Skin Potion", "ingredients": ["Stone Skin Potion", "Stone", "Stone"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Stone Skin Potion", "ingredients": ["Stone Skin Potion"], "catalyst": "<PERSON><PERSON><PERSON>", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Amulet of Darkness", "ingredients": ["Unidentified Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Heart of Darkness", "ingredients": ["Heart Container", "Corrupted Crystal"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "King Crown", "ingredients": ["Glowing Crown", "Box of Riches"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Snowball", "ingredients": ["Wonky Snowman"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Amulet of the Wild", "ingredients": ["Unidentified Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Corrupted Armor", "ingredients": ["Holy Armor", "Corrupted Crystal"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Moon Armor", "ingredients": ["Holy Armor", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Ice Armor", "ingredients": ["<PERSON><PERSON> Armor", "Snowball"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Vampiric Armor", "ingredients": ["<PERSON><PERSON> Armor", "Blood Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Burning Coal", "ingredients": ["Lump of Coal"], "catalyst": "Fire", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Vampiri<PERSON>s", "ingredients": ["Gloves of Haste", "Blood Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Cap of Discomfort", "ingredients": ["Cap of Resilience", "Corrupted Crystal"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "King <PERSON><PERSON>", "ingredients": ["Goobert", "King Crown"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Light Goobert", "ingredients": ["Goobert", "Lightsaber"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Blood Goobert", "ingredients": ["Goobert", "Blood Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON>", "ingredients": ["Health Potion", "Blueberries"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Vampiric Potion", "ingredients": ["Strong Health Potion"], "catalyst": "Blood Amulet", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Moon Shield", "ingredients": ["Shield of Valor", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON><PERSON>", "ingredients": ["<PERSON><PERSON>", "Snowball"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Stone Golem", "ingredients": ["Heart Container", "Stone", "Stone", "Stone", "Stone"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON>", "ingredients": ["<PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Darksaber", "ingredients": ["Lightsaber", "Corrupted Crystal"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Pandamonium", "ingredients": ["Pan", "Corrupted Crystal"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Burning Torch", "ingredients": ["<PERSON>ch"], "catalyst": "Fire", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Eggscalibur", "ingredients": ["Pan", "Heroic Potion"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Holy Spear", "ingredients": ["Spear", "Glowing Crown"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Shell Totem", "ingredients": ["Wooden Sword", "Shiny Shell"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Snow Stick", "ingredients": ["Broom", "Snowball"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Frostbite", "ingredients": ["Hungry Blade", "Snowball"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Magic Torch", "ingredients": ["<PERSON>ch", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON><PERSON> Dagger", "ingredients": ["<PERSON>gger", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Magic Staff", "ingredients": ["Broom", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Serpent Staff", "ingredients": ["Magic Staff", "Pestilence Flask"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Prismatic Sword", "ingredients": ["Wooden Sword", "Prismatic Orb"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON><PERSON><PERSON>", "ingredients": ["Hungry Blade", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Claws of Attack", "ingredients": ["Gloves of Haste", "<PERSON><PERSON><PERSON>"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Crossblades", "ingredients": ["Falcon Blade", "<PERSON> Longsword"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Falcon Blade", "ingredients": ["Hero Sword", "Gloves of Haste", "Gloves of Haste"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON> Longsword", "ingredients": ["Hero Sword", "Whetstone", "Whetstone"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Hero Sword", "ingredients": ["Wooden Sword", "Whetstone", "Whetstone"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON>", "ingredients": ["Ripsaw Blade", "Whetstone"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON>", "ingredients": ["<PERSON>gger", "Pestilence Flask"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON><PERSON>", "ingredients": ["Broom", "Pan"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON>ch", "ingredients": ["Wooden Sword", "Lump of Coal"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Thornbloom", "ingredients": ["Thorn Whip", "Heroic Potion"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Bloody Dagger", "ingredients": ["<PERSON>gger", "Blood Amulet"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON><PERSON><PERSON>", "ingredients": ["Hungry Blade", "Thorn Whip"], "catalyst": "", "class_restriction": "", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Armored Power Puppy", "ingredients": ["Power Puppy"], "catalyst": "Utility Pouch", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Armored Power Puppy", "ingredients": ["Power Puppy"], "catalyst": "Forging Hammer", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Armored Wisdom Puppy", "ingredients": ["<PERSON>"], "catalyst": "Utility Pouch", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Armored Wisdom Puppy", "ingredients": ["<PERSON>"], "catalyst": "Forging Hammer", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Dragon Claws", "ingredients": ["Gloves of Haste"], "catalyst": "Utility Pouch", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Dragon Claws", "ingredients": ["Gloves of Haste"], "catalyst": "Forging Hammer", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Dragonscale Armor", "ingredients": ["<PERSON><PERSON> Armor"], "catalyst": "Utility Pouch", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Dragonscale Armor", "ingredients": ["<PERSON><PERSON> Armor"], "catalyst": "Forging Hammer", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON> Boots", "ingredients": ["<PERSON><PERSON>"], "catalyst": "Utility Pouch", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON> Boots", "ingredients": ["<PERSON><PERSON>"], "catalyst": "Forging Hammer", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Cheese Goobert", "ingredients": ["Goobert", "Cheese"], "catalyst": "", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Armored Courage Puppy", "ingredients": ["<PERSON>"], "catalyst": "Utility Pouch", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Armored Courage Puppy", "ingredients": ["<PERSON>"], "catalyst": "Forging Hammer", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Chain Whip", "ingredients": ["Thorn Whip"], "catalyst": "Forging Hammer", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Chain Whip", "ingredients": ["Thorn Whip"], "catalyst": "Utility Pouch", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Spiked Staff", "ingredients": ["Magic Staff", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Busted Blade", "ingredients": ["Impractically Large Greatsword"], "catalyst": "Forging Hammer", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Busted Blade", "ingredients": ["Impractically Large Greatsword"], "catalyst": "Utility Pouch", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Double Axe", "ingredients": ["Axe", "Axe"], "catalyst": "", "class_restriction": "<PERSON><PERSON><PERSON><PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Sun Armor", "ingredients": ["Holy Armor", "Flame", "Flame"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON>", "ingredients": ["Goobert", "Chili Pepper"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Rainbow Goobert Epicglob Uberviscous", "ingredients": ["Goobert", "Blood Goobert", "<PERSON><PERSON>", "Light Goobert", "Steel Goobert"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Sun Shield", "ingredients": ["Shield of Valor", "Flame", "Flame"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Emerald Whelp", "ingredients": ["Emerald Egg"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Obsidian Dragon", "ingredients": ["<PERSON>", "Draconic Orb"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Sapphire Whelp", "ingredients": ["Sapphire Egg"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Amethyst Whelp", "ingredients": ["Amethyst Egg"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Staff of Fire", "ingredients": ["Magic Staff", "Draconic Orb"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Burning Blade", "ingredients": ["Burning Sword", "Whetstone", "Whetstone"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Burning Blade", "ingredients": ["<PERSON> Longsword", "Flame", "Flame"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Burning Sword", "ingredients": ["Hero Sword", "Flame", "Flame"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Flame Whip", "ingredients": ["Thorn Whip", "Flame", "Flame"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON> Dagger", "ingredients": ["<PERSON>gger", "Flame", "Flame"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON><PERSON>pear", "ingredients": ["Spear", "Flame", "Flame"], "catalyst": "", "class_restriction": "Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON> Piggy", "ingredients": ["Piggybank", "<PERSON> Clover", "<PERSON> Clover"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Carrot Goobert", "ingredients": ["Goobert", "Carrot", "Carrot"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Rat Chef", "ingredients": ["Rat", "Healing Herbs"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Rainbow Goobert Megasludge Alphapuddle", "ingredients": ["Goobert", "Blood Goobert", "Light Goobert", "Carrot Goobert", "Steel Goobert"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Squirrel Archer", "ingredients": ["Squirrel", "Shortbow"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Critwood Staff", "ingredients": ["Magic Staff", "Acorn Collar"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Belladonna's Shade", "ingredients": ["Shortbow", "Pestilence Flask"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Belladonna's Whisper", "ingredients": ["Bow and Arrow", "Pestilence Flask"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Fortuna's Grace", "ingredients": ["Bow and Arrow", "<PERSON> Clover", "<PERSON> Clover"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Fortuna's Hope", "ingredients": ["Shortbow", "<PERSON> Clover", "<PERSON> Clover"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Tusk Piercer", "ingredients": ["Bow and Arrow", "<PERSON><PERSON><PERSON>"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Tusk Poker", "ingredients": ["Shortbow", "<PERSON><PERSON><PERSON>"], "catalyst": "", "class_restriction": "<PERSON>", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Ice Dragon", "ingredients": ["<PERSON>", "White-Eyes Blue Dragon"], "catalyst": "", "class_restriction": "<PERSON>, Pyromancer", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Pestilence Flask", "ingredients": ["Pestilence Flask", "Fly Agaric"], "catalyst": "", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Pestilence Flask", "ingredients": ["Pestilence Flask"], "catalyst": "<PERSON><PERSON><PERSON>", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Doom Cap", "ingredients": ["Fly Agaric", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Poison Goobert", "ingredients": ["Goobert", "Fly Agaric", "Fly Agaric"], "catalyst": "", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Rainbow Goobert Omegaooze Primeslime", "ingredients": ["Goobert", "Blood Goobert", "Light Goobert", "Poison Goobert", "Steel Goobert"], "catalyst": "", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Demonic Flask", "ingredients": ["<PERSON><PERSON>"], "catalyst": "<PERSON><PERSON><PERSON>", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Demonic Flask", "ingredients": ["<PERSON><PERSON>", "Corrupted Crystal"], "catalyst": "", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Divine Potion", "ingredients": ["Divine Potion"], "catalyst": "<PERSON><PERSON><PERSON>", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Mana <PERSON>tion", "ingredients": ["<PERSON><PERSON>"], "catalyst": "<PERSON><PERSON><PERSON>", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Strong Vampiric Potion", "ingredients": ["Vampiric Potion"], "catalyst": "<PERSON><PERSON><PERSON>", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "<PERSON>", "ingredients": ["<PERSON>", "Holo Fire Lizard"], "catalyst": "", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}, {"result": "Staff of Unhealing", "ingredients": ["Magic Staff", "<PERSON><PERSON>"], "catalyst": "", "class_restriction": "Reaper", "source_url": "https://backpackbattles.wiki.gg/wiki/Recipe"}]}