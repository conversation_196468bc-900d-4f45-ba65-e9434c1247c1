import os
import json
from PIL import Image
import numpy as np

def analyze_reference_screenshots():
    """
    Analyze reference screenshots to extract actual item shapes and scales.
    """
    print("Analyzing reference screenshots for item scaling...")
    
    ref_dir = "data/item_scale_reference"
    ref_files = [f for f in os.listdir(ref_dir) if f.endswith('.png')]
    
    print(f"Found {len(ref_files)} reference screenshots")
    
    # Load class names to know what items to look for
    with open('data/object_detection/classes.txt', 'r') as f:
        class_names = [line.strip() for line in f if line.strip()]
    
    print(f"Looking for {len(class_names)} different items")
    
    # Process each reference screenshot
    for i, ref_file in enumerate(ref_files):
        print(f"\nProcessing {ref_file}...")
        
        ref_path = os.path.join(ref_dir, ref_file)
        screenshot = Image.open(ref_path)
        
        print(f"Screenshot size: {screenshot.size}")
        
        # Save full screenshot for manual inspection
        screenshot.save(f"debug_ref_full_{i+1}.png")
        
        # Try to detect grid structure in the screenshot
        # Since the inventory is artificially large, we need to find the grid pattern
        detect_grid_in_screenshot(screenshot, i+1)

def detect_grid_in_screenshot(screenshot, ref_num):
    """
    Try to detect the grid structure and item positions in a reference screenshot.
    """
    print(f"  Detecting grid structure in reference {ref_num}...")
    
    # Convert to grayscale for analysis
    gray = screenshot.convert('L')
    gray_array = np.array(gray)
    
    width, height = screenshot.size
    
    # Look for repeating patterns that might indicate grid structure
    # This is a simplified approach - we might need manual calibration
    
    # Try to find horizontal and vertical lines that repeat
    # Grid lines would show up as consistent patterns
    
    # For now, let's extract some sample regions and save them for manual inspection
    # We'll divide the image into a grid and extract samples
    
    sample_size = 150  # Size of sample regions to extract
    samples_per_row = width // sample_size
    samples_per_col = height // sample_size
    
    print(f"  Extracting {samples_per_row}x{samples_per_col} sample regions...")
    
    sample_count = 0
    for row in range(min(5, samples_per_col)):  # Limit to first 5 rows
        for col in range(min(8, samples_per_row)):  # Limit to first 8 columns
            x1 = col * sample_size
            y1 = row * sample_size
            x2 = min(x1 + sample_size, width)
            y2 = min(y1 + sample_size, height)
            
            sample_region = screenshot.crop((x1, y1, x2, y2))
            sample_region.save(f"debug_ref_{ref_num}_sample_{row}_{col}.png")
            sample_count += 1
    
    print(f"  Extracted {sample_count} sample regions")

def create_item_template_matcher():
    """
    Create a system to match items from our item_images with regions in reference screenshots.
    """
    print("\nCreating item template matching system...")
    
    item_img_dir = "data/item_images"
    item_files = [f for f in os.listdir(item_img_dir) if f.endswith('.png')]
    
    # Load a few sample items for template matching
    sample_items = ["Axe.png", "Dagger.png", "Hero_Sword.png", "Banana.png", "Shield.png"]
    
    templates = {}
    for item_file in sample_items:
        if item_file in item_files:
            item_path = os.path.join(item_img_dir, item_file)
            item_img = Image.open(item_path).convert('RGBA')
            
            # Create multiple scaled versions for template matching
            original_size = item_img.size
            templates[item_file] = {
                'original': item_img,
                'size': original_size,
                'scaled_versions': []
            }
            
            # Create scaled versions (50% to 200% of original)
            for scale in [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]:
                scaled_w = int(original_size[0] * scale)
                scaled_h = int(original_size[1] * scale)
                scaled_img = item_img.resize((scaled_w, scaled_h), Image.LANCZOS)
                templates[item_file]['scaled_versions'].append({
                    'scale': scale,
                    'image': scaled_img,
                    'size': (scaled_w, scaled_h)
                })
    
    print(f"Created templates for {len(templates)} items")
    return templates

def manual_measurement_guide():
    """
    Provide guidance for manual measurement of items in reference screenshots.
    """
    print("\n" + "="*80)
    print("MANUAL MEASUREMENT GUIDE")
    print("="*80)
    print("1. Open the debug_ref_full_*.png files")
    print("2. Look for recognizable items (Axe, Dagger, Hero Sword, etc.)")
    print("3. For each item, measure:")
    print("   - How many grid cells wide it is")
    print("   - How many grid cells tall it is") 
    print("   - What percentage of each cell it fills")
    print("   - The actual pixel dimensions of the item")
    print("4. Look for the grid pattern (lightened areas)")
    print("5. Measure the pixel size of individual grid cells")
    print("")
    print("Key items to look for:")
    print("- Axe: Should be 2x2 in default orientation")
    print("- Dagger: Should be 2x1 in default orientation") 
    print("- Hero Sword: Should be 1x2 in default orientation (tall)")
    print("- Banana: Should be a complex shape")
    print("")
    print("Once you have measurements, we can:")
    print("1. Calculate the correct grid cell size")
    print("2. Determine proper item scaling factors")
    print("3. Update the synthetic dataset generator")

def extract_specific_regions():
    """
    Extract specific regions that are likely to contain items for easier analysis.
    """
    print("\nExtracting specific regions for detailed analysis...")
    
    ref_dir = "data/item_scale_reference"
    ref_files = [f for f in os.listdir(ref_dir) if f.endswith('.png')]
    
    # Extract center regions and corners where items are likely to be
    for i, ref_file in enumerate(ref_files[:3]):  # First 3 screenshots
        ref_path = os.path.join(ref_dir, ref_file)
        screenshot = Image.open(ref_path)
        width, height = screenshot.size
        
        # Extract center region
        center_size = 800
        center_x = (width - center_size) // 2
        center_y = (height - center_size) // 2
        center_region = screenshot.crop((center_x, center_y, center_x + center_size, center_y + center_size))
        center_region.save(f"debug_ref_{i+1}_center.png")
        
        # Extract top-left region
        corner_size = 600
        top_left = screenshot.crop((0, 0, corner_size, corner_size))
        top_left.save(f"debug_ref_{i+1}_topleft.png")
        
        # Extract bottom-right region
        bottom_right = screenshot.crop((width-corner_size, height-corner_size, width, height))
        bottom_right.save(f"debug_ref_{i+1}_bottomright.png")
        
        print(f"Extracted regions from {ref_file}")

if __name__ == "__main__":
    analyze_reference_screenshots()
    templates = create_item_template_matcher()
    extract_specific_regions()
    manual_measurement_guide()
