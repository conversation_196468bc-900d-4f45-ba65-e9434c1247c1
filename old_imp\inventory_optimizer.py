import sqlite3
from typing import List

from optimizer import Optimizer, Evaluator, Layout
from simulator.core import Player, Item, load_all_item_data

# --- Configuration ---
DB_PATH = 'GameData.db'
# A list of items that are confirmed to be in the database from the file listing
ITEMS_TO_OPTIMIZE = [
    "Wooden_Sword",
    "Whetstone",
    "Leather_Armor",
    "Healing_Herbs",
]

def create_standard_opponent() -> Player:
    """Creates a standard, consistent opponent for the simulation."""
    opponent = Player(name="StandardOpponent", player_class="Pyromancer")
    # In a real scenario, you might give the opponent a default set of items.
    # For now, an empty backpack is a consistent baseline.
    return opponent

def generate_reorganization_plan(best_layout: Layout):
    """Prints the final reorganization plan to the console."""
    if not best_layout:
        print("No optimal layout was found.")
        return

    print("\n--- Optimal Inventory Layout Found ---")
    print(f"Fitness (Win Rate): {best_layout.fitness:.2%}")
    print("--------------------------------------")
    for i, item_instance in enumerate(best_layout.item_instances, 1):
        name = item_instance.item.name
        x, y = item_instance.x, item_instance.y
        rotation = item_instance.rotation * 90
        print(f"{i}. PLACE {name} at ({x}, {y}) with {rotation}° rotation.")

# --- Main Execution ---

if __name__ == "__main__":
    # 1. Load all item definitions from the database
    all_items = load_all_item_data(DB_PATH)
    if not all_items:
        print("Could not load item data. Exiting.")
        exit()

    # 2. Get the Item objects for the items we want to place
    items_to_place = [all_items[name] for name in ITEMS_TO_OPTIMIZE if name in all_items]
    if len(items_to_place) != len(ITEMS_TO_OPTIMIZE):
        print("Warning: Some items specified in ITEMS_TO_OPTIMIZE were not found in the database.")

    # 3. Set up the simulation environment
    db_connection = sqlite3.connect(DB_PATH)
    opponent = create_standard_opponent()
    
    # 4. Instantiate the optimization engine components
    evaluator = Evaluator(db_connection, opponent, num_simulations=50) # Reduce sims for speed during dev
    optimizer = Optimizer(
        items_to_place=items_to_place,
        evaluator=evaluator,
        population_size=30,
        generations=20
    )

    # 5. Run the optimization
    best_layout_found = optimizer.run_optimization()

    # 6. Generate and print the final plan
    generate_reorganization_plan(best_layout_found)
    
    # Clean up
    db_connection.close()