import cv2
import os
from calibrated_grid_coordinates import CALIBRATED_GRID_COORDS, GRID_COLS, GRID_ROWS

def draw_grid_on_background(background_path, output_path):
    """
    Draws the calibrated grid, including cell borders and center points,
    onto a background image for debugging and verification purposes.

    Args:
        background_path (str): Path to the background image.
        output_path (str): Path to save the output image with the grid overlay.
    """
    if not os.path.exists(background_path):
        print(f"Error: Background image not found at '{background_path}'")
        return

    # Load the background image
    image = cv2.imread(background_path)
    if image is None:
        print(f"Error: Could not read the background image.")
        return

    print("Drawing calibrated grid onto background image...")

    # Define colors for drawing
    grid_color = (0, 255, 0)  # Green for grid lines
    center_color = (0, 0, 255) # Red for center points
    text_color = (255, 255, 255) # White for text

    # Iterate through each cell in the calibrated grid data
    for cell in CALIBRATED_GRID_COORDS:
        # Get absolute coordinates for drawing
        x1, y1, x2, y2 = cell['abs_x1'], cell['abs_y1'], cell['abs_x2'], cell['abs_y2']
        center_x, center_y = cell['center_x'], cell['center_y']
        row, col = cell['row'], cell['col']

        # Draw the rectangle for the grid cell
        cv2.rectangle(image, (x1, y1), (x2, y2), grid_color, 2)

        # Draw a circle at the center of the cell
        cv2.circle(image, (center_x, center_y), 5, center_color, -1)
        
        # Add text label for the row and column
        label = f"{row},{col}"
        cv2.putText(image, label, (x1 + 5, y1 + 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, text_color, 1)

    # Save the final image
    cv2.imwrite(output_path, image)
    print(f"Successfully saved debug grid image to '{output_path}'")

if __name__ == "__main__":
    # Use one of the user's actual game screenshots as the background
    BACKGROUND_IMAGE = "data/screenshots/bbss/Screenshot 2025-06-23 221343.png"
    OUTPUT_IMAGE = "debug_grid_overlay.png"
    draw_grid_on_background(BACKGROUND_IMAGE, OUTPUT_IMAGE)
