import os

def generate_classes_file():
    """
    Reads image filenames from a directory, extracts the base names, and writes them
    to the classes.txt file required for YOLOv8 model training.
    """
    image_dir = 'data/item_images/'
    output_file = 'data/object_detection/classes.txt'
    
    # Ensure the output directory exists
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    try:
        # Get all file names from the image directory
        filenames = os.listdir(image_dir)
        
        # Filter for .png files and extract the base name
        class_names = [os.path.splitext(f)[0] for f in filenames if f.endswith('.png')]
        
        # Sort the class names alphabetically for consistency
        class_names.sort()
        
        # Write the class names to the output file
        with open(output_file, 'w') as f:
            for name in class_names:
                f.write(f"{name}\n")
                
        print(f"Successfully generated {output_file} with {len(class_names)} classes.")
        
    except FileNotFoundError:
        print(f"Error: The directory '{image_dir}' was not found.")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == "__main__":
    generate_classes_file()