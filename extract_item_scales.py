"""
Extract actual item scales from reference screenshots to apply precise scaling
in synthetic dataset generation.
"""

import cv2
import numpy as np
from PIL import Image
import os
import json

def extract_item_from_reference(reference_path, item_name, grid_position, item_size):
    """
    Extract a specific item from a reference screenshot at known grid position.
    
    Args:
        reference_path: Path to reference image
        item_name: Name of the item to extract
        grid_position: (row, col) position in grid
        item_size: (width, height) in grid cells
    
    Returns:
        Dictionary with item scaling information
    """
    img = cv2.imread(reference_path)
    if img is None:
        return None
    
    # Grid parameters from calibrated coordinates
    roi_x1, roi_y1, roi_x2, roi_y2 = 114, 80, 1129, 833
    grid_width = roi_x2 - roi_x1
    grid_height = roi_y2 - roi_y1
    cell_width = grid_width / 9
    cell_height = grid_height / 7
    
    # Calculate item position in pixels
    row, col = grid_position
    width_cells, height_cells = item_size
    
    # Item boundaries in the reference image
    item_x1 = roi_x1 + col * cell_width
    item_y1 = roi_y1 + row * cell_height
    item_x2 = item_x1 + width_cells * cell_width
    item_y2 = item_y1 + height_cells * cell_height
    
    # Extract the item region
    item_region = img[int(item_y1):int(item_y2), int(item_x1):int(item_x2)]
    
    # Save extracted item for inspection
    output_path = f"extracted_{item_name}_{reference_path.split('/')[-1]}"
    cv2.imwrite(output_path, item_region)
    
    # Calculate actual item dimensions within the grid area
    # Find the actual item boundaries by detecting non-background pixels
    gray = cv2.cvtColor(item_region, cv2.COLOR_BGR2GRAY)
    
    # Use edge detection to find item boundaries
    edges = cv2.Canny(gray, 50, 150)
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if contours:
        # Find the largest contour (should be the item)
        largest_contour = max(contours, key=cv2.contourArea)
        x, y, w, h = cv2.boundingRect(largest_contour)
        
        # Calculate scale factors
        grid_area_width = width_cells * cell_width
        grid_area_height = height_cells * cell_height
        
        scale_x = w / grid_area_width
        scale_y = h / grid_area_height
        
        return {
            'item_name': item_name,
            'reference_file': reference_path,
            'grid_position': grid_position,
            'grid_size': item_size,
            'actual_pixel_size': (w, h),
            'grid_area_size': (grid_area_width, grid_area_height),
            'scale_factors': (scale_x, scale_y),
            'padding_factor': min(scale_x, scale_y),  # Conservative estimate
            'extracted_region': output_path
        }
    
    return None

def analyze_reference_items():
    """
    Analyze specific items from reference screenshots to determine scaling.
    """
    # Define known items and their positions in reference images
    # This would need to be manually identified from the reference screenshots
    reference_items = [
        {
            'file': 'data/item_scale_reference/Item_reference1.png',
            'items': [
                # These positions would need to be manually identified
                # Format: (item_name, (row, col), (width_cells, height_cells))
                ('Dagger', (0, 0), (2, 1)),  # Example - needs actual positions
                ('Axe', (1, 0), (2, 2)),     # Example - needs actual positions
                ('Gem', (0, 2), (1, 1)),     # Example - needs actual positions
            ]
        }
        # Add more reference files and items as needed
    ]
    
    all_scales = []
    
    for ref_data in reference_items:
        ref_file = ref_data['file']
        if not os.path.exists(ref_file):
            print(f"Reference file not found: {ref_file}")
            continue
            
        for item_name, grid_pos, item_size in ref_data['items']:
            scale_info = extract_item_from_reference(ref_file, item_name, grid_pos, item_size)
            if scale_info:
                all_scales.append(scale_info)
                print(f"Extracted {item_name}: padding factor = {scale_info['padding_factor']:.3f}")
    
    if all_scales:
        # Calculate average padding factor
        avg_padding = sum(s['padding_factor'] for s in all_scales) / len(all_scales)
        print(f"\nAverage padding factor from reference items: {avg_padding:.3f}")
        
        # Save detailed analysis
        with open('reference_scale_analysis.json', 'w') as f:
            json.dump(all_scales, f, indent=2)
        
        return avg_padding
    
    return None

def create_precise_scaling_tool():
    """
    Create a tool to manually measure items in reference screenshots.
    """
    print("Creating precise scaling measurement tool...")
    
    # This would create an interactive tool to click on items in reference images
    # and measure their actual dimensions vs grid cell dimensions
    
    tool_code = '''
import cv2
import numpy as np

class ItemScaleMeasurer:
    def __init__(self, reference_image_path):
        self.image = cv2.imread(reference_image_path)
        self.reference_path = reference_image_path
        self.measurements = []
        
    def measure_item(self, item_name, top_left, bottom_right, grid_cells):
        """
        Measure an item given its pixel coordinates and grid cell count.
        """
        x1, y1 = top_left
        x2, y2 = bottom_right
        
        item_width = x2 - x1
        item_height = y2 - y1
        
        # Calculate expected grid size
        cell_width = 112.8  # From analysis
        cell_height = 107.6
        
        grid_width_cells, grid_height_cells = grid_cells
        expected_width = grid_width_cells * cell_width
        expected_height = grid_height_cells * cell_height
        
        scale_x = item_width / expected_width
        scale_y = item_height / expected_height
        
        measurement = {
            'item_name': item_name,
            'pixel_size': (item_width, item_height),
            'grid_cells': grid_cells,
            'expected_size': (expected_width, expected_height),
            'scale_factors': (scale_x, scale_y),
            'padding_factor': min(scale_x, scale_y)
        }
        
        self.measurements.append(measurement)
        return measurement

# Usage example:
# measurer = ItemScaleMeasurer('data/item_scale_reference/Item_reference1.png')
# measurer.measure_item('Dagger', (100, 150), (200, 200), (2, 1))
'''
    
    with open('item_scale_measurer.py', 'w') as f:
        f.write(tool_code)
    
    print("Created item_scale_measurer.py - use this to manually measure items")

def main():
    print("Extracting precise item scales from reference screenshots...")
    
    # First, create the measurement tool
    create_precise_scaling_tool()
    
    # Try to analyze any predefined items
    avg_padding = analyze_reference_items()
    
    if avg_padding:
        print(f"\nRecommended padding factor: {avg_padding:.3f}")
    else:
        print("\nNo predefined items found. Use the measurement tool to manually measure items.")
        print("Steps:")
        print("1. Open reference images in an image viewer")
        print("2. Identify item positions and sizes")
        print("3. Use item_scale_measurer.py to measure specific items")
        print("4. Calculate average padding factor from measurements")

if __name__ == "__main__":
    main()
