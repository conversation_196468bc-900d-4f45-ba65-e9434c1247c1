import argparse
import os
import random
import shutil
from PIL import Image
from simulator.core import load_all_item_data, Backpack, ItemInstance

def normalize_name(name):
    return name.replace('_', ' ').replace(':', '').replace("'", "").replace('.', '').lower()

def main(num_images):
    # --- Configuration ---
    db_path = "GameData.db"
    item_img_dir = "data/item_images"
    background_file = "data/screenshots/Empty inventory storage and shop.png"
    classes_file = "data/object_detection/classes.txt"
    output_img_dir = "data/object_detection/images"
    output_label_dir = "data/object_detection/labels"

    inventory_roi = (114, 80, 1129, 833) 
    grid_dims = (9, 7)
    
    shop_slot_rois = [
        (1920, 101, 2328, 352),
        (2003, 415, 2364, 638),
        (1609, 413, 2000, 652),
        (2024, 722, 2382, 942),
        (1611, 722, 1978, 946),
    ]
    # --- End Configuration ---

    print("Starting synthetic dataset generation...")

    # 1. Clean output directories
    for d in [output_img_dir, output_label_dir]:
        if os.path.exists(d):
            shutil.rmtree(d)
        os.makedirs(d)

    # 2. Load data
    print("Loading game data...")
    item_data = {normalize_name(k): v for k, v in load_all_item_data(db_path).items()}
    with open(classes_file, 'r') as f:
        class_names = [line.strip() for line in f]

    valid_item_names = [name for name in class_names if normalize_name(name) in item_data and item_data[normalize_name(name)].shape and os.path.exists(os.path.join(item_img_dir, f"{name}.png"))]
    print(f"Loaded {len(valid_item_names)} valid items with images and shapes.")
    
    if not valid_item_names:
        print("No valid items found. Exiting.")
        return

    background_template = Image.open(background_file).convert("RGB")
    bg_w, bg_h = background_template.size
    
    roi_x, roi_y, roi_x2, roi_y2 = inventory_roi
    roi_w = roi_x2 - roi_x
    roi_h = roi_y2 - roi_y
    cell_w = roi_w / grid_dims[0]
    cell_h = roi_h / grid_dims[1]

    # 3. Generate Images
    for i in range(num_images):
        background = background_template.copy()
        labels = []
        
        # Place in inventory
        backpack = Backpack(width=grid_dims[0], height=grid_dims[1])
        num_items_to_place = random.randint(5, 20)
        shuffled_items = random.sample(valid_item_names, k=min(len(valid_item_names), num_items_to_place))
        
        for item_name_with_underscores in shuffled_items:
            item_name = normalize_name(item_name_with_underscores)
            item = item_data[item_name]
            rotation = random.randint(0, 3)
            instance = ItemInstance(item, 0, 0, rotation)
            
            shape = instance.get_rotated_shape()
            item_grid_w, item_grid_h = len(shape[0]), len(shape)
            
            possible_coords = []
            for r in range(grid_dims[1] - item_grid_h + 1):
                for c in range(grid_dims[0] - item_grid_w + 1):
                    instance.x, instance.y = c, r
                    if backpack.can_place_item(instance):
                        possible_coords.append((c, r))
            
            if not possible_coords:
                continue

            instance.x, instance.y = random.choice(possible_coords)
            backpack.place_item(instance)
            
            item_img_path = os.path.join(item_img_dir, f"{item_name_with_underscores}.png")
            item_img = Image.open(item_img_path).convert("RGBA")
            item_img = item_img.rotate(rotation * -90, expand=True, resample=Image.BICUBIC)

            item_pixel_w = int(cell_w * item_grid_w)
            item_pixel_h = int(cell_h * item_grid_h)
            
            resized_item = item_img.resize((item_pixel_w, item_pixel_h), Image.LANCZOS)
            
            paste_x = int(roi_x + instance.x * cell_w)
            paste_y = int(roi_y + instance.y * cell_h)
            
            background.paste(resized_item, (paste_x, paste_y), resized_item)
            
            box_x_min = paste_x
            box_y_min = paste_y
            box_x_max = paste_x + item_pixel_w
            box_y_max = paste_y + item_pixel_h
            
            x_center = (box_x_min + box_x_max) / 2 / bg_w
            y_center = (box_y_min + box_y_max) / 2 / bg_h
            width = (box_x_max - box_x_min) / bg_w
            height = (box_y_max - box_y_min) / bg_h
            
            class_id = class_names.index(item_name_with_underscores)
            labels.append(f"{class_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}")

        # Place in shop
        for slot_roi in shop_slot_rois:
            if random.random() < 0.2:
                continue

            item_name_with_underscores = random.choice(valid_item_names)
            item_name = normalize_name(item_name_with_underscores)
            item = item_data[item_name]

            item_img = Image.open(os.path.join(item_img_dir, f"{item_name_with_underscores}.png")).convert("RGBA")
            
            shape = item.shape
            item_grid_w, item_grid_h = len(shape[0]), len(shape)

            item_pixel_w = int(cell_w * item_grid_w)
            item_pixel_h = int(cell_h * item_grid_h)

            resized_item = item_img.resize((item_pixel_w, item_pixel_h), Image.LANCZOS)
            
            slot_x, slot_y, slot_x2, slot_y2 = slot_roi
            slot_w = slot_x2 - slot_x
            slot_h = slot_y2 - slot_y

            paste_x = slot_x + (slot_w - item_pixel_w) // 2
            paste_y = slot_y + (slot_h - item_pixel_h) // 2
            
            background.paste(resized_item, (paste_x, paste_y), resized_item)

            box_x_min = paste_x
            box_y_min = paste_y
            box_x_max = paste_x + item_pixel_w
            box_y_max = paste_y + item_pixel_h

            x_center = (box_x_min + box_x_max) / 2 / bg_w
            y_center = (box_y_min + box_y_max) / 2 / bg_h
            width = (box_x_max - box_x_min) / bg_w
            height = (box_y_max - box_y_min) / bg_h
            
            class_id = class_names.index(item_name_with_underscores)
            labels.append(f"{class_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}")


        img_path = os.path.join(output_img_dir, f"synth_{i}.jpg")
        label_path = os.path.join(output_label_dir, f"synth_{i}.txt")
        
        background.save(img_path, "JPEG", quality=95)
        with open(label_path, 'w') as f:
            f.write("\n".join(labels))
            
        print(f"Generated image {i+1}/{num_images}")

    print(f"\nSynthetic dataset generation complete. {num_images} images generated.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Generate a synthetic dataset for YOLO object detection.")
    parser.add_argument("num_images", type=int, help="The number of synthetic images to generate.")
    args = parser.parse_args()
    main(args.num_images)