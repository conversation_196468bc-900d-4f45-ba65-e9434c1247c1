import argparse
import os
import random
import shutil
import json
from PIL import Image
from simulator.core import Backpack, ItemInstance, Item
from reference_scaling_system import get_corrected_item_dimensions

CALIBRATED_GRID_COORDINATES = {'top_left': (146, 118), 'top_right': (1023, 112), 'bottom_left': (142, 793), 'bottom_right': (1021, 793)}

def get_calibrated_cell(row, col, total_rows, total_cols):
    """
    Calculates the pixel coordinates of the center of a grid cell based on
    calibrated corner coordinates.
    """
    tl_x, tl_y = CALIBRATED_GRID_COORDINATES['top_left']
    tr_x, tr_y = CALIBRATED_GRID_COORDINATES['top_right']
    bl_x, bl_y = CALIBRATED_GRID_COORDINATES['bottom_left']
    br_x, br_y = CALIBRATED_GRID_COORDINATES['bottom_right']

    # Interpolate X and Y coordinates for the cell's center
    # X interpolation (horizontal position)
    x_top = tl_x + (tr_x - tl_x) * (col + 0.5) / total_cols
    x_bottom = bl_x + (br_x - bl_x) * (col + 0.5) / total_cols
    center_x = x_top + (x_bottom - x_top) * (row + 0.5) / total_rows

    # Y interpolation (vertical position)
    y_left = tl_y + (bl_y - tl_y) * (row + 0.5) / total_rows
    y_right = tr_y + (br_y - tr_y) * (row + 0.5) / total_cols
    center_y = y_left + (y_right - y_left) * (col + 0.5) / total_cols

    # Estimate cell dimensions (average width/height across the grid)
    avg_cell_width = ((tr_x - tl_x) + (br_x - bl_x)) / (2 * total_cols)
    avg_cell_height = ((bl_y - tl_y) + (br_y - tr_y)) / (2 * total_rows)

    return {
        'center_x': int(center_x),
        'center_y': int(center_y),
        'width': int(avg_cell_width),
        'height': int(avg_cell_height)
    }

def can_place_item(grid_row, grid_col, item_grid_width, item_grid_height):
    """
    Checks if an item of a given shape can logically be placed at a specific grid position.
    This is a basic check for bounds, not actual collision within a backpack model.
    """
    max_rows = 7  # Assuming a 9x7 grid, as used elsewhere, make this a variable later.
    max_cols = 9
    return (0 <= grid_col < max_cols - item_grid_width + 1 and
            0 <= grid_row < max_rows - item_grid_height + 1)

def get_item_placement_coords(grid_row, grid_col, item_grid_width, item_grid_height):
    """
    Calculates the consolidated pixel coordinates (center, width, height) for an item
    spanning multiple grid cells.
    """
    # Assuming a 9x7 grid
    total_cols = 9
    total_rows = 7

    # Get the calibrated cell info for the top-left cell of the item's grid occupancy
    start_cell_info = get_calibrated_cell(grid_row, grid_col, total_rows, total_cols)
    
    # Get the calibrated cell info for the bottom-right cell of the item's grid occupancy
    end_cell_info = get_calibrated_cell(grid_row + item_grid_height - 1, grid_col + item_grid_width - 1, total_rows, total_cols)

    # Calculate the actual pixel boundaries of the item's footprint
    item_left_pixel = start_cell_info['center_x'] - (start_cell_info['width'] / 2)
    item_right_pixel = end_cell_info['center_x'] + (end_cell_info['width'] / 2)
    item_top_pixel = start_cell_info['center_y'] - (start_cell_info['height'] / 2)
    item_bottom_pixel = end_cell_info['center_y'] + (end_cell_info['height'] / 2)

    # Calculate the center of the actual pixel area spanned by the item
    # Use the actual boundaries, not just the average of cell centers
    center_x = (item_left_pixel + item_right_pixel) / 2
    center_y = (item_top_pixel + item_bottom_pixel) / 2
    
    # Calculate total pixel width and height
    width = int(item_right_pixel - item_left_pixel)
    height = int(item_bottom_pixel - item_top_pixel)

    return {
        'center_x': int(center_x),
        'center_y': int(center_y),
        'width': width,
        'height': height
    }

def convert_grid_layout_to_shape(grid_layout):
    """
    Converts the JSON grid_layout (already a 2D list) to a compact, numeric shape.
    It treats any non-zero or non-empty cell as part of the shape.
    """
    if not grid_layout or not isinstance(grid_layout, list) or not grid_layout[0]:
        return [[1]] # Default to 1x1 if layout is invalid or empty

    # Ensure it's treated as a 2D list of lists, even if technically 1D
    if not isinstance(grid_layout[0], list):
        grid_layout = [grid_layout]

    # Find the bounding box of all non-empty cells
    # This logic assumes cells contain 0 for empty and 1 for filled
    filled_cells = []
    for r_idx, row in enumerate(grid_layout):
        for c_idx, cell_value in enumerate(row):
            if cell_value == 1: # Assuming 1 indicates an occupied cell
                filled_cells.append((r_idx, c_idx))

    if not filled_cells:
        return [[1]] # Fallback if no filled cells are found

    # Find the bounding box of all non-empty cells
    min_r = min(pos[0] for pos in filled_cells)
    max_r = max(pos[0] for pos in filled_cells)
    min_c = min(pos[1] for pos in filled_cells)
    max_c = max(pos[1] for pos in filled_cells)

    # Create the shape array
    shape = []
    for r in range(min_r, max_r + 1):
        row_shape = []
        for c in range(min_c, max_c + 1):
            if (r, c) in filled_cells:
                row_shape.append(1)
            else:
                row_shape.append(0)
        shape.append(row_shape)

    return shape

def load_item_data_from_json(json_path):
    """
    Load item data from the JSON file and create Item objects with proper shapes.

    Args:
        json_path: Path to the Backpack Battle Items.json file

    Returns:
        Dictionary mapping item names to Item objects
    """
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    items = {}
    
    # Check if 'enhanced_items' key exists and is a list
    if 'enhanced_items' in data and isinstance(data['enhanced_items'], list):
        for item_data_dict in data['enhanced_items']:
            name = item_data_dict.get('name')
            if name:
                shape = item_data_dict.get('shape', [[1]])
                processed_shape = convert_grid_layout_to_shape(shape)
                
                item_obj = Item(data={
                    'id': len(items) + 1,
                    'name': name,
                    'rarity': item_data_dict.get('rarity'),
                    'cost': item_data_dict.get('cost'),
                    'item_class': item_data_dict.get('class'),
                    'item_type': item_data_dict.get('type'),
                    'shape': processed_shape,
                    'description': item_data_dict.get('description', ''),
                    'raw_stats': item_data_dict.get('raw_stats', {}),
                    'synergy_triggers': item_data_dict.get('synergy_triggers', []),
                    'synergy_effects': item_data_dict.get('synergy_effects', []),
                })
                items[name] = item_obj
            else:
                print(f"Warning: Item dictionary found in 'enhanced_items' list without a 'name' key: {item_data_dict}")
    else:
        # Fallback for older JSON structures or if 'enhanced_items' is not a list/dict
        for name, item_data_dict in data.items():
            # Ensure item_data_dict is a dictionary before using .get()
            if isinstance(item_data_dict, dict):
                shape = item_data_dict.get('shape', [[1]])
                processed_shape = convert_grid_layout_to_shape(shape)
                
                item_obj = Item(data={
                    'id': len(items) + 1,
                    'name': name,
                    'rarity': item_data_dict.get('rarity'),
                    'cost': item_data_dict.get('cost'),
                    'item_class': item_data_dict.get('class'),
                    'item_type': item_data_dict.get('type'),
                    'shape': processed_shape,
                    'description': item_data_dict.get('description', ''),
                    'raw_stats': item_data_dict.get('raw_stats', {}),
                    'synergy_triggers': item_data_dict.get('synergy_triggers', []),
                    'synergy_effects': item_data_dict.get('synergy_effects', []),
                })
                items[name] = item_obj
            else:
                print(f"Warning: Top-level item '{name}' is not a dictionary: {item_data_dict}")
    return items

def name_to_filename(name):
    """
    Convert item name to filename format (spaces to underscores).

    Args:
        name: Item name with spaces (e.g., "Hero Sword")

    Returns:
        Filename format (e.g., "Hero_Sword")
    """
    return name.replace(' ', '_').replace(':', '').replace("'", "").replace('.', '')

def filename_to_name(filename):
    """
    Convert filename format to item name (underscores to spaces, handle special cases).

    Args:
        filename: Filename format (e.g., "Hero_Sword", "Artifact_Stone_Cold")

    Returns:
        Item name with spaces (e.g., "Hero Sword", "Artifact Stone: Cold")
    """
    # Handle special cases with colons
    name = filename.replace('_', ' ')

    # Special mappings for items with colons, periods, apostrophes, or other special characters
    special_mappings = {
        'Artifact Stone Cold': 'Artifact Stone: Cold',
        'Artifact Stone Death': 'Artifact Stone: Death',
        'Artifact Stone Heat': 'Artifact Stone: Heat',
        'Belladonna27s Shade': "Belladonna's Shade",
        'Belladonna27s Whisper': "Belladonna's Whisper",
        'Fortuna27s Grace': "Fortuna's Grace",
        'Fortuna27s Hope': "Fortuna's Hope",
        'Shepherd27s Crook': "Shepherds Crook",  # No apostrophe in JSON
        'Mr Struggles': 'Mr. Struggles',
        'Mrs Struggles': 'Mrs. Struggles',
        'Reverse': 'Reverse!'  # Has exclamation mark in JSON
    }

    return special_mappings.get(name, name)

def main(num_images):
    # --- Configuration ---
    json_path = "data/Backpack Battle Items.json" # Corrected path
    item_img_dir = "data/item_images"
    background_file = "data/screenshots/Empty inventory storage and shop.png"
    classes_file = "data/object_detection/classes.txt"
    output_img_dir = "data/object_detection/images"
    output_label_dir = "data/object_detection/labels"

    inventory_roi = (114, 80, 1129, 833)
    grid_dims = (9, 7)

    shop_slot_rois = [
        (1920, 101, 2328, 352),
        (2003, 415, 2364, 638),
        (1609, 413, 2000, 652),
        (2024, 722, 2382, 942),
        (1611, 722, 1978, 946),
    ]
    shop_slot_rois = [
        (1920, 101, 2328, 352),
        (2003, 415, 2364, 638),
        (1609, 413, 2000, 652),
        (2024, 722, 2382, 942),
        (1611, 722, 1978, 946),
    ]
    # --- End Configuration ---

    print("Starting synthetic dataset generation...")

    # 1. Clean output directories
    for d in [output_img_dir, output_label_dir]:
        if os.path.exists(d):
            shutil.rmtree(d)
        os.makedirs(d)

    # 2. Load data
    print("Loading game data from JSON...")
    item_data = load_item_data_from_json(json_path)
    print(f"Loaded {len(item_data)} items from JSON.")

    # Load class names (these are in filename format with underscores)
    with open(classes_file, 'r') as f:
        class_names = [line.strip() for line in f if line.strip()]

    print(f"Loaded {len(class_names)} class names.")

    # Find valid items: must have JSON data, image file, and be in classes
    valid_item_names = []
    for class_name in class_names:
        # Convert class name (filename format) to item name (space format)
        item_name = filename_to_name(class_name)

        # Check if item exists in JSON data
        if item_name in item_data:
            # Check if image file exists
            image_path = os.path.join(item_img_dir, f"{class_name}.png")
            if os.path.exists(image_path):
                # Check if item has a valid shape
                item = item_data[item_name]
                if item.shape and len(item.shape) > 0 and len(item.shape[0]) > 0:
                    valid_item_names.append(class_name)  # Store in filename format
                else:
                    print(f"Warning: Item '{item_name}' has invalid shape: {item.shape}")
            else:
                print(f"Warning: Image not found for '{class_name}' at {image_path}")
        else:
            print(f"Warning: Item '{item_name}' not found in JSON data")

    print(f"Found {len(valid_item_names)} valid items with images and shapes.")
    print("Using reference-based scaling from analyzed screenshots.")

    if not valid_item_names:
        print("No valid items found. Exiting.")
        return

    background_template = Image.open(background_file).convert("RGB")
    bg_w, bg_h = background_template.size
    
    # Using calibrated grid coordinates instead of calculated cell dimensions
    # roi_x, roi_y, roi_x2, roi_y2 = inventory_roi  # Not needed anymore
    # roi_w = roi_x2 - roi_x  # Not needed anymore
    # roi_h = roi_y2 - roi_y  # Not needed anymore
    # cell_w = roi_w / grid_dims[0]  # Not needed anymore
    # cell_h = roi_h / grid_dims[1]  # Not needed anymore

    # 3. Generate Images
    for i in range(num_images):
        background = background_template.copy()
        labels = []
        
        # Place in inventory
        backpack = Backpack(width=grid_dims[0], height=grid_dims[1])
        num_items_to_place = random.randint(5, 20)
        shuffled_items = random.sample(valid_item_names, k=min(len(valid_item_names), num_items_to_place))
        
        for class_name in shuffled_items:
            # Convert class name (filename format) to item name (space format)
            item_name = filename_to_name(class_name)
            item = item_data[item_name]
            rotation = random.randint(0, 3)
            instance = ItemInstance(item, 0, 0, rotation)

            shape = instance.get_rotated_shape()
            if not shape or not shape[0]:
                print(f"Warning: Invalid shape for item '{item_name}': {shape}")
                continue

            item_grid_w, item_grid_h = len(shape[0]), len(shape)

            # Find valid placement positions using calibrated grid
            possible_coords = []
            for r in range(grid_dims[1] - item_grid_h + 1):
                for c in range(grid_dims[0] - item_grid_w + 1):
                    # Check if item can be placed at this grid position
                    if can_place_item(r, c, item_grid_w, item_grid_h):
                        instance.x, instance.y = c, r
                        if backpack.can_place_item(instance):
                            possible_coords.append((c, r))

            if not possible_coords:
                continue

            # Choose random valid position
            grid_col, grid_row = random.choice(possible_coords)
            instance.x, instance.y = grid_col, grid_row
            backpack.place_item(instance)
            # Get precise placement coordinates from calibrated grid
            placement_coords = get_item_placement_coords(grid_row, grid_col, item_grid_w, item_grid_h)
            if not placement_coords:
                print(f"Warning: Could not get placement coordinates for {item_name} at ({grid_row}, {grid_col})")
                continue

            item_img_path = os.path.join(item_img_dir, f"{class_name}.png")
            item_img = Image.open(item_img_path).convert("RGBA")

            # Use reference-based scaling instead of grid-based scaling
            original_w, original_h = item_img.size
            scaled_w, scaled_h, scale_factor = get_corrected_item_dimensions(
                class_name.replace('_', ' '),  # Convert filename to item name
                item_grid_w,
                item_grid_h,
                (original_w, original_h)
            )

            resized_item = item_img.resize((scaled_w, scaled_h), Image.LANCZOS)
            # Apply rotation after scaling
            if rotation > 0:
                resized_item = resized_item.rotate(rotation * -90, expand=True, resample=Image.BICUBIC)
                # Update dimensions after rotation
                scaled_w, scaled_h = resized_item.size
            
            # Center the scaled item within the calibrated grid area
            paste_x = placement_coords['center_x'] - scaled_w // 2
            paste_y = placement_coords['center_y'] - scaled_h // 2

            background.paste(resized_item, (paste_x, paste_y), resized_item)

            # Bounding box for YOLO labels (use actual item size, not grid area)
            box_x_min = paste_x
            box_y_min = paste_y
            box_x_max = paste_x + scaled_w
            box_y_max = paste_y + scaled_h

            x_center = (box_x_min + box_x_max) / 2 / bg_w
            y_center = (box_y_min + box_y_max) / 2 / bg_h
            width = (box_x_max - box_x_min) / bg_w
            height = (box_y_max - box_y_min) / bg_h

            class_id = class_names.index(class_name)
            labels.append(f"{class_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}")


        img_path = os.path.join(output_img_dir, f"synth_{i}.jpg")
        label_path = os.path.join(output_label_dir, f"synth_{i}.txt")
        
        background.save(img_path, "JPEG", quality=95)
        with open(label_path, 'w') as f:
            f.write("\n".join(labels))
            
        print(f"Generated image {i+1}/{num_images}")

    print(f"\nSynthetic dataset generation complete. {num_images} images generated.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Generate a synthetic dataset for YOLO object detection.")
    parser.add_argument("num_images", type=int, help="The number of synthetic images to generate.")
    args = parser.parse_args()
    main(args.num_images)