import argparse
import os
import random
import shutil
import json
from PIL import Image
from simulator.core import Backpack, ItemInstance, Item

def convert_grid_layout_to_shape(grid_layout):
    """
    Convert JSON grid_layout format (with 'cell' and 'star' strings)
    to numeric format (1s and 0s) expected by the Item class.

    Args:
        grid_layout: List of lists containing 'cell' and 'star' strings

    Returns:
        List of lists containing 1s and 0s (1 for 'cell', 0 for 'star')
    """
    if not grid_layout or not grid_layout[0]:
        return [[1]]  # Default to 1x1 if no layout provided

    numeric_shape = []
    for row in grid_layout:
        numeric_row = []
        for cell in row:
            if cell == 'cell':
                numeric_row.append(1)
            else:  # 'star' or any other value
                numeric_row.append(0)
        numeric_shape.append(numeric_row)

    return numeric_shape

def load_item_data_from_json(json_path):
    """
    Load item data from the JSON file and create Item objects with proper shapes.

    Args:
        json_path: Path to the Backpack Battle Items.json file

    Returns:
        Dictionary mapping item names to Item objects
    """
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    items = {}
    for item_data in data.get('enhanced_items', []):
        name = item_data.get('name', '')
        if not name:
            continue

        # Convert grid_layout to numeric shape
        grid_layout = item_data.get('grid_layout', [[1]])
        shape = convert_grid_layout_to_shape(grid_layout)

        # Create Item object with the shape data
        item_dict = {
            'id': len(items) + 1,
            'name': name,
            'rarity': item_data.get('rarity', ''),
            'cost': 0,  # Default cost
            'item_class': item_data.get('type', ''),
            'item_type': item_data.get('type', ''),
            'shape': shape,
            'description': item_data.get('effect', ''),
            'raw_stats': {},
            'synergy_triggers': {},
            'synergy_effects': {}
        }

        item = Item(item_dict)
        items[name] = item

    return items

def name_to_filename(name):
    """
    Convert item name to filename format (spaces to underscores).

    Args:
        name: Item name with spaces (e.g., "Hero Sword")

    Returns:
        Filename format (e.g., "Hero_Sword")
    """
    return name.replace(' ', '_').replace(':', '').replace("'", "").replace('.', '')

def filename_to_name(filename):
    """
    Convert filename format to item name (underscores to spaces).

    Args:
        filename: Filename format (e.g., "Hero_Sword")

    Returns:
        Item name with spaces (e.g., "Hero Sword")
    """
    return filename.replace('_', ' ')

def main(num_images):
    # --- Configuration ---
    json_path = "Backpack Battle Items.json"
    item_img_dir = "data/item_images"
    background_file = "data/screenshots/Empty inventory storage and shop.png"
    classes_file = "data/object_detection/classes.txt"
    output_img_dir = "data/object_detection/images"
    output_label_dir = "data/object_detection/labels"

    inventory_roi = (114, 80, 1129, 833)
    grid_dims = (9, 7)

    shop_slot_rois = [
        (1920, 101, 2328, 352),
        (2003, 415, 2364, 638),
        (1609, 413, 2000, 652),
        (2024, 722, 2382, 942),
        (1611, 722, 1978, 946),
    ]
    # --- End Configuration ---

    print("Starting synthetic dataset generation...")

    # 1. Clean output directories
    for d in [output_img_dir, output_label_dir]:
        if os.path.exists(d):
            shutil.rmtree(d)
        os.makedirs(d)

    # 2. Load data
    print("Loading game data from JSON...")
    item_data = load_item_data_from_json(json_path)
    print(f"Loaded {len(item_data)} items from JSON.")

    # Load class names (these are in filename format with underscores)
    with open(classes_file, 'r') as f:
        class_names = [line.strip() for line in f if line.strip()]

    print(f"Loaded {len(class_names)} class names.")

    # Find valid items: must have JSON data, image file, and be in classes
    valid_item_names = []
    for class_name in class_names:
        # Convert class name (filename format) to item name (space format)
        item_name = filename_to_name(class_name)

        # Check if item exists in JSON data
        if item_name in item_data:
            # Check if image file exists
            image_path = os.path.join(item_img_dir, f"{class_name}.png")
            if os.path.exists(image_path):
                # Check if item has a valid shape
                item = item_data[item_name]
                if item.shape and len(item.shape) > 0 and len(item.shape[0]) > 0:
                    valid_item_names.append(class_name)  # Store in filename format
                else:
                    print(f"Warning: Item '{item_name}' has invalid shape: {item.shape}")
            else:
                print(f"Warning: Image not found for '{class_name}' at {image_path}")
        else:
            print(f"Warning: Item '{item_name}' not found in JSON data")

    print(f"Found {len(valid_item_names)} valid items with images and shapes.")

    if not valid_item_names:
        print("No valid items found. Exiting.")
        print("Debug info:")
        print(f"  - JSON items: {len(item_data)}")
        print(f"  - Class names: {len(class_names)}")
        print(f"  - Sample JSON items: {list(item_data.keys())[:5]}")
        print(f"  - Sample class names: {class_names[:5]}")
        return

    background_template = Image.open(background_file).convert("RGB")
    bg_w, bg_h = background_template.size
    
    roi_x, roi_y, roi_x2, roi_y2 = inventory_roi
    roi_w = roi_x2 - roi_x
    roi_h = roi_y2 - roi_y
    cell_w = roi_w / grid_dims[0]
    cell_h = roi_h / grid_dims[1]

    # 3. Generate Images
    for i in range(num_images):
        background = background_template.copy()
        labels = []
        
        # Place in inventory
        backpack = Backpack(width=grid_dims[0], height=grid_dims[1])
        num_items_to_place = random.randint(5, 20)
        shuffled_items = random.sample(valid_item_names, k=min(len(valid_item_names), num_items_to_place))
        
        for class_name in shuffled_items:
            # Convert class name (filename format) to item name (space format)
            item_name = filename_to_name(class_name)
            item = item_data[item_name]
            rotation = random.randint(0, 3)
            instance = ItemInstance(item, 0, 0, rotation)

            shape = instance.get_rotated_shape()
            if not shape or not shape[0]:
                print(f"Warning: Invalid shape for item '{item_name}': {shape}")
                continue

            item_grid_w, item_grid_h = len(shape[0]), len(shape)

            possible_coords = []
            for r in range(grid_dims[1] - item_grid_h + 1):
                for c in range(grid_dims[0] - item_grid_w + 1):
                    instance.x, instance.y = c, r
                    if backpack.can_place_item(instance):
                        possible_coords.append((c, r))

            if not possible_coords:
                continue

            instance.x, instance.y = random.choice(possible_coords)
            backpack.place_item(instance)

            item_img_path = os.path.join(item_img_dir, f"{class_name}.png")
            item_img = Image.open(item_img_path).convert("RGBA")
            item_img = item_img.rotate(rotation * -90, expand=True, resample=Image.BICUBIC)

            item_pixel_w = int(cell_w * item_grid_w)
            item_pixel_h = int(cell_h * item_grid_h)

            resized_item = item_img.resize((item_pixel_w, item_pixel_h), Image.LANCZOS)

            paste_x = int(roi_x + instance.x * cell_w)
            paste_y = int(roi_y + instance.y * cell_h)

            background.paste(resized_item, (paste_x, paste_y), resized_item)

            box_x_min = paste_x
            box_y_min = paste_y
            box_x_max = paste_x + item_pixel_w
            box_y_max = paste_y + item_pixel_h

            x_center = (box_x_min + box_x_max) / 2 / bg_w
            y_center = (box_y_min + box_y_max) / 2 / bg_h
            width = (box_x_max - box_x_min) / bg_w
            height = (box_y_max - box_y_min) / bg_h

            class_id = class_names.index(class_name)
            labels.append(f"{class_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}")

        # Place in shop
        for slot_roi in shop_slot_rois:
            if random.random() < 0.2:
                continue

            class_name = random.choice(valid_item_names)
            item_name = filename_to_name(class_name)
            item = item_data[item_name]

            item_img = Image.open(os.path.join(item_img_dir, f"{class_name}.png")).convert("RGBA")

            shape = item.shape
            if not shape or not shape[0]:
                print(f"Warning: Invalid shape for shop item '{item_name}': {shape}")
                continue

            item_grid_w, item_grid_h = len(shape[0]), len(shape)

            item_pixel_w = int(cell_w * item_grid_w)
            item_pixel_h = int(cell_h * item_grid_h)

            resized_item = item_img.resize((item_pixel_w, item_pixel_h), Image.LANCZOS)

            slot_x, slot_y, slot_x2, slot_y2 = slot_roi
            slot_w = slot_x2 - slot_x
            slot_h = slot_y2 - slot_y

            paste_x = slot_x + (slot_w - item_pixel_w) // 2
            paste_y = slot_y + (slot_h - item_pixel_h) // 2

            background.paste(resized_item, (paste_x, paste_y), resized_item)

            box_x_min = paste_x
            box_y_min = paste_y
            box_x_max = paste_x + item_pixel_w
            box_y_max = paste_y + item_pixel_h

            x_center = (box_x_min + box_x_max) / 2 / bg_w
            y_center = (box_y_min + box_y_max) / 2 / bg_h
            width = (box_x_max - box_x_min) / bg_w
            height = (box_y_max - box_y_min) / bg_h

            class_id = class_names.index(class_name)
            labels.append(f"{class_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}")


        img_path = os.path.join(output_img_dir, f"synth_{i}.jpg")
        label_path = os.path.join(output_label_dir, f"synth_{i}.txt")
        
        background.save(img_path, "JPEG", quality=95)
        with open(label_path, 'w') as f:
            f.write("\n".join(labels))
            
        print(f"Generated image {i+1}/{num_images}")

    print(f"\nSynthetic dataset generation complete. {num_images} images generated.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Generate a synthetic dataset for YOLO object detection.")
    parser.add_argument("num_images", type=int, help="The number of synthetic images to generate.")
    args = parser.parse_args()
    main(args.num_images)