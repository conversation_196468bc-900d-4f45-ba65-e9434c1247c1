# Plan: Inventory Optimization Engine

This document outlines the design for an engine that optimizes the placement of items in a player's backpack to maximize combat effectiveness.

## 1. Core Objective & Fitness Function

The engine's goal is to find the optimal arrangement of items in a `Backpack`. The fitness of any given arrangement will be determined by its win rate in the `BattleSimulator`. A higher win rate against a standardized opponent indicates a better layout.

## 2. Optimization Algorithm: Genetic Algorithm (GA)

A Genetic Algorithm is the chosen approach. Here's why it's a good fit:

*   **Complex Search Space:** The number of possible item layouts (combinations of positions and rotations) is enormous. A GA can intelligently explore this space without exhaustively checking every possibility.
*   **Non-Linearity:** The fitness function (the battle simulator) is a black box. The relationship between an item's position and the battle outcome is not simple or linear. GAs are effective even when the fitness landscape is unpredictable.
*   **Parallelism:** The fitness of each individual in a generation can be evaluated independently, which allows for significant parallelization to speed up the optimization process.

The GA will work as follows:

1.  **Initialization:** Create an initial population of `N` random, valid backpack layouts (the "genomes").
2.  **Evaluation:** For each layout, run `M` battle simulations against a fixed opponent and calculate the win rate (the "fitness").
3.  **Selection:** Select the best-performing layouts from the population to be "parents" for the next generation.
4.  **Crossover:** Create new layouts ("offspring") by combining the features of two parent layouts. A simple crossover could involve taking a subset of item placements from each parent.
5.  **Mutation:** Introduce small, random changes into the offspring layouts (e.g., moving an item, rotating an item) to maintain genetic diversity and avoid getting stuck in local optima.
6.  **Repeat:** Repeat steps 2-5 for a set number of generations or until the fitness of the best layout stops improving.

## 3. System Architecture

I propose the following components, illustrated by this diagram:

```mermaid
graph TD
    A[Optimizer] --> B{Layout};
    B --> C[Evaluator];
    C --> D[BattleSimulator];
    D -- Battle Result --> C;
    C -- Fitness Score --> A;
    A -- Creates new generation --> B;

    subgraph "Optimization Core"
        A
        B
        C
    end

    subgraph "Existing Simulation"
        D
        E[Player]
        F[Backpack]
        G[ItemInstance]
    end

    B -- Contains --> G;
    E -- Has a --> F;
    F -- Contains --> G;
    D -- Simulates battle between --> E;
```

**Component Responsibilities:**

*   **`Layout` Class:**
    *   Represents a single, complete, and valid backpack arrangement.
    *   Holds a list of `ItemInstance` objects.
    *   Has a method to check its own validity (no overlapping items).
    *   Can be created randomly for the initial population.
    *   Can be created through crossover and mutation operations.

*   **`Evaluator` Class:**
    *   Takes a `Layout` object as input.
    *   Constructs two `Player` objects: one with the layout to be tested, and a second "standard" opponent.
    *   Runs the `BattleSimulator` a configurable number of times (`M`).
    *   Returns a fitness score (win rate) for the `Layout`.

*   **`Optimizer` Class:**
    *   The main orchestrator.
    *   Takes a list of `Item`s to be arranged as input.
    *   Initializes the population of `Layout`s.
    *   Runs the main GA loop: evaluation, selection, crossover, mutation.
    *   Uses the `Evaluator` to assess the fitness of each `Layout`.
    *   Keeps track of the best `Layout` found so far.
    *   Outputs the best `Layout` after the process completes.

## 4. Data Flow

1.  **Input:** The `Optimizer` receives a list of `Item` objects that need to be arranged.
2.  **Initialization:** The `Optimizer` creates an initial population of `Layout` objects, where each `Layout` represents one possible valid arrangement of the input items.
3.  **Evaluation Loop:**
    *   For each `Layout` in the population, the `Optimizer` passes it to the `Evaluator`.
    *   The `Evaluator` creates a `Player` and populates its `Backpack` according to the `Layout`.
    *   The `Evaluator` runs, for example, 100 simulations using `BattleSimulator.run_battle()`.
    *   The `Evaluator` calculates the win percentage and returns it as the fitness score to the `Optimizer`.
4.  **Evolution:** The `Optimizer` uses the fitness scores to perform selection, crossover, and mutation, creating a new generation of `Layout`s.
5.  **Termination:** The process repeats for a fixed number of generations.
6.  **Output:** The `Optimizer` returns the single `Layout` with the highest fitness score from all generations.