<html class="client-js theme-none view-unknown skin-theme-clientpref-day" lang="en" dir="ltr"><head>
<meta charset="UTF-8">
<title>Healing Herbs - The Backpack Battles Wiki</title>
<script src="https://www.googletagmanager.com/gtag/js?id=G-0CPE0JFSCT" async="" nnid="nn-77ed23dc"></script><script src="https://scripts.webcontentassessor.com/scripts/fa866076b638fb98d1cc84e16435f506c69ce3f3d6433bb2591568441bb1a2ca" nnid="nn-486e1073"></script><script src="https://kumo.network-n.com/dist/1.59.0/quantcast.js" async="" nnid="nn-3f150c98"></script><script src="https://kumo.network-n.com/dist/1.59.0/celtra-bfab.js" async="" nnid="nn-658b6c1f"></script><script src="https://kumo.network-n.com/dist/1.59.0/analytics.js" async="" nnid="nn-20a59d94"></script><script src="https://kumo.network-n.com/dist/1.59.0/pubstack.js" async="" nnid="nn-44d62cd7"></script><script src="https://kumo.network-n.com/dist/1.59.0/adengine.js" async="" nnid="nn-2327b191"></script><script src="https://kumo.network-n.com/dist/1.59.0/comscore.js" async="" nnid="nn-662f6d63"></script><script src="https://kumo.network-n.com/dist/1.59.0/mediatrust.js" async="" nnid="nn-2cb6d000"></script><script src="https://kumo.network-n.com/dist/1.59.0/blockthrough.js" async="" nnid="nn-784c0e64"></script><script src="https://kumo.network-n.com/dist/1.59.0/cmp-sourcepoint.js" async="" nnid="nn-78908d8c"></script><script type="text/javascript" async="" src="https://www.googletagmanager.com/gtag/js?id=G-ZS8NKMS9PL&amp;cx=c&amp;gtm=45He56g0h1v9200401626za200&amp;tag_exp=101509157~103116026~103200004~103233427~103351869~103351871~104684208~104684211~104718208~104791498~104791500"></script><script type="text/javascript" async="" src="https://www.googletagmanager.com/gtag/js?id=G-68B3H7K5M9&amp;cx=c&amp;gtm=45He56g0h1v9200401626za200&amp;tag_exp=101509157~103116026~103200004~103233427~103351869~103351871~104684208~104684211~104718208~104791498~104791500"></script><script async="" src="https://www.googletagmanager.com/gtm.js?id=GTM-P68LVFKN"></script><script>(function(){var className="client-js view-unknown theme-none skin-theme-clientpref-day";var cookie=document.cookie.match(/(?:^|; )commons_enmwclientpreferences=([^;]+)/);if(cookie){cookie[1].split('%2C').forEach(function(pref){className=className.replace(new RegExp('(^| )'+pref.replace(/-clientpref-\w+$|[^\w-]+/g,'')+'-clientpref-\\w+( |$)'),'$1'+pref+'$2');});}document.documentElement.className=className;}());RLCONF={"wgBreakFrames":false,"wgSeparatorTransformTable":["",""],"wgDigitTransformTable":["",""],"wgDefaultDateFormat":"dmy","wgMonthNames":["","January","February","March","April","May","June","July","August","September","October","November","December"],"wgRequestId":"699de5157f0e755ad3bc1aca","wgCanonicalNamespace":"","wgCanonicalSpecialPageName":false,"wgNamespaceNumber":0,"wgPageName":"Healing_Herbs","wgTitle":"Healing Herbs","wgCurRevisionId":10140,"wgRevisionId":10140,"wgArticleId":175,"wgIsArticle":true,"wgIsRedirect":false,"wgAction":"view","wgUserName":null,"wgUserGroups":["*"],"wgCategories":["Item","Common","Accessory","Nature","Regeneration"],"wgPageViewLanguage":"en","wgPageContentLanguage":"en","wgPageContentModel":"wikitext","wgRelevantPageName":"Healing_Herbs","wgRelevantArticleId":175,"wgIsProbablyEditable":false,"wgRelevantPageIsProbablyEditable":false,"wgRestrictionEdit":[],"wgRestrictionMove":[],"wgPageFormsTargetName":null,"wgPageFormsAutocompleteValues":[],"wgPageFormsAutocompleteOnAllChars":false,"wgPageFormsFieldProperties":[],"wgPageFormsCargoFields":[],"wgPageFormsDependentFields":[],"wgPageFormsCalendarValues":[],"wgPageFormsCalendarParams":[],"wgPageFormsCalendarHTML":null,"wgPageFormsGridValues":[],"wgPageFormsGridParams":[],"wgPageFormsContLangYes":null,"wgPageFormsContLangNo":null,"wgPageFormsContLangMonths":[],"wgPageFormsHeightForMinimizingInstances":800,"wgPageFormsDelayReload":false,"wgPageFormsShowOnSelect":[],"wgPageFormsScriptPath":"/mw-1.43/extensions/PageForms","edgValues":[],"wgPageFormsEDSettings":null,"wgAmericanDates":false,"wgCargoDefaultQueryLimit":2000,"wgCargoMonthNamesShort":["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],"wgMediaViewerOnClick":true,"wgMediaViewerEnabledByDefault":true,"wgCiteReferencePreviewsActive":true,"wgCheckUserClientHintsHeadersJsApi":["architecture","bitness","brands","fullVersionList","mobile","model","platform","platformVersion"]};
RLSTATE={"ext.globalCssJs.user.styles":"ready","site.styles":"ready","user.styles":"ready","ext.globalCssJs.user":"ready","user":"ready","user.options":"loading","skins.vector.styles.legacy":"ready","jquery.makeCollapsible.styles":"ready","ext.CookieWarning.styles":"ready","oojs-ui-core.styles":"ready","oojs-ui.styles.indicators":"ready","mediawiki.widgets.styles":"ready","oojs-ui-core.icons":"ready","ext.removeredlinks.styles":"ready","ext.usergroupbadges.styles":"ready","ext.globalui.styles":"ready"};RLPAGEMODULES=["ext.cargo.main","site","mediawiki.page.ready","jquery.makeCollapsible","skins.vector.legacy.js","ext.CookieWarning","ext.removeredlinks","ext.themes.switcher","ext.gadget.interwikiDropdownButton","mmv.bootstrap","ext.checkUser.clientHints","ext.cargo.purge","ext.globalui"];</script>
<script>(RLQ=window.RLQ||[]).push(function(){mw.loader.impl(function(){return["user.options@12s5i",function($,jQuery,require,module){mw.user.tokens.set({"patrolToken":"+\\","watchToken":"+\\","csrfToken":"+\\"});
}];});});</script>
<link rel="stylesheet" href="/load.php?lang=en&amp;modules=ext.CookieWarning.styles%7Cext.globalui.styles%7Cext.removeredlinks.styles%7Cext.usergroupbadges.styles%7Cjquery.makeCollapsible.styles%7Cmediawiki.widgets.styles%7Coojs-ui-core.icons%2Cstyles%7Coojs-ui.styles.indicators%7Cskins.vector.styles.legacy&amp;only=styles&amp;skin=vector">
<script async="" src="/load.php?lang=en&amp;modules=startup&amp;only=scripts&amp;raw=1&amp;skin=vector"></script>
<style>
.cdx-button{display:inline-flex;align-items:center;justify-content:center;gap:4px;box-sizing:border-box;min-height:32px;max-width:28rem;margin:0;border-width:1px;border-style:solid;border-radius:2px;padding-right:11px;padding-left:11px;font-family:inherit;font-size:inherit;font-weight:700;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;text-transform:none;transition-property:background-color,color,border-color,box-shadow;transition-duration:.1s}.cdx-button--size-large{min-height:44px;padding-right:15px;padding-left:15px}.cdx-button--icon-only{min-width:32px;padding-right:5px;padding-left:5px}.cdx-button--icon-only.cdx-button--size-large{min-width:44px;padding-right:11px;padding-left:11px}.cdx-button::-moz-focus-inner{border:0;padding:0}.cdx-button .cdx-button__icon,.cdx-button .cdx-icon{vertical-align:middle}.cdx-button .cdx-icon{color:inherit}.cdx-button--fake-button,.cdx-button--fake-button:hover,.cdx-button--fake-button:focus{text-decoration:none}.cdx-button:enabled,.cdx-button.cdx-button--fake-button--enabled{background-color:var(--background-color-interactive-subtle,#f8f9fa);color:var(--color-base,#202122);border-color:var(--border-color-interactive,#72777d)}@supports ((-webkit-mask-image:none) or (mask-image:none)){.cdx-button:enabled .cdx-button__icon,.cdx-button.cdx-button--fake-button--enabled .cdx-button__icon{background-color:var(--color-base,#202122)}}.cdx-button:enabled:hover,.cdx-button.cdx-button--fake-button--enabled:hover{background-color:var(--background-color-interactive-subtle--hover,#eaecf0);border-color:var(--border-color-interactive--hover,#27292d);cursor:pointer}.cdx-button:enabled:active,.cdx-button.cdx-button--fake-button--enabled:active,.cdx-button:enabled.cdx-button--is-active,.cdx-button.cdx-button--fake-button--enabled.cdx-button--is-active{background-color:var(--background-color-interactive-subtle--active,#dadde3);border-color:var(--border-color-interactive--active,#202122)}.cdx-button:enabled:focus,.cdx-button.cdx-button--fake-button--enabled:focus{outline:1px solid transparent}.cdx-button:enabled:focus:not(:active):not(.cdx-button--is-active),.cdx-button.cdx-button--fake-button--enabled:focus:not(:active):not(.cdx-button--is-active){border-color:var(--border-color-progressive--focus,#36c);box-shadow:inset 0 0 0 1px var(--box-shadow-color-progressive--focus,#36c)}.cdx-button:enabled.cdx-button--action-progressive,.cdx-button.cdx-button--fake-button--enabled.cdx-button--action-progressive{background-color:var(--background-color-progressive-subtle,#f1f4fd);color:var(--color-progressive,#36c);border-color:var(--border-color-progressive,#6485d1)}@supports ((-webkit-mask-image:none) or (mask-image:none)){.cdx-button:enabled.cdx-button--action-progressive .cdx-button__icon,.cdx-button.cdx-button--fake-button--enabled.cdx-button--action-progressive .cdx-button__icon{background-color:var(--color-progressive,#36c)}}.cdx-button:enabled.cdx-button--action-progressive:hover,.cdx-button.cdx-button--fake-button--enabled.cdx-button--action-progressive:hover{background-color:var(--background-color-progressive-subtle--hover,#dce3f9);color:var(--color-progressive--hover,#3056a9);border-color:var(--border-color-progressive--hover,#3056a9)}@supports ((-webkit-mask-image:none) or (mask-image:none)){.cdx-button:enabled.cdx-button--action-progressive:hover .cdx-button__icon,.cdx-button.cdx-button--fake-button--enabled.cdx-button--action-progressive:hover .cdx-button__icon{background-color:var(--color-progressive--hover,#3056a9)}}.cdx-button:enabled.cdx-button--action-progressive:active,.cdx-button.cdx-button--fake-button--enabled.cdx-button--action-progressive:active,.cdx-button:enabled.cdx-button--action-progressive.cdx-button--is-active,.cdx-button.cdx-button--fake-button--enabled.cdx-button--action-progressive.cdx-button--is-active{background-color:var(--background-color-progressive-subtle--active,#cbd6f6);color:var(--color-progressive--active,#233566);border-color:var(--border-color-progressive--active,#233566)}@supports ((-webkit-mask-image:none) or (mask-image:none)){.cdx-button:enabled.cdx-button--action-progressive:active .cdx-button__icon,.cdx-button.cdx-button--fake-button--enabled.cdx-button--action-progressive:active .cdx-button__icon,.cdx-button:enabled.cdx-button--action-progressive.cdx-button--is-active .cdx-button__icon,.cdx-button.cdx-button--fake-button--enabled.cdx-button--action-progressive.cdx-button--is-active .cdx-button__icon{background-color:var(--color-progressive--active,#233566)}}.cdx-button:enabled.cdx-button--action-destructive,.cdx-button.cdx-button--fake-button--enabled.cdx-button--action-destructive{background-color:var(--background-color-destructive-subtle,#ffe9e5);color:var(--color-destructive,#bf3c2c);border-color:var(--border-color-destructive,#f54739)}@supports ((-webkit-mask-image:none) or (mask-image:none)){.cdx-button:enabled.cdx-button--action-destructive .cdx-button__icon,.cdx-button.cdx-button--fake-button--enabled.cdx-button--action-destructive .cdx-button__icon{background-color:var(--color-destructive,#bf3c2c)}}.cdx-button:enabled.cdx-button--action-destructive:hover,.cdx-button.cdx-button--fake-button--enabled.cdx-button--action-destructive:hover{background-color:var(--background-color-destructive-subtle--hover,#ffdad3);color:var(--color-destructive--hover,#9f3526);border-color:var(--border-color-destructive--hover,#9f3526)}@supports ((-webkit-mask-image:none) or (mask-image:none)){.cdx-button:enabled.cdx-button--action-destructive:hover .cdx-button__icon,.cdx-button.cdx-button--fake-button--enabled.cdx-button--action-destructive:hover .cdx-button__icon{background-color:var(--color-destructive--hover,#9f3526)}}.cdx-button:enabled.cdx-button--action-destructive:active,.cdx-button.cdx-button--fake-button--enabled.cdx-button--action-destructive:active,.cdx-button:enabled.cdx-button--action-destructive.cdx-button--is-active,.cdx-button.cdx-button--fake-button--enabled.cdx-button--action-destructive.cdx-button--is-active{background-color:var(--background-color-destructive-subtle--active,#ffc8bd);color:var(--color-destructive--active,#612419);border-color:var(--border-color-destructive--active,#612419)}@supports ((-webkit-mask-image:none) or (mask-image:none)){.cdx-button:enabled.cdx-button--action-destructive:active .cdx-button__icon,.cdx-button.cdx-button--fake-button--enabled.cdx-button--action-destructive:active .cdx-button__icon,.cdx-button:enabled.cdx-button--action-destructive.cdx-button--is-active .cdx-button__icon,.cdx-button.cdx-button--fake-button--enabled.cdx-button--action-destructive.cdx-button--is-active .cdx-button__icon{background-color:var(--color-destructive--active,#612419)}}.cdx-button:enabled.cdx-button--action-destructive:focus:not(:active):not(.cdx-button--is-active),.cdx-button.cdx-button--fake-button--enabled.cdx-button--action-destructive:focus:not(:active):not(.cdx-button--is-active){border-color:var(--border-color-destructive--focus,#36c);box-shadow:inset 0 0 0 1px var(--box-shadow-color-destructive--focus,#36c)}.cdx-button:enabled.cdx-button--weight-primary.cdx-button--action-progressive,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-primary.cdx-button--action-progressive{background-color:var(--background-color-progressive,#36c);color:var(--wgg-color-against-background-color-progressive,#fff);border-color:var(--border-color-transparent,transparent)}.cdx-button:enabled.cdx-button--weight-primary.cdx-button--action-progressive:hover,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-primary.cdx-button--action-progressive:hover{background-color:var(--background-color-progressive--hover,#3056a9)}.cdx-button:enabled.cdx-button--weight-primary.cdx-button--action-progressive:active,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-primary.cdx-button--action-progressive:active,.cdx-button:enabled.cdx-button--weight-primary.cdx-button--action-progressive.cdx-button--is-active,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-primary.cdx-button--action-progressive.cdx-button--is-active{background-color:var(--background-color-progressive--active,#233566)}.cdx-button:enabled.cdx-button--weight-primary.cdx-button--action-progressive:focus:not(:active):not(.cdx-button--is-active),.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-primary.cdx-button--action-progressive:focus:not(:active):not(.cdx-button--is-active){border-color:var(--border-color-progressive--focus,#36c);box-shadow:inset 0 0 0 1px var(--box-shadow-color-progressive--focus,#36c),inset 0 0 0 2px var(--box-shadow-color-inverted,#fff)}@supports ((-webkit-mask-image:none) or (mask-image:none)){.cdx-button:enabled.cdx-button--weight-primary.cdx-button--action-progressive .cdx-button__icon,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-primary.cdx-button--action-progressive .cdx-button__icon{background-color:var(--wgg-color-against-background-color-progressive,#fff)}}.cdx-button:enabled.cdx-button--weight-primary.cdx-button--action-destructive,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-primary.cdx-button--action-destructive{background-color:var(--background-color-destructive,#bf3c2c);color:var(--wgg-color-against-background-color-destructive,#fff);border-color:var(--border-color-transparent,transparent)}.cdx-button:enabled.cdx-button--weight-primary.cdx-button--action-destructive:hover,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-primary.cdx-button--action-destructive:hover{background-color:var(--background-color-destructive--hover,#9f3526)}.cdx-button:enabled.cdx-button--weight-primary.cdx-button--action-destructive:active,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-primary.cdx-button--action-destructive:active,.cdx-button:enabled.cdx-button--weight-primary.cdx-button--action-destructive.cdx-button--is-active,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-primary.cdx-button--action-destructive.cdx-button--is-active{background-color:var(--background-color-destructive--active,#612419)}.cdx-button:enabled.cdx-button--weight-primary.cdx-button--action-destructive:focus:not(:active):not(.cdx-button--is-active),.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-primary.cdx-button--action-destructive:focus:not(:active):not(.cdx-button--is-active){border-color:var(--border-color-destructive--focus,#36c);box-shadow:inset 0 0 0 1px var(--box-shadow-color-destructive--focus,#36c),inset 0 0 0 2px var(--box-shadow-color-inverted,#fff)}@supports ((-webkit-mask-image:none) or (mask-image:none)){.cdx-button:enabled.cdx-button--weight-primary.cdx-button--action-destructive .cdx-button__icon,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-primary.cdx-button--action-destructive .cdx-button__icon{background-color:var(--wgg-color-against-background-color-destructive,#fff)}}.cdx-button:enabled.cdx-button--weight-quiet,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-quiet{background-color:var(--background-color-transparent,transparent);border-color:var(--border-color-transparent,transparent)}.cdx-button:enabled.cdx-button--weight-quiet:hover,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-quiet:hover{background-color:var(--background-color-interactive-subtle--hover,#eaecf0);mix-blend-mode:var(--mix-blend-mode-blend,multiply)}.cdx-button:enabled.cdx-button--weight-quiet:active,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-quiet:active,.cdx-button:enabled.cdx-button--weight-quiet.cdx-button--is-active,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-quiet.cdx-button--is-active{background-color:var(--background-color-interactive-subtle--active,#dadde3)}.cdx-button:enabled.cdx-button--weight-quiet.cdx-button--action-progressive,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-quiet.cdx-button--action-progressive{color:var(--color-progressive,#36c)}@supports ((-webkit-mask-image:none) or (mask-image:none)){.cdx-button:enabled.cdx-button--weight-quiet.cdx-button--action-progressive .cdx-button__icon,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-quiet.cdx-button--action-progressive .cdx-button__icon{background-color:var(--color-progressive,#36c)}}.cdx-button:enabled.cdx-button--weight-quiet.cdx-button--action-progressive:hover,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-quiet.cdx-button--action-progressive:hover{background-color:var(--background-color-progressive-subtle--hover,#dce3f9);color:var(--color-progressive--hover,#3056a9);border-color:var(--border-color-transparent,transparent)}@supports ((-webkit-mask-image:none) or (mask-image:none)){.cdx-button:enabled.cdx-button--weight-quiet.cdx-button--action-progressive:hover .cdx-button__icon,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-quiet.cdx-button--action-progressive:hover .cdx-button__icon{background-color:var(--color-progressive--hover,#3056a9)}}.cdx-button:enabled.cdx-button--weight-quiet.cdx-button--action-progressive:active,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-quiet.cdx-button--action-progressive:active,.cdx-button:enabled.cdx-button--weight-quiet.cdx-button--action-progressive.cdx-button--is-active,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-quiet.cdx-button--action-progressive.cdx-button--is-active{background-color:var(--background-color-progressive-subtle--active,#cbd6f6);color:var(--color-progressive--active,#233566);border-color:var(--border-color-transparent,transparent)}@supports ((-webkit-mask-image:none) or (mask-image:none)){.cdx-button:enabled.cdx-button--weight-quiet.cdx-button--action-progressive:active .cdx-button__icon,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-quiet.cdx-button--action-progressive:active .cdx-button__icon,.cdx-button:enabled.cdx-button--weight-quiet.cdx-button--action-progressive.cdx-button--is-active .cdx-button__icon,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-quiet.cdx-button--action-progressive.cdx-button--is-active .cdx-button__icon{background-color:var(--color-progressive--active,#233566)}}.cdx-button:enabled.cdx-button--weight-quiet.cdx-button--action-progressive:focus:not(:active):not(.cdx-button--is-active),.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-quiet.cdx-button--action-progressive:focus:not(:active):not(.cdx-button--is-active){border-color:var(--border-color-progressive--focus,#36c);box-shadow:inset 0 0 0 1px var(--box-shadow-color-progressive--focus,#36c)}.cdx-button:enabled.cdx-button--weight-quiet.cdx-button--action-destructive,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-quiet.cdx-button--action-destructive{color:var(--color-destructive,#bf3c2c)}@supports ((-webkit-mask-image:none) or (mask-image:none)){.cdx-button:enabled.cdx-button--weight-quiet.cdx-button--action-destructive .cdx-button__icon,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-quiet.cdx-button--action-destructive .cdx-button__icon{background-color:var(--color-destructive,#bf3c2c)}}.cdx-button:enabled.cdx-button--weight-quiet.cdx-button--action-destructive:hover,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-quiet.cdx-button--action-destructive:hover{background-color:var(--background-color-destructive-subtle--hover,#ffdad3);color:var(--color-destructive--hover,#9f3526);border-color:var(--border-color-transparent,transparent)}@supports ((-webkit-mask-image:none) or (mask-image:none)){.cdx-button:enabled.cdx-button--weight-quiet.cdx-button--action-destructive:hover .cdx-button__icon,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-quiet.cdx-button--action-destructive:hover .cdx-button__icon{background-color:var(--color-destructive--hover,#9f3526)}}.cdx-button:enabled.cdx-button--weight-quiet.cdx-button--action-destructive:active,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-quiet.cdx-button--action-destructive:active,.cdx-button:enabled.cdx-button--weight-quiet.cdx-button--action-destructive.cdx-button--is-active,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-quiet.cdx-button--action-destructive.cdx-button--is-active{background-color:var(--background-color-destructive-subtle--active,#ffc8bd);color:var(--color-destructive--active,#612419);border-color:var(--border-color-transparent,transparent)}@supports ((-webkit-mask-image:none) or (mask-image:none)){.cdx-button:enabled.cdx-button--weight-quiet.cdx-button--action-destructive:active .cdx-button__icon,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-quiet.cdx-button--action-destructive:active .cdx-button__icon,.cdx-button:enabled.cdx-button--weight-quiet.cdx-button--action-destructive.cdx-button--is-active .cdx-button__icon,.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-quiet.cdx-button--action-destructive.cdx-button--is-active .cdx-button__icon{background-color:var(--color-destructive--active,#612419)}}.cdx-button:enabled.cdx-button--weight-quiet.cdx-button--action-destructive:focus:not(:active):not(.cdx-button--is-active),.cdx-button.cdx-button--fake-button--enabled.cdx-button--weight-quiet.cdx-button--action-destructive:focus:not(:active):not(.cdx-button--is-active){border-color:var(--border-color-destructive--focus,#36c);box-shadow:inset 0 0 0 1px var(--box-shadow-color-destructive--focus,#36c)}.cdx-button:disabled,.cdx-button.cdx-button--fake-button--disabled{background-color:var(--background-color-disabled,#dadde3);color:var(--color-disabled-emphasized,#a2a9b1);border-color:var(--border-color-transparent,transparent)}@supports ((-webkit-mask-image:none) or (mask-image:none)){.cdx-button:disabled .cdx-button__icon,.cdx-button.cdx-button--fake-button--disabled .cdx-button__icon{background-color:var(--color-inverted,#fff)}}.cdx-button:disabled.cdx-button--weight-quiet,.cdx-button.cdx-button--fake-button--disabled.cdx-button--weight-quiet{background-color:var(--background-color-transparent,transparent);color:var(--color-disabled,#a2a9b1)}@supports ((-webkit-mask-image:none) or (mask-image:none)){.cdx-button:disabled.cdx-button--weight-quiet .cdx-button__icon,.cdx-button.cdx-button--fake-button--disabled.cdx-button--weight-quiet .cdx-button__icon{background-color:var(--color-disabled,#a2a9b1)}}.cdx-icon{color:var(--color-base,#202122);display:inline-flex;align-items:center;justify-content:center;vertical-align:text-bottom}.cdx-icon svg{fill:currentcolor;width:100%;height:100%}.cdx-icon--x-small{min-width:12px;min-height:12px;width:.75rem;height:.75rem}.cdx-icon--small{min-width:16px;min-height:16px;width:1rem;height:1rem}.cdx-icon--medium{min-width:20px;min-height:20px;width:1.25rem;height:1.25rem}.cdx-icon--flipped svg{transform:scaleX(-1)}.cdx-dialog-backdrop{background-color:var(--background-color-backdrop-light,rgba(255,255,255,.65));display:flex;align-items:center;justify-content:center;position:fixed;top:0;left:0;z-index:400;min-height:100%;width:100vw;height:100vh;height:-webkit-fill-available}.cdx-dialog{background-color:var(--background-color-base,#fff);display:flex;flex-direction:column;box-sizing:border-box;width:calc(100% - 2rem);max-width:32rem;max-height:calc(100vh - 2.5rem);border:1px solid var(--border-color-base,#a2a9b1);border-radius:2px;box-shadow:0 2px 2px rgba(0,0,0,.2)}.cdx-dialog__header{padding:16px 24px}.cdx-dialog__header--default{display:flex;align-items:baseline;justify-content:flex-end;box-sizing:border-box;width:100%}.cdx-dialog__header__title-group{display:flex;flex-grow:1;flex-direction:column;gap:6px}.cdx-dialog__header .cdx-dialog__header__title{margin:0;border:0;padding:0;font-family:inherit;font-size:1.125rem;font-weight:700;line-height:1.25}.cdx-dialog__header .cdx-dialog__header__subtitle{color:var(--color-subtle,#54595d);margin:0;padding:0;font-size:1rem;line-height:1.375}.cdx-dialog__header__close-button.cdx-button{margin-right:-8px}.cdx-dialog--dividers .cdx-dialog__header{border-bottom:1px solid var(--border-color-subtle,#c8ccd1)}.cdx-dialog__body{flex-grow:1;padding:16px 24px;overflow-y:auto}.cdx-dialog__body--no-header{padding-top:24px}.cdx-dialog__body--no-footer{padding-bottom:24px}.cdx-dialog__body>*:first-child{margin-top:0;padding-top:0}.cdx-dialog__body>*:last-child{margin-bottom:0;padding-bottom:0}.cdx-dialog__footer{padding:16px 24px 24px}.cdx-dialog__footer--default{display:flex;align-items:baseline;flex-wrap:wrap;justify-content:space-between;gap:12px}.cdx-dialog__footer .cdx-dialog__footer__text{color:var(--color-subtle,#54595d);flex:1 0 auto;width:100%;margin:0;font-size:.875rem;line-height:1.5714285}.cdx-dialog__footer__actions{display:flex;flex-grow:1;gap:12px}.cdx-dialog--dividers .cdx-dialog__footer{border-top:1px solid var(--border-color-subtle,#c8ccd1)}.cdx-dialog--horizontal-actions .cdx-dialog__footer__actions{flex-direction:row-reverse}.cdx-dialog--vertical-actions .cdx-dialog__footer__actions{flex-direction:column;width:100%}.cdx-dialog--vertical-actions .cdx-dialog__footer .cdx-dialog__footer__primary-action.cdx-button,.cdx-dialog--vertical-actions .cdx-dialog__footer .cdx-dialog__footer__default-action.cdx-button{max-width:none}.cdx-dialog-focus-trap{position:absolute}.cdx-dialog-focus-trap:focus{outline:0}.cdx-dialog-fade-enter-active,.cdx-dialog-fade-leave-active{transition-property:opacity;transition-duration:.25s;transition-timing-function:ease}.cdx-dialog-fade-enter-from,.cdx-dialog-fade-leave-to{opacity:0}body.cdx-dialog-open{overflow:hidden}.cdx-progress-bar{box-sizing:border-box;overflow-x:hidden}.cdx-progress-bar__bar{width:33.33%;height:100%}.cdx-progress-bar:not(.cdx-progress-bar--inline){position:relative;z-index:1;height:1rem;max-width:none;border:1px solid var(--border-color-base,#a2a9b1);border-radius:9999px;box-shadow:0 2px 2px rgba(0,0,0,.2)}.cdx-progress-bar--inline{width:100%;height:.25rem}.cdx-progress-bar:not(.cdx-progress-bar--disabled) .cdx-progress-bar__bar{background-color:var(--background-color-progressive,#36c);animation-name:cdx-animation-progress-bar__bar;animation-duration:1.6s;animation-timing-function:linear;animation-iteration-count:infinite}.cdx-progress-bar:not(.cdx-progress-bar--disabled).cdx-progress-bar--block{background-color:var(--background-color-base,#fff)}.cdx-progress-bar--disabled .cdx-progress-bar__bar{background-color:var(--background-color-disabled,#dadde3)}.cdx-progress-bar--disabled:not(.cdx-progress-bar--inline){background-color:var(--background-color-disabled-subtle,#eaecf0)}@keyframes cdx-animation-progress-bar__bar{0%{transform:translate(-100%)}to{transform:translate(300%)}}.cdx-thumbnail{display:inline-flex}.cdx-thumbnail__placeholder,.cdx-thumbnail__image{background-position:center;background-repeat:no-repeat;background-size:cover;flex-shrink:0;box-sizing:border-box;min-width:40px;min-height:40px;width:2.5rem;height:2.5rem;border:1px solid var(--border-color-subtle,#c8ccd1);border-radius:2px}.cdx-thumbnail__image{background-color:var(--background-color-base-fixed,#fff);display:inline-block}.cdx-thumbnail__image-enter-active{transition-property:opacity;transition-duration:.1s}.cdx-thumbnail__image-enter-from{opacity:0}.cdx-thumbnail__placeholder{background-color:var(--background-color-interactive-subtle,#f8f9fa);display:inline-flex;align-items:center;justify-content:center}.cdx-thumbnail__placeholder__icon{min-width:20px;min-height:20px;width:1.25rem;height:1.25rem;display:inline-block;vertical-align:text-bottom}@supports not (((-webkit-mask-image:none) or (mask-image:none))){.cdx-thumbnail__placeholder__icon{background-position:center;background-repeat:no-repeat;background-size:max(1.25rem,20px)}}@supports ((-webkit-mask-image:none) or (mask-image:none)){.cdx-thumbnail__placeholder__icon{-webkit-mask-position:center;mask-position:center;-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat;-webkit-mask-size:max(1.25rem,20px);mask-size:max(1.25rem,20px)}}@supports not (((-webkit-mask-image:none) or (mask-image:none))){.cdx-thumbnail__placeholder__icon{background-image:url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="%23000"><path d="M19 3H1v14h18zM3 14l3.5-4.5 2.5 3L12.5 8l4.5 6z"/><path d="M19 5H1V3h18zm0 12H1v-2h18z"/></svg>');filter:invert(var(--filter-invert-icon,0));opacity:var(--opacity-icon-base,.87)}}@supports ((-webkit-mask-image:none) or (mask-image:none)){.cdx-thumbnail__placeholder__icon{-webkit-mask-image:url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="%23000"><path d="M19 3H1v14h18zM3 14l3.5-4.5 2.5 3L12.5 8l4.5 6z"/><path d="M19 5H1V3h18zm0 12H1v-2h18z"/></svg>');mask-image:url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="%23000"><path d="M19 3H1v14h18zM3 14l3.5-4.5 2.5 3L12.5 8l4.5 6z"/><path d="M19 5H1V3h18zm0 12H1v-2h18z"/></svg>');background-color:var(--color-placeholder,#72777d)}}.cdx-thumbnail__placeholder__icon--vue.cdx-icon{color:var(--color-placeholder,#72777d)}.cdx-search-result-title{display:inline-block;max-width:100%;font-weight:700}.cdx-search-result-title__match{font-weight:400}.cdx-menu-item{list-style:none;position:relative;padding:8px 12px;line-height:1.6;transition-property:background-color,color,border-color,box-shadow;transition-duration:.1s}.cdx-menu-item__content{display:flex;align-items:center;line-height:1.4285714;word-wrap:break-word;-webkit-hyphens:auto;-ms-hyphens:auto;hyphens:auto}.cdx-menu-item__content,.cdx-menu-item__content:hover{text-decoration:none}.cdx-menu-item--has-description .cdx-menu-item__content{align-items:flex-start}.cdx-menu-item__text{max-width:100%}.cdx-menu-item__text__description{display:block}.cdx-menu-item__thumbnail.cdx-thumbnail,.cdx-menu-item__icon{margin-right:8px}.cdx-menu-item__selected-icon{height:1.4285714em;margin-left:auto}.cdx-menu-item__icon.cdx-icon{color:var(--color-subtle,#54595d)}.cdx-menu-item__selected-icon.cdx-icon{color:inherit}.cdx-menu-item--bold-label .cdx-menu-item__text__label{font-weight:700}.cdx-menu-item--hide-description-overflow .cdx-menu-item__text{overflow:hidden}.cdx-menu-item--hide-description-overflow .cdx-menu-item__text__description{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.cdx-menu-item--enabled,.cdx-menu-item--enabled .cdx-menu-item__content{color:var(--color-base,#202122)}.cdx-menu-item--enabled .cdx-menu-item__text__supporting-text,.cdx-menu-item--enabled .cdx-menu-item__text__description{color:var(--color-subtle,#54595d)}.cdx-menu-item--enabled.cdx-menu-item--highlighted{background-color:var(--background-color-interactive-subtle--hover,#eaecf0);cursor:pointer}.cdx-menu-item--enabled.cdx-menu-item--active{background-color:var(--background-color-interactive-subtle--active,#dadde3)}.cdx-menu-item--enabled.cdx-menu-item--selected{background-color:var(--background-color-progressive-subtle,#f1f4fd);color:var(--color-progressive,#36c)}.cdx-menu-item--enabled.cdx-menu-item--selected .cdx-menu-item__content,.cdx-menu-item--enabled.cdx-menu-item--selected .cdx-menu-item__text__description,.cdx-menu-item--enabled.cdx-menu-item--selected .cdx-menu-item__icon{color:var(--color-progressive,#36c)}.cdx-menu-item--enabled.cdx-menu-item--selected.cdx-menu-item--highlighted{background-color:var(--background-color-progressive-subtle--hover,#dce3f9)}.cdx-menu-item--enabled.cdx-menu-item--selected.cdx-menu-item--highlighted .cdx-menu-item__content,.cdx-menu-item--enabled.cdx-menu-item--selected.cdx-menu-item--highlighted .cdx-menu-item__text__description,.cdx-menu-item--enabled.cdx-menu-item--selected.cdx-menu-item--highlighted .cdx-menu-item__icon{color:var(--color-progressive--hover,#3056a9)}.cdx-menu-item--enabled.cdx-menu-item--selected.cdx-menu-item--active{background-color:var(--background-color-progressive-subtle--active,#cbd6f6)}.cdx-menu-item--enabled.cdx-menu-item--selected.cdx-menu-item--active .cdx-menu-item__content,.cdx-menu-item--enabled.cdx-menu-item--selected.cdx-menu-item--active .cdx-menu-item__text__description,.cdx-menu-item--enabled.cdx-menu-item--selected.cdx-menu-item--active .cdx-menu-item__icon{color:var(--color-progressive--active,#233566)}.cdx-menu-item--disabled{color:var(--color-disabled,#a2a9b1);cursor:default}.cdx-menu-item--disabled .cdx-menu-item__text__description,.cdx-menu-item--disabled .cdx-menu-item__icon{color:inherit}.cdx-menu-item--destructive .cdx-menu-item__content,.cdx-menu-item--destructive .cdx-menu-item__text__description,.cdx-menu-item--destructive .cdx-menu-item__icon{color:var(--color-destructive,#bf3c2c)}.cdx-menu-item--destructive.cdx-menu-item--highlighted{background-color:var(--background-color-destructive-subtle--hover,#ffdad3)}.cdx-menu-item--destructive.cdx-menu-item--highlighted .cdx-menu-item__content,.cdx-menu-item--destructive.cdx-menu-item--highlighted .cdx-menu-item__text__description,.cdx-menu-item--destructive.cdx-menu-item--highlighted .cdx-menu-item__icon{color:var(--color-destructive--hover,#9f3526)}.cdx-menu-item--destructive.cdx-menu-item--active{background-color:var(--background-color-destructive-subtle--active,#ffc8bd)}.cdx-menu-item--destructive.cdx-menu-item--active .cdx-menu-item__content,.cdx-menu-item--destructive.cdx-menu-item--active .cdx-menu-item__text__description,.cdx-menu-item--destructive.cdx-menu-item--active .cdx-menu-item__icon{color:var(--color-destructive--active,#612419)}.cdx-menu-item--destructive.cdx-menu-item--selected{background-color:var(--background-color-destructive-subtle,#ffe9e5);color:var(--color-destructive,#bf3c2c)}.cdx-menu-item--destructive.cdx-menu-item--selected .cdx-menu-item__content,.cdx-menu-item--destructive.cdx-menu-item--selected .cdx-menu-item__text__description,.cdx-menu-item--destructive.cdx-menu-item--selected .cdx-menu-item__icon{color:var(--color-destructive,#bf3c2c)}.cdx-menu-item--destructive.cdx-menu-item--selected.cdx-menu-item--highlighted{background-color:var(--background-color-destructive-subtle--hover,#ffdad3)}.cdx-menu-item--destructive.cdx-menu-item--selected.cdx-menu-item--highlighted .cdx-menu-item__content,.cdx-menu-item--destructive.cdx-menu-item--selected.cdx-menu-item--highlighted .cdx-menu-item__text__description,.cdx-menu-item--destructive.cdx-menu-item--selected.cdx-menu-item--highlighted .cdx-menu-item__icon{color:var(--color-destructive--hover,#9f3526)}.cdx-menu-item--destructive.cdx-menu-item--selected.cdx-menu-item--active{background-color:var(--background-color-destructive-subtle--active,#ffc8bd)}.cdx-menu-item--destructive.cdx-menu-item--selected.cdx-menu-item--active .cdx-menu-item__content,.cdx-menu-item--destructive.cdx-menu-item--selected.cdx-menu-item--active .cdx-menu-item__text__description,.cdx-menu-item--destructive.cdx-menu-item--selected.cdx-menu-item--active .cdx-menu-item__icon{color:var(--color-destructive--active,#612419)}.cdx-menu{background-color:var(--background-color-base,#fff);display:flex;flex-direction:column;position:absolute;left:0;z-index:50;box-sizing:border-box;width:100%;border:1px solid var(--border-color-base,#a2a9b1);border-radius:2px;box-shadow:0 2px 2px rgba(0,0,0,.2)}.cdx-menu__progress-bar.cdx-progress-bar{position:absolute;top:0}.cdx-menu__listbox,.cdx-menu__group{margin:0;padding:0}.cdx-menu__listbox{overflow-y:auto}.cdx-menu__group{display:flex;flex-direction:column}.cdx-menu__group__meta{display:flex;gap:8px;padding:8px 12px 6px}.cdx-menu__group__meta__text{display:flex;flex-direction:column;line-height:1.6}.cdx-menu__group__icon{height:1.6em}.cdx-menu__group__label{font-weight:700}.cdx-menu__group__description{color:var(--color-subtle,#54595d);font-size:.875rem}.cdx-menu__group-wrapper--hide-label .cdx-menu__group__meta{display:block;clip:rect(1px,1px,1px,1px);position:absolute!important;width:1px;height:1px;margin:-1px;border:0;padding:0;overflow:hidden}.cdx-menu__group-wrapper+.cdx-menu-item,.cdx-menu-item+.cdx-menu__group-wrapper,.cdx-menu__group-wrapper--hide-label,.cdx-menu__group-wrapper--hide-label+.cdx-menu__group-wrapper{border-top:1px solid var(--border-color-muted,#dadde3)}.cdx-menu--has-footer .cdx-menu__listbox>.cdx-menu-item:last-of-type{position:absolute;bottom:0;box-sizing:border-box;width:100%}.cdx-menu--has-footer .cdx-menu__listbox>.cdx-menu-item:last-of-type:not(:first-of-type){border-top:1px solid var(--border-color-subtle,#c8ccd1)}.cdx-select{box-sizing:border-box;min-width:256px;min-height:32px;border-width:1px;border-style:solid;border-radius:2px;padding-top:4px;padding-bottom:4px;padding-left:8px;padding-right:calc(16px + 1.25rem);font-size:inherit;line-height:1.375;-webkit-appearance:none;appearance:none;background-position:center right 12px;background-repeat:no-repeat;background-size:max(.75rem,12px)}.cdx-select:disabled{background-color:var(--background-color-disabled-subtle,#eaecf0);color:var(--color-disabled,#a2a9b1);border-color:var(--border-color-disabled,#c8ccd1);background-image:url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="%2372777d"><path d="m17.5 4.75-7.5 7.5-7.5-7.5L1 6.25l9 9 9-9z"/></svg>');opacity:1}.cdx-select:enabled{background-color:var(--background-color-interactive-subtle,#f8f9fa);color:var(--color-subtle,#54595d);border-color:var(--border-color-interactive,#72777d);transition-property:background-color,color,border-color,box-shadow;transition-duration:.1s;background-image:url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="%23202122"><path d="m17.5 4.75-7.5 7.5-7.5-7.5L1 6.25l9 9 9-9z"/></svg>')}.cdx-select:enabled:hover{background-color:var(--background-color-interactive-subtle--hover,#eaecf0);border-color:var(--border-color-interactive--hover,#27292d);cursor:pointer}.cdx-select:enabled:active{background-color:var(--background-color-interactive-subtle--active,#dadde3);border-color:var(--border-color-interactive--active,#202122)}.cdx-select:enabled:focus:not(:active){background-color:var(--background-color-base,#fff);border-color:var(--border-color-progressive--focus,#36c);box-shadow:inset 0 0 0 1px var(--box-shadow-color-progressive--focus,#36c);outline:1px solid transparent}.cdx-select-vue{display:inline-block;position:relative}.cdx-select-vue__handle{box-sizing:border-box;min-width:256px;min-height:32px;border-width:1px;border-style:solid;border-radius:2px;padding-top:4px;padding-bottom:4px;padding-left:8px;padding-right:calc(16px + 1.25rem);font-size:inherit;line-height:1.375;position:relative;width:100%}.cdx-select-vue--has-start-icon .cdx-select-vue__handle{padding-left:calc(20px + 1.25rem)}.cdx-select-vue__start-icon.cdx-icon{color:var(--color-subtle,#54595d);position:absolute;top:50%;min-width:20px;min-height:20px;width:1.25rem;height:1.25rem;transition-property:color;transition-duration:.1s;left:12px;transform:translateY(-50%)}.cdx-select-vue__indicator.cdx-icon{color:var(--color-base,#202122);position:absolute;top:50%;min-width:12px;min-height:12px;width:.75rem;height:.75rem;transition-property:color;transition-duration:.1s;right:12px;transform:translateY(-50%)}.cdx-select-vue--enabled .cdx-select-vue__handle{background-color:var(--background-color-interactive-subtle,#f8f9fa);color:var(--color-subtle,#54595d);border-color:var(--border-color-interactive,#72777d);transition-property:background-color,color,border-color,box-shadow;transition-duration:.1s}.cdx-select-vue--enabled .cdx-select-vue__handle:hover{background-color:var(--background-color-interactive-subtle--hover,#eaecf0);border-color:var(--border-color-interactive--hover,#27292d);cursor:pointer}.cdx-select-vue--enabled .cdx-select-vue__handle:active{background-color:var(--background-color-interactive-subtle--active,#dadde3);border-color:var(--border-color-interactive--active,#202122)}.cdx-select-vue--enabled .cdx-select-vue__handle:focus:not(:active){background-color:var(--background-color-base,#fff);border-color:var(--border-color-progressive--focus,#36c);box-shadow:inset 0 0 0 1px var(--box-shadow-color-progressive--focus,#36c);outline:1px solid transparent}.cdx-select-vue--enabled.cdx-select-vue--value-selected .cdx-select-vue__handle{color:var(--color-base,#202122)}.cdx-select-vue--enabled.cdx-select-vue--expanded .cdx-select-vue__handle{background-color:var(--background-color-base,#fff)}.cdx-select-vue--disabled .cdx-select-vue__handle{background-color:var(--background-color-disabled-subtle,#eaecf0);color:var(--color-disabled,#a2a9b1);border-color:var(--border-color-disabled,#c8ccd1);cursor:default}.cdx-select-vue--disabled .cdx-select-vue__indicator,.cdx-select-vue--disabled .cdx-select-vue__start-icon{color:var(--color-disabled,#a2a9b1)}.cdx-select-vue--status-error.cdx-select-vue--enabled .cdx-select-vue__handle{background-color:var(--background-color-error-subtle,#ffe9e5);color:var(--color-error,#bf3c2c);border-color:var(--border-color-error,#f54739)}.cdx-select-vue--status-error.cdx-select-vue--enabled .cdx-select-vue__handle .cdx-select-vue__start-icon{color:var(--color-error,#bf3c2c)}.cdx-select-vue--status-error.cdx-select-vue--enabled .cdx-select-vue__handle:hover:not(:focus){background-color:var(--background-color-error-subtle--hover,#ffdad3);color:var(--color-error--hover,#9f3526);border-color:var(--border-color-error--hover,#9f3526)}.cdx-select-vue--status-error.cdx-select-vue--enabled .cdx-select-vue__handle:hover:not(:focus) .cdx-select-vue__start-icon{color:var(--color-error--hover,#9f3526)}.cdx-select-vue--status-error.cdx-select-vue--enabled .cdx-select-vue__handle:active{background-color:var(--background-color-error-subtle--active,#ffc8bd);color:var(--color-error--active,#612419);border-color:var(--border-color-error--active,#612419)}.cdx-select-vue--status-error.cdx-select-vue--enabled .cdx-select-vue__handle:active .cdx-select-vue__start-icon{color:var(--color-error--active,#612419)}.cdx-select-vue--status-error.cdx-select-vue--enabled .cdx-select-vue__handle:focus:not(:active){color:var(--color-subtle,#54595d)}.cdx-select-vue--status-error.cdx-select-vue--enabled.cdx-select-vue--value-selected .cdx-select-vue__handle:focus:not(:active){color:var(--color-base,#202122)}.cdx-select-vue--status-error.cdx-select-vue--enabled.cdx-select-vue--value-selected .cdx-select-vue__handle:focus:not(:active) .cdx-select-vue__start-icon{color:var(--color-base,#202122)}.cdx-dialog .cdx-select-vue{position:static}.cdx-tab[aria-hidden=true]{display:none}.cdx-tab:focus{outline:1px solid transparent}.cdx-tabs__header{display:flex;align-items:flex-end;position:relative}.cdx-tabs__prev-scroller,.cdx-tabs__next-scroller{background-color:inherit;position:absolute;top:0;bottom:0}.cdx-tabs__prev-scroller{left:0}.cdx-tabs__next-scroller{right:0}.cdx-tabs__prev-scroller:after,.cdx-tabs__next-scroller:before{content:"";position:absolute;top:0;z-index:1;width:1.5rem;height:100%;pointer-events:none}.cdx-tabs__prev-scroller:after{left:100%}.cdx-tabs__next-scroller:before{right:100%}.cdx-tabs__scroll-button.cdx-button{height:100%}.cdx-tabs__list{display:flex;overflow-x:auto;scrollbar-width:none;-webkit-overflow-scrolling:touch}.cdx-tabs__list::-webkit-scrollbar{-webkit-appearance:none;display:none}.cdx-tabs__list__item{background-color:var(--background-color-transparent,transparent);display:block;flex:0 0 auto;max-width:16rem;border-width:0;border-top-left-radius:2px;border-top-right-radius:2px;padding:4px 12px;font-size:1rem;font-weight:700;line-height:1.4285714;text-decoration:none;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;transition-property:background-color,color,border-color,box-shadow;transition-duration:.1s}.cdx-tabs__list__item:hover{cursor:pointer}.cdx-tabs__list__item[aria-selected=true]{cursor:default}.cdx-tabs>.cdx-tabs__header .cdx-tabs__list__item+.cdx-tabs__list__item{margin-left:0}.cdx-tabs--framed>.cdx-tabs__header{background-color:var(--background-color-interactive,#eaecf0)}.cdx-tabs--framed>.cdx-tabs__header .cdx-tabs__prev-scroller:after{background-image:linear-gradient(to right,var(--background-color-interactive,#eaecf0) 0,var(--background-color-transparent,transparent) 100%)}.cdx-tabs--framed>.cdx-tabs__header .cdx-tabs__next-scroller:before{background-image:linear-gradient(to left,var(--background-color-interactive,#eaecf0) 0,var(--background-color-transparent,transparent) 100%)}.cdx-tabs--framed>.cdx-tabs__header .cdx-tabs__list__item{color:var(--color-base,#202122);margin:8px 4px 0 8px}.cdx-tabs--framed>.cdx-tabs__header .cdx-tabs__list__item:enabled{overflow:hidden}.cdx-tabs--framed>.cdx-tabs__header .cdx-tabs__list__item:enabled:hover{background-color:var(--background-color-interactive-subtle--hover,#eaecf0);color:var(--color-base,#202122);mix-blend-mode:var(--mix-blend-mode-blend,multiply)}.cdx-tabs--framed>.cdx-tabs__header .cdx-tabs__list__item:enabled:active{background-color:var(--background-color-interactive-subtle--active,#dadde3);color:var(--color-base,#202122)}.cdx-tabs--framed>.cdx-tabs__header .cdx-tabs__list__item[aria-selected=true],.cdx-tabs--framed>.cdx-tabs__header .cdx-tabs__list__item[aria-selected=true]:hover{background-color:var(--background-color-base,#fff);color:var(--color-base,#202122);mix-blend-mode:var(--mix-blend-mode-base,normal)}.cdx-tabs--framed>.cdx-tabs__header .cdx-tabs__list__item:disabled{background-color:var(--background-color-interactive,#eaecf0);color:var(--color-disabled,#a2a9b1);cursor:default}.cdx-tabs--framed>.cdx-tabs__header .cdx-tabs__list__item:last-child{margin-right:8px}.cdx-tabs:not(.cdx-tabs--framed)>.cdx-tabs__header{background-color:var(--background-color-base,#fff);margin:0 4px;border-bottom:1px solid var(--border-color-base,#a2a9b1)}.cdx-tabs:not(.cdx-tabs--framed)>.cdx-tabs__header .cdx-tabs__prev-scroller:after{background-image:linear-gradient(to right,var(--background-color-base,#fff) 0,var(--background-color-transparent,transparent) 100%)}.cdx-tabs:not(.cdx-tabs--framed)>.cdx-tabs__header .cdx-tabs__next-scroller:before{background-image:linear-gradient(to left,var(--background-color-base,#fff) 0,var(--background-color-transparent,transparent) 100%)}.cdx-tabs:not(.cdx-tabs--framed)>.cdx-tabs__header .cdx-tabs__list__item{margin:0 2px}.cdx-tabs:not(.cdx-tabs--framed)>.cdx-tabs__header .cdx-tabs__list__item:enabled{color:var(--color-base,#202122)}.cdx-tabs:not(.cdx-tabs--framed)>.cdx-tabs__header .cdx-tabs__list__item:enabled:hover:not([aria-selected="true"]){color:var(--color-progressive--hover,#3056a9);box-shadow:inset 0 -2px 0 0 var(--box-shadow-color-progressive-selected--hover,#3056a9)}.cdx-tabs:not(.cdx-tabs--framed)>.cdx-tabs__header .cdx-tabs__list__item:enabled:active:not([aria-selected="true"]){color:var(--color-progressive--active,#233566);box-shadow:inset 0 -2px 0 0 var(--box-shadow-color-progressive-selected--active,#233566)}.cdx-tabs:not(.cdx-tabs--framed)>.cdx-tabs__header .cdx-tabs__list__item[aria-selected=true]{color:var(--color-progressive,#36c);box-shadow:inset 0 -2px 0 0 var(--box-shadow-color-progressive-selected,#36c)}.cdx-tabs:not(.cdx-tabs--framed)>.cdx-tabs__header .cdx-tabs__list__item[aria-selected=true]:hover{color:var(--color-progressive,#36c)}.cdx-tabs:not(.cdx-tabs--framed)>.cdx-tabs__header .cdx-tabs__list__item:disabled{color:var(--color-disabled,#a2a9b1);cursor:default}.cdx-tabs:not(.cdx-tabs--framed)>.cdx-tabs__header .cdx-tabs__list__item:first-child{margin-left:0}.cdx-tabs:not(.cdx-tabs--framed)>.cdx-tabs__header .cdx-tabs__list__item:last-child{margin-right:0}.cdx-tabs--framed>.cdx-tabs__header .cdx-tabs__list__item:focus-visible,.cdx-tabs:not(.cdx-tabs--framed)>.cdx-tabs__header .cdx-tabs__list__item:focus-visible{box-shadow:inset 0 0 0 2px var(--border-color-progressive,#6485d1);outline:1px solid transparent;overflow:hidden}.cdx-text-input{position:relative;box-sizing:border-box;min-width:256px;border-radius:2px;overflow:hidden}.cdx-text-input .cdx-text-input__start-icon{position:absolute;top:50%;min-width:20px;min-height:20px;width:1.25rem;height:1.25rem;transition-property:color;transition-duration:.1s;left:9px;transform:translateY(-50%)}.cdx-text-input__icon.cdx-text-input__end-icon{min-width:16px;min-height:16px;width:1rem;height:1rem}@supports not (((-webkit-mask-image:none) or (mask-image:none))){.cdx-text-input__icon.cdx-text-input__end-icon{background-position:center;background-repeat:no-repeat;background-size:max(1rem,16px)}}@supports ((-webkit-mask-image:none) or (mask-image:none)){.cdx-text-input__icon.cdx-text-input__end-icon{-webkit-mask-position:center;mask-position:center;-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat;-webkit-mask-size:max(1rem,16px);mask-size:max(1rem,16px)}}.cdx-text-input__clear-icon.cdx-icon,.cdx-text-input .cdx-text-input__end-icon{position:absolute;top:50%;min-width:16px;min-height:16px;width:1rem;height:1rem;transition-property:color;transition-duration:.1s;right:9px;transform:translateY(-50%)}.cdx-text-input__clear-icon.cdx-icon:hover{cursor:pointer}.cdx-text-input__end-icon.cdx-icon+.cdx-text-input__clear-icon.cdx-icon{right:calc(17px + 1rem)}.cdx-text-input__input{display:block;box-sizing:border-box;min-height:32px;width:100%;margin:0;border-width:1px;border-style:solid;border-radius:0;padding:4px 8px;font-family:inherit;font-size:inherit;line-height:1.375}.cdx-text-input__input:enabled{background-color:var(--background-color-base,#fff);color:var(--color-base,#202122);border-color:var(--border-color-interactive,#72777d);box-shadow:inset 0 0 0 1px var(--box-shadow-color-transparent,transparent);transition-property:background-color,color,border-color,box-shadow;transition-duration:.25s}.cdx-text-input__input:enabled~.cdx-text-input__icon-vue{color:var(--color-placeholder,#72777d)}.cdx-text-input__input:enabled~.cdx-text-input__icon{opacity:var(--opacity-icon-placeholder,.51)}.cdx-text-input__input:enabled:hover{border-color:var(--border-color-interactive--hover,#27292d)}.cdx-text-input__input:enabled:focus~.cdx-text-input__icon-vue,.cdx-text-input__input:enabled.cdx-text-input__input--has-value~.cdx-text-input__icon-vue{color:var(--color-subtle,#54595d)}.cdx-text-input__input:enabled:focus~.cdx-text-input__clear-icon,.cdx-text-input__input:enabled.cdx-text-input__input--has-value~.cdx-text-input__clear-icon{color:var(--color-base,#202122)}.cdx-text-input__input:enabled:focus~.cdx-text-input__icon,.cdx-text-input__input:enabled.cdx-text-input__input--has-value~.cdx-text-input__icon{opacity:1}.cdx-text-input__input:enabled:focus{border-color:var(--border-color-progressive--focus,#36c);box-shadow:inset 0 0 0 1px var(--box-shadow-color-progressive--focus,#36c);outline:1px solid transparent}.cdx-text-input__input:enabled:read-only{background-color:var(--background-color-neutral-subtle,#f8f9fa);border-color:var(--border-color-base,#a2a9b1)}.cdx-text-input__input:disabled{background-color:var(--background-color-disabled-subtle,#eaecf0);color:var(--color-disabled,#a2a9b1);-webkit-text-fill-color:var(--color-disabled,#a2a9b1);border-color:var(--border-color-disabled,#c8ccd1)}.cdx-text-input__input:disabled~.cdx-text-input__icon-vue{color:var(--color-disabled,#a2a9b1);pointer-events:none}.cdx-text-input__input:disabled~.cdx-text-input__icon{opacity:var(--opacity-icon-base--disabled,.51)}.cdx-text-input__input::placeholder{color:var(--color-placeholder,#72777d);opacity:1}.cdx-text-input__input[type=search]{-webkit-appearance:none;-moz-appearance:textfield}.cdx-text-input__input[type=search]::-webkit-search-decoration,.cdx-text-input__input[type=search]::-webkit-search-cancel-button{display:none}.cdx-text-input--has-start-icon .cdx-text-input__input{padding-left:calc(16px + 1.25rem)}.cdx-text-input--has-end-icon .cdx-text-input__input,.cdx-text-input--clearable .cdx-text-input__input{padding-right:calc(16px + 1rem)}.cdx-text-input--has-end-icon.cdx-text-input--clearable .cdx-text-input__input{padding-right:calc(24px + 2rem)}.cdx-text-input--status-error .cdx-text-input__input:enabled:not(:read-only):not(:focus){background-color:var(--background-color-error-subtle,#ffe9e5);color:var(--color-error,#bf3c2c);border-color:var(--border-color-error,#f54739)}.cdx-text-input--status-error .cdx-text-input__input:enabled:not(:read-only):not(:focus)::placeholder,.cdx-text-input--status-error .cdx-text-input__input:enabled:not(:read-only):not(:focus)~.cdx-text-input__start-icon,.cdx-text-input--status-error .cdx-text-input__input:enabled:not(:read-only):not(:focus)~.cdx-text-input__end-icon{color:var(--color-error,#bf3c2c)}.cdx-text-input--status-error .cdx-text-input__input:enabled:not(:read-only):not(:focus):hover{background-color:var(--background-color-error-subtle--hover,#ffdad3);color:var(--color-error--hover,#9f3526);border-color:var(--border-color-error--hover,#9f3526)}.cdx-text-input--status-error .cdx-text-input__input:enabled:not(:read-only):not(:focus):hover::placeholder,.cdx-text-input--status-error .cdx-text-input__input:enabled:not(:read-only):not(:focus):hover~.cdx-text-input__start-icon,.cdx-text-input--status-error .cdx-text-input__input:enabled:not(:read-only):not(:focus):hover~.cdx-text-input__end-icon{color:var(--color-error--hover,#9f3526)}
span.cargoFieldName{color:var(--color-subtle,#54595d)}span.searchresult{font-size:95%}span.searchmatch{font-weight:bold}.cargoQueryTooltipIcon{float:right;width:20px;height:20px;background-image:url("data:image/svg+xml,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 width=%2220%22 height=%2220%22 viewBox=%220 0 20 20%22%3E%3Ctitle%3Einfo%3C/title%3E%3Cpath d=%22M9.5 16A6.61 6.61 0 0 1 3 9.5 6.61 6.61 0 0 1 9.5 3 6.61 6.61 0 0 1 16 9.5 6.63 6.63 0 0 1 9.5 16zm0-14A7.5 7.5 0 1 0 17 9.5 7.5 7.5 0 0 0 9.5 2zm.5 6v4.08h1V13H8.07v-.92H9V9H8V8zM9 6h1v1H9z%22/%3E%3C/svg%3E");border:none;margin-right:5px}.addButton{padding:10px;background-image:url(/mw-1.43/extensions/Librarian/resources/images/add.png?e344f);background-color:#f8f9fa;border:1px #a2a9b1 solid;vertical-align:top}.deleteButton{padding:10px;background-image:url(/mw-1.43/extensions/Librarian/resources/images/subtract.png?09c41);background-color:#f8f9fa;border:1px #a2a9b1 solid;vertical-align:top}a.specialCargoQuery-extraPane-toggle{margin-top:7px;width:100%;text-align:left !important}div.specialCargoQuery-extraPane{background:linear-gradient(rgba(0,0,0,0.1),#f8f9fa 0.5em);border:solid 1px #a2a9b1;border-top:0;padding:10px} .ui-state-focus{border:none !important;background:#2a4b8d !important;color:#fff !important;margin:0px !important}.ui-menu .ui-menu-item a{color:#000;font-size:13px;padding:0.01em 0.25em;font-family:sans-serif}.ui-corner-all{border-radius:0px;padding:0px} input.cargo-drilldown-search{height:auto;width:631px;padding:6px 28px 6px 37px}  table.cargoTable,table.ext-cargo-datatable{border-collapse:collapse}table.cargoTable td,table.ext-cargo-datatable td{vertical-align:top;padding:5px;border:var(--border-color-subtle,#c8ccd1) 1px solid}table.cargoTable.noMerge tbody tr:nth-child(odd),table.ext-cargo-datatable.noMerge tbody tr:nth-child(odd),table.cargoTable.ext-cargo-datatable--merge-none tbody tr:nth-child(odd),table.ext-cargo-datatable.ext-cargo-datatable--merge-none tbody tr:nth-child(odd){background:var(--background-color-base,#fff)}table.cargoTable.noMerge tbody tr:nth-child(even),table.ext-cargo-datatable.noMerge tbody tr:nth-child(even),table.cargoTable.ext-cargo-datatable--merge-none tbody tr:nth-child(even),table.ext-cargo-datatable.ext-cargo-datatable--merge-none tbody tr:nth-child(even){background:var(--background-color-neutral-subtle,#f8f9fa)}table.cargoTable.mergeSimilarCells tbody td.odd,table.ext-cargo-datatable.mergeSimilarCells tbody td.odd,table.cargoTable.ext-cargo-datatable--merge-similar tbody td.odd,table.ext-cargo-datatable.ext-cargo-datatable--merge-similar tbody td.odd{background:var(--background-color-base,#fff)}table.cargoTable.mergeSimilarCells tbody td.even,table.ext-cargo-datatable.mergeSimilarCells tbody td.even,table.cargoTable.ext-cargo-datatable--merge-similar tbody td.even,table.ext-cargo-datatable.ext-cargo-datatable--merge-similar tbody td.even{background:var(--background-color-neutral-subtle,#f8f9fa)}
li#pt-themes{margin-right:0;position:relative;line-height:1.1em}li#pt-themes > input[type="checkbox"]{ display:none}li#pt-themes > label{color:#fff;padding:3px 0.4em;background-color:var(--wikigg-themeprefs-background-color);border-radius:0.4em;cursor:pointer;display:flex;align-items:center;justify-content:center;height:calc(2 / 3 * var(--wikigg-header-button-size))}li#pt-themes > label:hover{background-color:var(--wikigg-themeprefs-background-color--hover)}li#pt-themes > label > span{display:inline-flex;align-items:center}li#pt-themes > label > span::after{content:'';--color-base:#fff; min-width:16px;min-height:16px;width:1rem;height:1rem;display:inline-block;vertical-align:text-bottom;margin-left:0.35em}@supports not ((-webkit-mask-image:none) or (mask-image:none)){li#pt-themes > label > span::after{background-position:center;background-repeat:no-repeat; background-size:calc(max(1rem,16px))}}@supports (-webkit-mask-image:none) or (mask-image:none){li#pt-themes > label > span::after{ -webkit-mask-position:center;mask-position:center;-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat;-webkit-mask-size:calc(max(1rem,16px));mask-size:calc(max(1rem,16px)); }}@supports not ((-webkit-mask-image:none) or (mask-image:none)){li#pt-themes > label > span::after{background-image:url("data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"%23000\"><path d=\"m17.5 4.75-7.5 7.5-7.5-7.5L1 6.25l9 9 9-9z\"/></svg>");filter:invert(var(--filter-invert-icon,0));opacity:var(--opacity-icon-base,0.87)}}@supports (-webkit-mask-image:none) or (mask-image:none){li#pt-themes > label > span::after{ -webkit-mask-image:url("data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"%23000\"><path d=\"m17.5 4.75-7.5 7.5-7.5-7.5L1 6.25l9 9 9-9z\"/></svg>"); mask-image:url("data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"%23000\"><path d=\"m17.5 4.75-7.5 7.5-7.5-7.5L1 6.25l9 9 9-9z\"/></svg>");background-color:var(--color-base,#202122)}}li#pt-themes > input[type="checkbox"]:checked ~ label{background-color:var(--wikigg-themeprefs-background-color--active)}li#pt-themes > input[type="checkbox"]:checked ~ label::after{content:'';display:block;position:absolute;top:110%;transform:rotate(-45deg);width:2rem;height:2rem;background-color:var(--wikigg-themeprefs-background-color--active)}li#pt-themes > input[type="checkbox"]:checked ~ .ext-themetoggle-popup{display:block}#pt-themes .ext-themetoggle-popup{display:none;position:absolute;top:calc(100% - 2px);left:50%;min-width:10em;min-width:clamp(12em,12.5vw,200px);max-width:280px;transform:translateX(-50%);font-size:102%;padding-top:0.3rem;z-index:450}@media screen and (max-width:720px){#pt-themes .ext-themetoggle-popup{font-size:inherit}}#pt-themes .ext-themetoggle-popup .ext-themetoggle-popup__inner{padding:10px 16px;border:2px solid var(--wikigg-themeprefs-background-color--hover);border-radius:6px;background:var(--wikigg-themeprefs-content-background-color);box-shadow:0 0.05em 0.6em -0.15em rgba(0,0,0,0.3);display:flex;gap:8px;box-sizing:border-box}#pt-themes .ext-themetoggle-popup .ext-themetoggle-popup__inner .ext-themetoggle-popup__pane{flex:1 1}#pt-themes .ext-themetoggle-popup .ext-themetoggle-popup__inner .ext-themetoggle-popup__pane .ext-themetoggle-feature__heading{color:rgba(255,255,255,0.6);padding:0.4em 0.25em 0;font-size:90%}#pt-themes .ext-themetoggle-popup .ext-themetoggle-popup__inner .ext-themetoggle-popup__pane ul{display:flex;flex-direction:column;align-items:flex-start;gap:0.25em;margin:4px 0 0;padding:0}#pt-themes .ext-themetoggle-popup li:not(.mw-list-item){color:rgba(255,255,255,0.6);padding:0.4em 0.25em 0;font-size:90%}#pt-themes .ext-themetoggle-popup .mw-list-item{width:100%;border-radius:4px}#pt-themes .ext-themetoggle-popup .mw-list-item:hover{background:var(--wikigg-themeprefs-item-background-color--hover)}#pt-themes .ext-themetoggle-popup .mw-list-item[data-current]{background:var(--wikigg-themeprefs-item-background-color--active)}#pt-themes .ext-themetoggle-popup .mw-list-item a{display:block;padding:0.3em 0.4em 0.25em 0.3em;text-decoration:none;color:var(--wikigg-themeprefs-content-text-color)}@media screen and (max-width:calc(640px - 1px)){#p-personal #pt-themes:has(> input) > label span{text-indent:-9999px}#p-personal #pt-themes:has(> input) > label span::before{content:''; min-width:20px;min-height:20px;width:1.25rem;height:1.25rem;display:inline-block;vertical-align:text-bottom}@supports not ((-webkit-mask-image:none) or (mask-image:none)){#p-personal #pt-themes:has(> input) > label span::before{background-position:center;background-repeat:no-repeat; background-size:calc(max(1.25rem,20px))}}@supports (-webkit-mask-image:none) or (mask-image:none){#p-personal #pt-themes:has(> input) > label span::before{ -webkit-mask-position:center;mask-position:center;-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat;-webkit-mask-size:calc(max(1.25rem,20px));mask-size:calc(max(1.25rem,20px)); }}@supports not ((-webkit-mask-image:none) or (mask-image:none)){#p-personal #pt-themes:has(> input) > label span::before{background-image:url("data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"%23000\"><path d=\"M17.07 7.07V2.93h-4.14L10 0 7.07 2.93H2.93v4.14L0 10l2.93 2.93v4.14h4.14L10 20l2.93-2.93h4.14v-4.14L20 10zM10 16a6 6 0 116-6 6 6 0 01-6 6\"/><circle cx=\"10\" cy=\"10\" r=\"4.5\"/></svg>");filter:invert(var(--filter-invert-icon,0));opacity:var(--opacity-icon-base,0.87)}}@supports (-webkit-mask-image:none) or (mask-image:none){#p-personal #pt-themes:has(> input) > label span::before{ -webkit-mask-image:url("data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"%23000\"><path d=\"M17.07 7.07V2.93h-4.14L10 0 7.07 2.93H2.93v4.14L0 10l2.93 2.93v4.14h4.14L10 20l2.93-2.93h4.14v-4.14L20 10zM10 16a6 6 0 116-6 6 6 0 01-6 6\"/><circle cx=\"10\" cy=\"10\" r=\"4.5\"/></svg>"); mask-image:url("data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"%23000\"><path d=\"M17.07 7.07V2.93h-4.14L10 0 7.07 2.93H2.93v4.14L0 10l2.93 2.93v4.14h4.14L10 20l2.93-2.93h4.14v-4.14L20 10zM10 16a6 6 0 116-6 6 6 0 01-6 6\"/><circle cx=\"10\" cy=\"10\" r=\"4.5\"/></svg>");background-color:var(--color-base,#202122)}}@supports not ((-webkit-mask-image:none) or (mask-image:none)){html.view-dark #p-personal #pt-themes:has(> input) > label span::before{background-image:url("data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"%23000\"><path d=\"M17.39 15.14A7.33 7.33 0 0111.75 1.6c.23-.11.56-.23.79-.34a8.2 8.2 0 00-5.41.45 9 9 0 107 16.58 8.42 8.42 0 004.29-3.84 5.3 5.3 0 01-1.03.69\"/></svg>");filter:invert(var(--filter-invert-icon,0));opacity:var(--opacity-icon-base,0.87)}}@supports (-webkit-mask-image:none) or (mask-image:none){html.view-dark #p-personal #pt-themes:has(> input) > label span::before{ -webkit-mask-image:url("data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"%23000\"><path d=\"M17.39 15.14A7.33 7.33 0 0111.75 1.6c.23-.11.56-.23.79-.34a8.2 8.2 0 00-5.41.45 9 9 0 107 16.58 8.42 8.42 0 004.29-3.84 5.3 5.3 0 01-1.03.69\"/></svg>"); mask-image:url("data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"%23000\"><path d=\"M17.39 15.14A7.33 7.33 0 0111.75 1.6c.23-.11.56-.23.79-.34a8.2 8.2 0 00-5.41.45 9 9 0 107 16.58 8.42 8.42 0 004.29-3.84 5.3 5.3 0 01-1.03.69\"/></svg>");background-color:var(--color-base,#202122)}}@supports not ((-webkit-mask-image:none) or (mask-image:none)){html.theme-auto #p-personal #pt-themes:has(> input) > label span::before{background-image:url("data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"%23000\"><path d=\"M17 6.67V3h-4.2L9.87.07 6.94 3H3v3.67L.07 9.6 3 12.53V17h3.94l2.93 2.93L12.8 17H17v-4.47l2.93-2.93zm-7 8.93v-12a6.21 6.21 0 016 6 6.21 6.21 0 01-6 6\"/></svg>");filter:invert(var(--filter-invert-icon,0));opacity:var(--opacity-icon-base,0.87)}}@supports (-webkit-mask-image:none) or (mask-image:none){html.theme-auto #p-personal #pt-themes:has(> input) > label span::before{ -webkit-mask-image:url("data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"%23000\"><path d=\"M17 6.67V3h-4.2L9.87.07 6.94 3H3v3.67L.07 9.6 3 12.53V17h3.94l2.93 2.93L12.8 17H17v-4.47l2.93-2.93zm-7 8.93v-12a6.21 6.21 0 016 6 6.21 6.21 0 01-6 6\"/></svg>"); mask-image:url("data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"%23000\"><path d=\"M17 6.67V3h-4.2L9.87.07 6.94 3H3v3.67L.07 9.6 3 12.53V17h3.94l2.93 2.93L12.8 17H17v-4.47l2.93-2.93zm-7 8.93v-12a6.21 6.21 0 016 6 6.21 6.21 0 01-6 6\"/></svg>");background-color:var(--color-base,#202122)}}html.theme-auto #p-personal #pt-themes:has(> input) > label span::before[dir='rtl'],html[dir='rtl'] html.theme-auto #p-personal #pt-themes:has(> input) > label span::before:not([dir='ltr']){transform:scaleX(-1)}}
#p-lang-btn{align-self:center;float:right;margin-left:5px}#p-lang-btn .vector-menu-content li{margin:0.2em 0.5em}#p-lang-btn .vector-menu-content{right:0;left:unset;padding:0.2em 0}#p-lang-btn .vector-menu-content li:hover{background-color:rgba(var(--content-default-link-color--rgb),0.25);border-radius:2px}#p-lang-btn .menu li a{text-transform:none;color:var(--content-text-color)}#p-lang-btn-label::before,#p-lang-btn-label::after{content:'';background-position:100% 50%;background-repeat:no-repeat;display:inline-block;width:1.25em;height:1em;vertical-align:middle}#p-lang-btn-label::before{margin-right:5px;filter:var(--oo-ui-icon-filter--normal-progressive);background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20'%3E%3Cpath d='M20 18h-1.44a.61.61 0 0 1-.4-.12.81.81 0 0 1-.23-.31L17 15h-5l-1 2.54a.77.77 0 0 1-.22.3.59.59 0 0 1-.4.14H9l4.55-11.47h1.89zm-3.53-4.31L14.89 9.5a11.62 11.62 0 0 1-.39-1.24q-.09.37-.19.69l-.19.56-1.58 4.19zm-6.3-1.58a13.43 13.43 0 0 1-2.91-1.41 11.46 11.46 0 0 0 2.81-5.37H12V4H7.31a4 4 0 0 0-.2-.56C6.87 2.79 6.6 2 6.6 2l-1.47.5s.4.89.6 1.5H0v1.33h2.15A11.23 11.23 0 0 0 5 10.7a17.19 17.19 0 0 1-5 2.1q.56.82.87 1.38a23.28 23.28 0 0 0 5.22-2.51 15.64 15.64 0 0 0 3.56 1.77zM3.63 5.33h4.91a8.11 8.11 0 0 1-2.45 4.45 9.11 9.11 0 0 1-2.46-4.45z'/%3E%3C/svg%3E%0A")}#p-lang-btn-label::after{filter:var(--oo-ui-icon-filter--normal-progressive);background-image:linear-gradient(transparent,transparent),url("data:image/svg+xml,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 width=%2212%22 height=%2212%22 viewBox=%220 0 12 12%22%3E%3Cpath d=%22M11.05 3.996l-.965-1.053-4.035 3.86-3.947-3.86L1.05 3.996l5 5 5-5%22/%3E%3C/svg%3E")}
.mw-collapsible-toggle{float:right;-webkit-user-select:none;-moz-user-select:none;user-select:none}.mw-collapsible-toggle-default{-webkit-appearance:none;-moz-appearance:none;appearance:none;background:none;margin:0;padding:0;border:0;font:inherit}.mw-collapsible-toggle-default .mw-collapsible-text{color:var(--color-progressive,#36c);border-radius:2px;text-decoration:none; }.mw-collapsible-toggle-default .mw-collapsible-text:visited{color:var(--color-visited,#6a60b0)}.mw-collapsible-toggle-default .mw-collapsible-text:visited:hover{color:var(--color-visited--hover,#534fa3)}.mw-collapsible-toggle-default .mw-collapsible-text:visited:active{color:var(--color-visited--active,#353262)}.mw-collapsible-toggle-default .mw-collapsible-text:hover{color:var(--color-progressive--hover,#3056a9);text-decoration:underline}.mw-collapsible-toggle-default .mw-collapsible-text:active{color:var(--color-progressive--active,#233566);text-decoration:underline}.mw-collapsible-toggle-default .mw-collapsible-text:focus-visible{outline:solid 2px var(--outline-color-progressive--focus,#36c)}@supports not selector(:focus-visible){.mw-collapsible-toggle-default .mw-collapsible-text:focus{outline:solid 2px var(--outline-color-progressive--focus,#36c)}}.mw-collapsible-toggle-default .mw-collapsible-text .cdx-icon:not(.cdx-thumbnail__placeholder__icon--vue):last-child{min-width:12px;min-height:12px;width:1rem;height:1rem;padding-left:4px;vertical-align:middle}.mw-underline-always .mw-collapsible-toggle-default .mw-collapsible-text{text-decoration:underline}.mw-underline-never .mw-collapsible-toggle-default .mw-collapsible-text{text-decoration:none}.mw-collapsible-toggle-default::before{content:'['}.mw-collapsible-toggle-default::after{content:']'}.mw-customtoggle,.mw-collapsible-toggle{cursor:pointer} caption .mw-collapsible-toggle,.mw-content-ltr caption .mw-collapsible-toggle,.mw-content-rtl caption .mw-collapsible-toggle,.mw-content-rtl .mw-content-ltr caption .mw-collapsible-toggle,.mw-content-ltr .mw-content-rtl caption .mw-collapsible-toggle{float:none}
#mw-teleport-target{position:absolute;z-index:450}</style><style>
.mw-mmv-overlay{position:fixed;top:0;left:0;right:0;bottom:0;z-index:1000;background-color:#000;display:flex;justify-items:center;align-items:center;align-content:center;justify-content:center}.mw-mmv-overlay .cdx-progress-bar{max-width:80vw;min-width:20vw;width:20rem}body.mw-mmv-lightbox-open{overflow-y:auto;background-color:#000}body.mw-mmv-lightbox-open > *:not(.mw-notification-area-overlay){display:none}body.mw-mmv-lightbox-open > .mw-mmv-overlay{display:flex}body.mw-mmv-lightbox-open > .mw-mmv-wrapper{display:block}.mw-mmv-view-expanded .cdx-button__icon{ min-width:16px;min-height:16px;width:1rem;height:1rem;display:inline-block;vertical-align:text-bottom}@supports not ((-webkit-mask-image:none) or (mask-image:none)){.mw-mmv-view-expanded .cdx-button__icon{background-position:center;background-repeat:no-repeat; background-size:calc(max(1rem,16px))}}@supports (-webkit-mask-image:none) or (mask-image:none){.mw-mmv-view-expanded .cdx-button__icon{ -webkit-mask-position:center;mask-position:center;-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat;-webkit-mask-size:calc(max(1rem,16px));mask-size:calc(max(1rem,16px)); }}@supports not ((-webkit-mask-image:none) or (mask-image:none)){.mw-mmv-view-expanded .cdx-button__icon{background-image:url("data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"%23000\"><path d=\"M3 5a2 2 0 00-2 2v10a2 2 0 002 2h14a2 2 0 002-2V7a2 2 0 00-2-2zm0 11 3.5-4.5 2.5 3 3.5-4.5 4.5 6zM16 2a2 2 0 012 2H2a2 2 0 012-2z\"/></svg>");filter:invert(var(--filter-invert-icon,0));opacity:var(--opacity-icon-base,0.87)}.cdx-button:not(.cdx-button--weight-quiet):disabled .mw-mmv-view-expanded .cdx-button__icon,.cdx-button--weight-primary.cdx-button--action-progressive .mw-mmv-view-expanded .cdx-button__icon,.cdx-button--weight-primary.cdx-button--action-destructive .mw-mmv-view-expanded .cdx-button__icon{filter:invert(var(--filter-invert-primary-button-icon,1))}}@supports (-webkit-mask-image:none) or (mask-image:none){.mw-mmv-view-expanded .cdx-button__icon{ -webkit-mask-image:url("data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"%23000\"><path d=\"M3 5a2 2 0 00-2 2v10a2 2 0 002 2h14a2 2 0 002-2V7a2 2 0 00-2-2zm0 11 3.5-4.5 2.5 3 3.5-4.5 4.5 6zM16 2a2 2 0 012 2H2a2 2 0 012-2z\"/></svg>"); mask-image:url("data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"%23000\"><path d=\"M3 5a2 2 0 00-2 2v10a2 2 0 002 2h14a2 2 0 002-2V7a2 2 0 00-2-2zm0 11 3.5-4.5 2.5 3 3.5-4.5 4.5 6zM16 2a2 2 0 012 2H2a2 2 0 012-2z\"/></svg>");transition-property:background-color;transition-duration:100ms}}</style><meta name="ResourceLoaderDynamicStyles" content="">
<link rel="stylesheet" href="/load.php?lang=en&amp;modules=site.styles&amp;only=styles&amp;skin=vector">
<script async="" src="/load.php?lang=en&amp;skin=vector&amp;modules=ext.themes.apply&amp;only=scripts&amp;skin=vector&amp;raw=1"></script>
<meta name="generator" content="MediaWiki 1.43.1">
<meta name="robots" content="max-image-preview:standard">
<meta name="format-detection" content="telephone=no">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, minimum-scale=0.25, maximum-scale=5.0">
<style type="text/css">@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/cyrillic-ext/wght/normal.woff2);unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/latin/wght/normal.woff2);unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/vietnamese/wght/normal.woff2);unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/cyrillic/wght/normal.woff2);unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/latin-ext/wght/normal.woff2);unicode-range:U+0100-02AF,U+0304,U+0308,U+0329,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF;font-display:swap;}</style>
<link rel="apple-touch-icon" href="/images/4/4a/Site-favicon.ico">
<link rel="icon" href="/images/4/4a/Site-favicon.ico">
<link rel="search" type="application/opensearchdescription+xml" href="/rest.php/v1/search" title="Backpack Battles Wiki (en)">
<link rel="EditURI" type="application/rsd+xml" href="https://backpackbattles.wiki.gg/api.php?action=rsd">
<link rel="canonical" href="https://backpackbattles.wiki.gg/wiki/Healing_Herbs">
<link rel="license" href="https://creativecommons.org/licenses/by-sa/4.0">
<link rel="alternate" type="application/atom+xml" title="Backpack Battles Wiki Atom feed" href="/wiki/Special:RecentChanges?feed=atom">
	<meta property="og:type" content="article">

	<meta property="og:site_name" content="Backpack Battles Wiki">

	<meta property="og:title" content="Healing Herbs">

	<meta property="og:url" content="https://backpackbattles.wiki.gg/wiki/Healing_Herbs">

<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-P68LVFKN');</script>
<!-- End Google Tag Manager -->
<link rel="stylesheet" id="kumo-css" href="https://kumo.network-n.com/dist/css/wikigg.css"><script src="https://kumo.network-n.com/dist/app.js" async="" site="wikigg"></script><link rel="preload" href="https://boot.pbstck.com/v1/tag/fc9322df-259c-4ae5-a861-d8350b75d4eb" as="script"><link rel="preload" href="https://00917082-71e9-498e-8343-00c3df06b798.edge.permutive.app/00917082-71e9-498e-8343-00c3df06b798-web.js" as="script"><link rel="preload" href="https://btloader.com/tag?o=5684350990417920&amp;upapi=true" as="script"><link rel="preload" href="https://cdn.privacy-mgmt.com/unified/wrapperMessagingWithoutDetection.js" as="script"><link rel="preload" href="https://www.googletagmanager.com/gtag/js?id=G-0CPE0JFSCT" as="script"><link rel="preload" href="https://securepubads.g.doubleclick.net/tag/js/gpt.js" as="script"><style>@media only screen and (min-width:900px){.bfa-maximum div[id*=primis_playerSekindoSPlayer][style*="position: fixed"]{top:var(--nnVideoOffsetTop,140px)!important}.bfa-loaded.bfa-collapsed div[id*=primis_playerSekindoSPlayer][style*="position: fixed"]{bottom:100px!important;top:auto!important}}@media(max-width:899px){.bfa-maximum div[id^=primis_playerSekindoSPlayer][style^="overflow: visible"][style*="position: fixed"]{bottom:140px!important}}</style></head>
<body class="wgg-dom-version-1_43 skin-vector-legacy mediawiki ltr sitedir-ltr mw-hide-empty-elt ns-0 ns-subject page-Healing_Herbs rootpage-Healing_Herbs skin-vector action-view skin--responsive"><header id="wgg-netbar">
	<div class="wgg-netbar__left">
		<div class="wgg-netbar__logo">
			<a href="/" aria-label="wiki.gg">
				<img height="25" alt="wiki.gg logo" src="https://commons.wiki.gg/images/2/27/Network_header_logo.svg?feeba7">
			</a>
		</div>
	</div>

	<nav class="wgg-netbar__user-links" aria-label="Personal tools">
		<div id="p-personal" class="mw-portlet mw-portlet-personal">
		    
		    <ul>
		            <li id="pt-createaccount" class="mw-list-item wgg-netbar__desktop-only"><a data-mw="interface" class="" href="/wiki/Special:CreateAccount?returnto=Healing+Herbs" title="You are encouraged to create an account and log in; however, it is not mandatory"><span>Create account</span></a></li>
		            <li id="pt-login" class="mw-list-item wgg-netbar__desktop-only"><a data-mw="interface" class="" href="/wiki/Special:UserLogin?returnto=Healing+Herbs" title="You are encouraged to log in; however, it is not mandatory [alt-shift-o]" accesskey="o"><span>Log in</span></a></li>
		    </ul>
		    
		</div>

		<div id="wgg-user-menu-overflow" class="wgg-netbar__dropdown wgg-netbar__mobile-only">
			<input type="checkbox" id="wgg-user-menu-overflow-checkbox" role="button" aria-haspopup="true" aria-labelledby="wgg-user-menu-overflow-label">
			<label id="wgg-user-menu-overflow-label" for="wgg-user-menu-overflow-checkbox" class="wgg-netbar__icon-button"><span class="wgg-netbar-icon wgg-netbar-icon--overflow"></span><span>Toggle personal tools menu</span></label>

			<div class="wgg-netbar__dropdown-content">
				<div id="p-user-menu" class="mw-portlet mw-portlet-user-menu">
				    
				    <ul>
				            <li id="pt-createaccount-overflow" class="mw-list-item wgg-netbar__mobile-only"><a data-mw="interface" class="" href="/wiki/Special:CreateAccount?returnto=Healing+Herbs" title="You are encouraged to create an account and log in; however, it is not mandatory"><span>Create account</span></a></li>
				            <li id="pt-login-overflow" class="mw-list-item wgg-netbar__mobile-only"><a data-mw="interface" class="" href="/wiki/Special:UserLogin?returnto=Healing+Herbs" title="You are encouraged to log in; however, it is not mandatory [alt-shift-o]" accesskey="o"><span>Log in</span></a></li>
				    </ul>
				    
				</div>
			</div>
		</div>
</nav>

</header>

<div id="mw-page-base" class="noprint"></div>
<div id="mw-head-base" class="noprint"></div>
<div class="content-wrapper">
	<div id="mw-navigation">
		<h2>Navigation menu</h2>
		<div id="mw-head">
			<div id="left-navigation">
				
<div id="p-namespaces" class="mw-portlet mw-portlet-namespaces vector-menu-tabs vector-menu-tabs-legacy vectorTabs vector-menu" aria-labelledby="p-namespaces-label">
	<h3 id="p-namespaces-label" class="vector-menu-heading ">
		<span class="vector-menu-heading-label">Namespaces</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="ca-nstab-main" class="selected mw-list-item"><a href="/wiki/Healing_Herbs" title="View the content page [alt-shift-c]" accesskey="c"><span>Page</span></a></li><li id="ca-talk" class="new mw-list-item"><a href="/wiki/Talk:Healing_Herbs?action=edit&amp;redlink=1" rel="discussion" class="new" title="Discussion about the content page (page does not exist) [alt-shift-t]" accesskey="t"><span>Discussion</span></a></li>
		</ul>
		
	</div>
</div>

				
<div id="p-variants" class="mw-portlet mw-portlet-variants emptyPortlet vector-menu-dropdown vector-menu" aria-labelledby="p-variants-label">
	<input type="checkbox" id="p-variants-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-p-variants" class="vector-menu-checkbox" aria-labelledby="p-variants-label">
	<label id="p-variants-label" class="vector-menu-heading ">
		<span class="vector-menu-heading-label">English</span>
	</label>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			
		</ul>
		
	</div>
</div>

			</div>
			<div id="right-navigation">
				
<div id="p-views" class="mw-portlet mw-portlet-views vector-menu-tabs vector-menu-tabs-legacy vectorTabs vector-menu" aria-labelledby="p-views-label">
	<h3 id="p-views-label" class="vector-menu-heading ">
		<span class="vector-menu-heading-label">Views</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="ca-view" class="selected mw-list-item collapsible" style=""><a href="/wiki/Healing_Herbs"><span>Read</span></a></li><li id="ca-edit" class="mw-list-item collapsible"><a href="/wiki/Special:CreateAccount?warning=accountrequiredtoedit&amp;returnto=Healing_Herbs&amp;returntoquery=action%3Dedit" title="Edit this page [alt-shift-e]" accesskey="e"><span>Sign up to edit</span></a></li><li id="ca-viewsource" class="mw-list-item collapsible"><a href="/wiki/Healing_Herbs?action=edit" title="This page is protected.
You can view its source [alt-shift-e]" accesskey="e"><span>View source</span></a></li><li id="ca-history" class="mw-list-item collapsible"><a href="/wiki/Healing_Herbs?action=history" title="Past revisions of this page [alt-shift-h]" accesskey="h"><span>History</span></a></li>
		</ul>
		
	</div>
</div>

				
<div id="p-cactions" class="mw-portlet mw-portlet-cactions vectorMenu vector-menu-dropdown vector-menu" aria-labelledby="p-cactions-label" title="More options">
	<input type="checkbox" id="p-cactions-checkbox" role="button" aria-haspopup="true" data-event-name="ui.dropdown-p-cactions" class="vector-menu-checkbox" aria-labelledby="p-cactions-label">
	<label id="p-cactions-label" class="vector-menu-heading ">
		<span class="vector-menu-heading-label">More</span>
	</label>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="ca-cargo-purge" class="mw-list-item"><a href="/wiki/Healing_Herbs?action=purge"><span>Purge cache</span></a></li>
		</ul>
		
	</div>
</div>

				
<div id="p-search" role="search" class="vector-search-box-vue  vector-search-box-show-thumbnail vector-search-box-auto-expand-width vector-search-box">
	<h3>Search</h3>
	<form action="/wiki/Special:Search" id="searchform" class="vector-search-box-form">
		<div id="simpleSearch" class="vector-search-box-inner" data-search-loc="header-navigation">
			<input class="vector-search-box-input" type="search" name="search" placeholder="Search Backpack Battles Wiki" aria-label="Search Backpack Battles Wiki" autocapitalize="sentences" spellcheck="false" title="Search Backpack Battles Wiki [alt-shift-f]" accesskey="f" id="searchInput">
			<input id="mw-searchButton" class="searchButton mw-fallbackSearchButton" type="submit" name="fulltext" title="Search the pages for this text" value="Search">
			<input id="searchButton" class="searchButton" type="submit" name="go" title="Go to a page with this exact name if it exists" value="Go">
		</div>
	</form>
</div>

			</div>
		</div>
		
<div id="mw-panel">
	<div id="p-logo" role="banner">
		<a class="mw-wiki-logo" href="/" title="Visit the main page"></a>
	</div>
	
<div id="p-Content" class="mw-portlet mw-portlet-Content vector-menu-portal portal vector-menu" aria-labelledby="p-Content-label">
	<h3 id="p-Content-label" class="vector-menu-heading ">
		<span class="vector-menu-heading-label">Content</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="n-mainpage-description" class="mw-list-item"><a href="/" title="Visit the main page [alt-shift-z]" accesskey="z"><span>Main page</span></a></li><li id="n-Backpack-Battles" class="mw-list-item"><a href="/wiki/Backpack_Battles"><span>Backpack Battles</span></a></li><li id="n-Items" class="mw-list-item"><a href="/wiki/Items"><span>Items</span></a></li><li id="n-Game-Mechanics" class="mw-list-item"><a href="/wiki/Game_Mechanics"><span>Game Mechanics</span></a></li><li id="n-Characters" class="mw-list-item"><a href="/wiki/Characters"><span>Characters</span></a></li><li id="n-Version-History" class="mw-list-item"><a href="/wiki/Version_History"><span>Version History</span></a></li>
		</ul>
		
	</div>
</div>

	
<div id="p-navigation" class="mw-portlet mw-portlet-navigation vector-menu-portal portal vector-menu" aria-labelledby="p-navigation-label">
	<h3 id="p-navigation-label" class="vector-menu-heading ">
		<span class="vector-menu-heading-label">Navigation</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="n-portal" class="mw-list-item"><a href="/wiki/Backpack_Battles_Wiki:Community_portal" title="About the project, what you can do, where to find things"><span>Community portal</span></a></li><li id="n-recentchanges" class="mw-list-item"><a href="/wiki/Special:RecentChanges" title="A list of recent changes in the wiki [alt-shift-r]" accesskey="r"><span>Recent changes</span></a></li><li id="n-randompage" class="mw-list-item"><a href="/wiki/Special:Random" title="Load a random page [alt-shift-x]" accesskey="x"><span>Random page</span></a></li><li id="n-help-mediawiki" class="mw-list-item"><a href="https://www.mediawiki.org/wiki/Special:MyLanguage/Help:Contents"><span>Help about MediaWiki</span></a></li>
		</ul>
		
	</div>
</div>

<div id="p-tb" class="mw-portlet mw-portlet-tb vector-menu-portal portal vector-menu" aria-labelledby="p-tb-label">
	<h3 id="p-tb-label" class="vector-menu-heading ">
		<span class="vector-menu-heading-label">Tools</span>
	</h3>
	<div class="vector-menu-content body">
		
		<ul class="vector-menu-content-list menu">
			
			<li id="t-whatlinkshere" class="mw-list-item"><a href="/wiki/Special:WhatLinksHere/Healing_Herbs" title="A list of all wiki pages that link here [alt-shift-j]" accesskey="j"><span>What links here</span></a></li><li id="t-recentchangeslinked" class="mw-list-item"><a href="/wiki/Special:RecentChangesLinked/Healing_Herbs" rel="nofollow" title="Recent changes in pages linked from this page"><span>Related changes</span></a></li><li id="t-newpage" class="mw-list-item"><a href="/wiki/Special:NewPage" accesskey="]"><span>New page</span></a></li><li id="t-specialpages" class="mw-list-item"><a href="/wiki/Special:SpecialPages" title="A list of all special pages [alt-shift-q]" accesskey="q"><span>Special pages</span></a></li><li id="t-print" class="mw-list-item"><a href="javascript:print();" rel="alternate" title="Printable version of this page [alt-shift-p]" accesskey="p"><span>Printable version</span></a></li><li id="t-info" class="mw-list-item"><a href="/wiki/Healing_Herbs?action=info" title="More information about this page"><span>Page information</span></a></li><li id="t-cargopagevalueslink" class="mw-list-item"><a href="/wiki/Healing_Herbs?action=pagevalues" rel="cargo-pagevalues"><span>Cargo data</span></a></li>
		<li class="mw-list-item mw-list-item-js" id="t-collapsible-toggle-all"><a href="#" title="Expand all collapsible elements on the current page" role="button" aria-expanded="false"><span>Expand all</span></a></li></ul>
		
	</div>
</div>

	
</div>

	</div>
	<div id="content" class="mw-body" role="main">
		<aside id="wikigg-sl-header" data-mw="WggShowcaseLayout">
        <div class="wikigg-showcase__unit wikigg-showcase__reserved--lb" data-wgg-unit-type="internal"></div>
</aside>

		<div class="content-body">
			<main>
				<a id="top"></a>
				<div id="siteNotice"></div>
				<div class="mw-indicators">
				</div>
				<h1 id="firstHeading" class="firstHeading mw-first-heading"><span class="mw-page-title-main">Healing Herbs</span></h1>
				<div id="bodyContent" class="vector-body">
					<div id="siteSub" class="noprint">From Backpack Battles Wiki</div>
					<div id="contentSub"></div>
					<div id="contentSub2"></div>
					
					<div id="jump-to-nav"></div>
					<a class="mw-jump-link" href="#mw-head">Jump to navigation</a>
					<a class="mw-jump-link" href="#searchInput">Jump to search</a>
					<div id="mw-content-text" class="mw-body-content"><div class="mw-content-ltr mw-parser-output" lang="en" dir="ltr"><div class="infobox item"><div class="title"><span class="name">Healing Herbs </span></div><div class="section images"><a href="/wiki/File:HealingHerbs.png" class="image"><img alt="HealingHerbs.png" src="/images/c/c4/HealingHerbs.png?408827" decoding="async" loading="lazy" width="160" height="160" data-file-width="160" data-file-height="160"></a></div><div class="section statistics"><div class="title">Statistics</div><table class="stat"><tbody><tr><th><a href="/wiki/Rarity" title="Rarity">Rarity</a></th><td><a href="/wiki/Common" title="Common">Common</a></td></tr><tr><th>Type</th><td><a href="/wiki/Nature" title="Nature"><img alt="Nature" src="/images/thumb/5/54/Icon_Nature.png/15px-Icon_Nature.png?d4077f" decoding="async" loading="lazy" width="15" height="15" data-file-width="55" data-file-height="56"></a><a href="/wiki/Accessory" title="Accessory">Accessory</a></td></tr><tr><th>Cost</th><td>4 <img alt="Gold" src="/images/thumb/d/d3/GoldCoin.png/15px-GoldCoin.png?eeeedb" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100"></td></tr><tr><th><a href="/wiki/Characters#Playable_Classes" title="Characters">Class</a></th><td><a href="/wiki/Neutral_items" title="Neutral items"><img alt="Neutral" src="/images/thumb/6/61/Icon_Neutral.png/20px-Icon_Neutral.png?5f0cfa" decoding="async" loading="lazy" width="20" height="20" data-file-width="182" data-file-height="182"></a><a href="/wiki/Neutral_items" title="Neutral items">Neutral</a></td></tr><tr><th>Effect</th><td>
<p><b>Start of battle:</b> Gain 2 <a href="/wiki/Regeneration" title="Regeneration"><img alt="Regeneration" src="/images/thumb/5/53/Regeneration.png/15px-Regeneration.png?7bd3bc" decoding="async" loading="lazy" width="15" height="15" data-file-width="100" data-file-height="100"></a>.
</p>
                </td></tr><tr><th>In shop</th><td>
                Yes</td></tr></tbody></table></div><div class="section item-grid"><div class="title">Grid</div><div><p style="display:flex; align-items:center; justify-content:center; margin: 0; line-height: 15px;">
<a href="/wiki/File:Cell.png" class="image"><img alt="Cell.png" src="/images/thumb/b/b8/Cell.png/15px-Cell.png?9ec987" decoding="async" loading="lazy" width="15" height="15" style="vertical-align: bottom" data-file-width="80" data-file-height="80"></a></p></div></div><div class="section recipes"><div class="title"><a href="/wiki/Recipe" title="Recipe">Recipe</a></div><table class="stat"><tbody><tr><th>Used in</th><td>
<a href="/wiki/Rat_Chef" title="Rat Chef">Rat Chef</a>,  <a href="/wiki/Strong_Health_Potion" title="Strong Health Potion">Strong Health Potion</a></td></tr></tbody></table></div></div>
<h2><span class="mw-headline" id="History">History</span></h2>
<table class="styled">
<caption><a href="/wiki/Version_History" title="Version History">Version History</a>
</caption>
<tbody><tr>
<th>Version
</th>
<th>Changes
</th></tr>
<tr>
<td>0.5.6
</td>
<td>Regen 1 -&gt; 2, Cost 2g -&gt; 4g
</td></tr></tbody></table>
<table class="navbox mw-collapsible mw-made-collapsible" data-expandtext="Show" data-collapsetext="Hide"><tbody><tr class="navbox-title"><th colspan="2" scope="col"><button type="button" class="mw-collapsible-toggle mw-collapsible-toggle-default" aria-expanded="true" tabindex="0"><span class="mw-collapsible-text">Hide</span></button><span class="navbox-vde"><a href="/wiki/Template:Items" title="Template:Items"><span title="View this navbox template">v</span></a> · <span class="plainlinks"><a class="text" href="https://backpackbattles.wiki.gg/wiki/Template_talk:Items"><span title="Discuss this navbox template">d</span></a> · <a class="text" href="https://backpackbattles.wiki.gg/wiki/Template:Items?action=edit"><span title="Edit this navbox template">e</span></a></span></span><a href="/wiki/Items" title="Items">Items</a></th></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Accessory" title="Accessory">Accessory</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Acorn_Collar" title="Acorn Collar">Acorn Collar</a>&nbsp;• <a href="/wiki/Amulet_of_Alchemy" title="Amulet of Alchemy">Amulet of Alchemy</a>&nbsp;• <a href="/wiki/Amulet_of_Darkness" title="Amulet of Darkness">Amulet of Darkness</a>&nbsp;• <a href="/wiki/Amulet_of_Energy" title="Amulet of Energy">Amulet of Energy</a>&nbsp;• <a href="/wiki/Amulet_of_Feasting" title="Amulet of Feasting">Amulet of Feasting</a>&nbsp;• <a href="/wiki/Amulet_of_Life" title="Amulet of Life">Amulet of Life</a>&nbsp;• <a href="/wiki/Amulet_of_Steel" title="Amulet of Steel">Amulet of Steel</a>&nbsp;• <a href="/wiki/Amulet_of_the_Wild" title="Amulet of the Wild">Amulet of the Wild</a>&nbsp;• <a href="/wiki/Anvil" title="Anvil">Anvil</a>&nbsp;• <a href="/wiki/Bag_of_Stones" title="Bag of Stones">Bag of Stones</a>&nbsp;• <a href="/wiki/Blood_Amulet" title="Blood Amulet">Blood Amulet</a>&nbsp;• <a href="/wiki/Book_of_Ice" title="Book of Ice">Book of Ice</a>&nbsp;• <a href="/wiki/Box_of_Riches" title="Box of Riches">Box of Riches</a>&nbsp;• <a href="/wiki/Bunch_of_Coins" title="Bunch of Coins">Bunch of Coins</a>&nbsp;• <a href="/wiki/Burning_Banner" title="Burning Banner">Burning Banner</a>&nbsp;• <a href="/wiki/Cauldron" title="Cauldron">Cauldron</a>&nbsp;• <a href="/wiki/Customer_Card" title="Customer Card">Customer Card</a>&nbsp;• <a href="/wiki/Dark_Lantern" title="Dark Lantern">Dark Lantern</a>&nbsp;• <a href="/wiki/Deck_of_Cards" title="Deck of Cards">Deck of Cards</a>&nbsp;• <a href="/wiki/Deerwood_Guardian" title="Deerwood Guardian">Deerwood Guardian</a>&nbsp;• <a href="/wiki/Djinn_Lamp" title="Djinn Lamp">Djinn Lamp</a>&nbsp;• <a href="/wiki/Draconic_Orb" title="Draconic Orb">Draconic Orb</a>&nbsp;• <a href="/wiki/Dragon_Nest" title="Dragon Nest">Dragon Nest</a>&nbsp;• <a href="/wiki/Fanfare" title="Fanfare">Fanfare</a>&nbsp;• <a href="/wiki/Flame" title="Flame">Flame</a>&nbsp;• <a href="/wiki/Flame_Badge" title="Flame Badge">Flame Badge</a>&nbsp;• <a href="/wiki/Flute" title="Flute">Flute</a>&nbsp;• <a href="/wiki/Friendly_Fire" title="Friendly Fire">Friendly Fire</a>&nbsp;• <a href="/wiki/Frozen_Flame" title="Frozen Flame">Frozen Flame</a>&nbsp;• <a class="mw-selflink selflink">Healing Herbs</a>&nbsp;• <a href="/wiki/Heart_Container" title="Heart Container">Heart Container</a>&nbsp;• <a href="/wiki/Heart_of_Darkness" title="Heart of Darkness">Heart of Darkness</a>&nbsp;• <a href="/wiki/King_Crown" title="King Crown">King Crown</a>&nbsp;• <a href="/wiki/Leaf_Badge" title="Leaf Badge">Leaf Badge</a>&nbsp;• <a href="/wiki/Lucky_Clover" title="Lucky Clover">Lucky Clover</a>&nbsp;• <a href="/wiki/Lucky_Piggy" title="Lucky Piggy">Lucky Piggy</a>&nbsp;• <a href="/wiki/Mana_Orb" title="Mana Orb">Mana Orb</a>&nbsp;• <a href="/wiki/Maneki-neko" title="Maneki-neko">Maneki-neko</a>&nbsp;• <a href="/wiki/Mega_Clover" title="Mega Clover">Mega Clover</a>&nbsp;• <a href="/wiki/Miss_Fortune" title="Miss Fortune">Miss Fortune</a>&nbsp;• <a href="/wiki/Mr._Struggles" title="Mr. Struggles">Mr. Struggles</a>&nbsp;• <a href="/wiki/Mrs._Struggles" title="Mrs. Struggles">Mrs. Struggles</a>&nbsp;• <a href="/wiki/Nocturnal_Lock_Lifter" title="Nocturnal Lock Lifter">Nocturnal Lock Lifter</a>&nbsp;• <a href="/wiki/Oil_Lamp" title="Oil Lamp">Oil Lamp</a>&nbsp;• <a href="/wiki/Piercing_Arrow" title="Piercing Arrow">Piercing Arrow</a>&nbsp;• <a href="/wiki/Piggybank" title="Piggybank">Piggybank</a>&nbsp;• <a href="/wiki/Platinum_Customer_Card" title="Platinum Customer Card">Platinum Customer Card</a>&nbsp;• <a href="/wiki/Pocket_Sand" title="Pocket Sand">Pocket Sand</a>&nbsp;• <a href="/wiki/Poison_Ivy" title="Poison Ivy">Poison Ivy</a>&nbsp;• <a href="/wiki/Present" title="Present">Present</a>&nbsp;• <a href="/wiki/Prismatic_Orb" title="Prismatic Orb">Prismatic Orb</a>&nbsp;• <a href="/wiki/Rainbow_Badge" title="Rainbow Badge">Rainbow Badge</a>&nbsp;• <a href="/wiki/Shaman_Mask" title="Shaman Mask">Shaman Mask</a>&nbsp;• <a href="/wiki/Shepherd%27s_Crook" class="mw-redirect" title="Shepherd's Crook">Shepherd's Crook</a>&nbsp;• <a href="/wiki/Shiny_Shell" title="Shiny Shell">Shiny Shell</a>&nbsp;• <a href="/wiki/Skull_Badge" title="Skull Badge">Skull Badge</a>&nbsp;• <a href="/wiki/Snowball" title="Snowball">Snowball</a>&nbsp;• <a href="/wiki/Spiked_Collar" title="Spiked Collar">Spiked Collar</a>&nbsp;• <a href="/wiki/Stable_Recombobulator" title="Stable Recombobulator">Stable Recombobulator</a>&nbsp;• <a href="/wiki/Stone_Badge" title="Stone Badge">Stone Badge</a>&nbsp;• <a href="/wiki/Time_Dilator" title="Time Dilator">Time Dilator</a>&nbsp;• <a href="/wiki/Unidentified_Amulet" title="Unidentified Amulet">Unidentified Amulet</a>&nbsp;• <a href="/wiki/Unstable_Recombobulator" title="Unstable Recombobulator">Unstable Recombobulator</a>&nbsp;• <a href="/wiki/Walrus_Tusk" title="Walrus Tusk">Walrus Tusk</a>&nbsp;• <a href="/wiki/Whetstone" title="Whetstone">Whetstone</a>&nbsp;• <a href="/wiki/Wolf_Badge" title="Wolf Badge">Wolf Badge</a>&nbsp;• <a href="/wiki/Wolf_Emblem" title="Wolf Emblem">Wolf Emblem</a>&nbsp;• <a href="/wiki/Wonky_Snowman" title="Wonky Snowman">Wonky Snowman</a>&nbsp;• <a href="/wiki/Yggdrasil_Leaf" title="Yggdrasil Leaf">Yggdrasil Leaf</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Armor" title="Armor">Armor</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Corrupted_Armor" title="Corrupted Armor">Corrupted Armor</a>&nbsp;• <a href="/wiki/Dragonscale_Armor" title="Dragonscale Armor">Dragonscale Armor</a>&nbsp;• <a href="/wiki/Holy_Armor" title="Holy Armor">Holy Armor</a>&nbsp;• <a href="/wiki/Ice_Armor" title="Ice Armor">Ice Armor</a>&nbsp;• <a href="/wiki/Leather_Armor" title="Leather Armor">Leather Armor</a>&nbsp;• <a href="/wiki/Moon_Armor" title="Moon Armor">Moon Armor</a>&nbsp;• <a href="/wiki/Stone_Armor" title="Stone Armor">Stone Armor</a>&nbsp;• <a href="/wiki/Sun_Armor" title="Sun Armor">Sun Armor</a>&nbsp;• <a href="/wiki/Vampiric_Armor" title="Vampiric Armor">Vampiric Armor</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Bag" title="Bag">Bag</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Box_of_Prosperity" title="Box of Prosperity">Box of Prosperity</a>&nbsp;• <a href="/wiki/Duffle_Bag" title="Duffle Bag">Duffle Bag</a>&nbsp;• <a href="/wiki/Fanny_Pack" title="Fanny Pack">Fanny Pack</a>&nbsp;• <a href="/wiki/Fire_Pit" title="Fire Pit">Fire Pit</a>&nbsp;• <a href="/wiki/Holdall" title="Holdall">Holdall</a>&nbsp;• <a href="/wiki/Leather_Bag" title="Leather Bag">Leather Bag</a>&nbsp;• <a href="/wiki/Offering_Bowl" title="Offering Bowl">Offering Bowl</a>&nbsp;• <a href="/wiki/Potion_Belt" title="Potion Belt">Potion Belt</a>&nbsp;• <a href="/wiki/Protective_Purse" title="Protective Purse">Protective Purse</a>&nbsp;• <a href="/wiki/Ranger_Bag" title="Ranger Bag">Ranger Bag</a>&nbsp;• <a href="/wiki/Relic_Case" title="Relic Case">Relic Case</a>&nbsp;• <a href="/wiki/Sack_of_Surprises" title="Sack of Surprises">Sack of Surprises</a>&nbsp;• <a href="/wiki/Stamina_Sack" title="Stamina Sack">Stamina Sack</a>&nbsp;• <a href="/wiki/Storage_Coffin" title="Storage Coffin">Storage Coffin</a>&nbsp;• <a href="/wiki/Utility_Pouch" title="Utility Pouch">Utility Pouch</a>&nbsp;• <a href="/wiki/Vineweave_Basket" title="Vineweave Basket">Vineweave Basket</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Food" title="Food">Food</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Banana" title="Banana">Banana</a>&nbsp;• <a href="/wiki/Big_Bowl_of_Treats" title="Big Bowl of Treats">Big Bowl of Treats</a>&nbsp;• <a href="/wiki/Blueberries" title="Blueberries">Blueberries</a>&nbsp;• <a href="/wiki/Carrot" title="Carrot">Carrot</a>&nbsp;• <a href="/wiki/Cheese" title="Cheese">Cheese</a>&nbsp;• <a href="/wiki/Chili_Pepper" title="Chili Pepper">Chili Pepper</a>&nbsp;• <a href="/wiki/Doom_Cap" title="Doom Cap">Doom Cap</a>&nbsp;• <a href="/wiki/Fly_Agaric" title="Fly Agaric">Fly Agaric</a>&nbsp;• <a href="/wiki/Garlic" title="Garlic">Garlic</a>&nbsp;• <a href="/wiki/Gingerbread_Jerry" title="Gingerbread Jerry">Gingerbread Jerry</a>&nbsp;• <a href="/wiki/Pineapple" title="Pineapple">Pineapple</a>&nbsp;• <a href="/wiki/Pumpkin" title="Pumpkin">Pumpkin</a>&nbsp;• <a href="/wiki/Snowcake" title="Snowcake">Snowcake</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Gemstone" title="Gemstone">Gemstone</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Amethyst" title="Amethyst">Amethyst</a>&nbsp;• <a href="/wiki/Badger_Rune" title="Badger Rune">Badger Rune</a>&nbsp;• <a href="/wiki/Burning_Coal" title="Burning Coal">Burning Coal</a>&nbsp;• <a href="/wiki/Corrupted_Crystal" title="Corrupted Crystal">Corrupted Crystal</a>&nbsp;• <a href="/wiki/Elephant_Rune" title="Elephant Rune">Elephant Rune</a>&nbsp;• <a href="/wiki/Emerald" title="Emerald">Emerald</a>&nbsp;• <a href="/wiki/Hawk_Rune" title="Hawk Rune">Hawk Rune</a>&nbsp;• <a href="/wiki/Lump_of_Coal" title="Lump of Coal">Lump of Coal</a>&nbsp;• <a href="/wiki/Ruby" title="Ruby">Ruby</a>&nbsp;• <a href="/wiki/Sapphire" title="Sapphire">Sapphire</a>&nbsp;• <a href="/wiki/Tim" title="Tim">Tim</a>&nbsp;• <a href="/wiki/Topaz" title="Topaz">Topaz</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Gloves" title="Gloves">Gloves</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Dragon_Claws" title="Dragon Claws">Dragon Claws</a>&nbsp;• <a href="/wiki/Gloves_of_Haste" title="Gloves of Haste">Gloves of Haste</a>&nbsp;• <a href="/wiki/Gloves_of_Power" title="Gloves of Power">Gloves of Power</a>&nbsp;• <a href="/wiki/Vampiric_Gloves" title="Vampiric Gloves">Vampiric Gloves</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Helmet" title="Helmet">Helmet</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Cap_of_Discomfort" title="Cap of Discomfort">Cap of Discomfort</a>&nbsp;• <a href="/wiki/Cap_of_Resilience" title="Cap of Resilience">Cap of Resilience</a>&nbsp;• <a href="/wiki/Glowing_Crown" title="Glowing Crown">Glowing Crown</a>&nbsp;• <a href="/wiki/Stone_Helm" title="Stone Helm">Stone Helm</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Pet" title="Pet">Pet</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Amethyst_Egg" title="Amethyst Egg">Amethyst Egg</a>&nbsp;• <a href="/wiki/Armored_Courage_Puppy" title="Armored Courage Puppy">Armored Courage Puppy</a>&nbsp;• <a href="/wiki/Armored_Power_Puppy" title="Armored Power Puppy">Armored Power Puppy</a>&nbsp;• <a href="/wiki/Armored_Wisdom_Puppy" title="Armored Wisdom Puppy">Armored Wisdom Puppy</a>&nbsp;• <a href="/wiki/Blood_Goobert" title="Blood Goobert">Blood Goobert</a>&nbsp;• <a href="/wiki/Carrot_Goobert" title="Carrot Goobert">Carrot Goobert</a>&nbsp;• <a href="/wiki/Cheese_Goobert" title="Cheese Goobert">Cheese Goobert</a>&nbsp;• <a href="/wiki/Chili_Goobert" title="Chili Goobert">Chili Goobert</a>&nbsp;• <a href="/wiki/Chtulhu" title="Chtulhu">Chtulhu</a>&nbsp;• <a href="/wiki/Courage_Puppy" title="Courage Puppy">Courage Puppy</a>&nbsp;• <a href="/wiki/Cubert" title="Cubert">Cubert</a>&nbsp;• <a href="/wiki/Emerald_Egg" title="Emerald Egg">Emerald Egg</a>&nbsp;• <a href="/wiki/Emerald_Whelp" title="Emerald Whelp">Emerald Whelp</a>&nbsp;• <a href="/wiki/Goobert" title="Goobert">Goobert</a>&nbsp;• <a href="/wiki/Goobling" title="Goobling">Goobling</a>&nbsp;• <a href="/wiki/Hedgehog" title="Hedgehog">Hedgehog</a>&nbsp;• <a href="/wiki/Ice_Dragon" title="Ice Dragon">Ice Dragon</a>&nbsp;• <a href="/wiki/Jynx_torquilla" title="Jynx torquilla">Jynx torquilla</a>&nbsp;• <a href="/wiki/King_Goobert" title="King Goobert">King Goobert</a>&nbsp;• <a href="/wiki/Light_Goobert" title="Light Goobert">Light Goobert</a>&nbsp;• <a href="/wiki/Obsidian_Dragon" title="Obsidian Dragon">Obsidian Dragon</a>&nbsp;• <a href="/wiki/Phoenix" title="Phoenix">Phoenix</a>&nbsp;• <a href="/wiki/Poison_Goobert" title="Poison Goobert">Poison Goobert</a>&nbsp;• <a href="/wiki/Pop" title="Pop">Pop</a>&nbsp;• <a href="/wiki/Power_Puppy" title="Power Puppy">Power Puppy</a>&nbsp;• <a href="/wiki/Rainbow_Goobert_Deathslushy_Mansquisher" title="Rainbow Goobert Deathslushy Mansquisher">Rainbow Goobert Deathslushy Mansquisher</a>&nbsp;• <a href="/wiki/Rainbow_Goobert_Epicglob_Uberviscous" title="Rainbow Goobert Epicglob Uberviscous">Rainbow Goobert Epicglob Uberviscous</a>&nbsp;• <a href="/wiki/Rainbow_Goobert_Megasludge_Alphapuddle" title="Rainbow Goobert Megasludge Alphapuddle">Rainbow Goobert Megasludge Alphapuddle</a>&nbsp;• <a href="/wiki/Rainbow_Goobert_Omegaooze_Primeslime" title="Rainbow Goobert Omegaooze Primeslime">Rainbow Goobert Omegaooze Primeslime</a>&nbsp;• <a href="/wiki/Rat" title="Rat">Rat</a>&nbsp;• <a href="/wiki/Rat_Chef" title="Rat Chef">Rat Chef</a>&nbsp;• <a href="/wiki/Ruby_Chonk" title="Ruby Chonk">Ruby Chonk</a>&nbsp;• <a href="/wiki/Ruby_Egg" title="Ruby Egg">Ruby Egg</a>&nbsp;• <a href="/wiki/Ruby_Whelp" title="Ruby Whelp">Ruby Whelp</a>&nbsp;• <a href="/wiki/Sapphire_Egg" title="Sapphire Egg">Sapphire Egg</a>&nbsp;• <a href="/wiki/Sapphire_Whelp" title="Sapphire Whelp">Sapphire Whelp</a>&nbsp;• <a href="/wiki/Snake" title="Snake">Snake</a>&nbsp;• <a href="/wiki/Squirrel" title="Squirrel">Squirrel</a>&nbsp;• <a href="/wiki/Squirrel_Archer" title="Squirrel Archer">Squirrel Archer</a>&nbsp;• <a href="/wiki/Steel_Goobert" title="Steel Goobert">Steel Goobert</a>&nbsp;• <a href="/wiki/Stone_Golem" title="Stone Golem">Stone Golem</a>&nbsp;• <a href="/wiki/Toad" title="Toad">Toad</a>&nbsp;• <a href="/wiki/Unsettling_Presence" title="Unsettling Presence">Unsettling Presence</a>&nbsp;• <a href="/wiki/Wisdom_Puppy" title="Wisdom Puppy">Wisdom Puppy</a>&nbsp;• <a href="/wiki/Wolpertinger" title="Wolpertinger">Wolpertinger</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Playing_Card" title="Playing Card">Playing Card</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Ace_of_Spades" title="Ace of Spades">Ace of Spades</a>&nbsp;• <a href="/wiki/Darkest_Lotus" title="Darkest Lotus">Darkest Lotus</a>&nbsp;• <a href="/wiki/Holo_Fire_Lizard" title="Holo Fire Lizard">Holo Fire Lizard</a>&nbsp;• <a href="/wiki/Jimbo" title="Jimbo">Jimbo</a>&nbsp;• <a href="/wiki/Reverse!" title="Reverse!">Reverse!</a>&nbsp;• <a href="/wiki/The_Fool" title="The Fool">The Fool</a>&nbsp;• <a href="/wiki/The_Lovers" title="The Lovers">The Lovers</a>&nbsp;• <a href="/wiki/White-Eyes_Blue_Dragon" title="White-Eyes Blue Dragon">White-Eyes Blue Dragon</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Potion" title="Potion">Potion</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Demonic_Flask" title="Demonic Flask">Demonic Flask</a>&nbsp;• <a href="/wiki/Divine_Potion" title="Divine Potion">Divine Potion</a>&nbsp;• <a href="/wiki/Health_Potion" title="Health Potion">Health Potion</a>&nbsp;• <a href="/wiki/Heroic_Potion" title="Heroic Potion">Heroic Potion</a>&nbsp;• <a href="/wiki/Mana_Potion" title="Mana Potion">Mana Potion</a>&nbsp;• <a href="/wiki/Pestilence_Flask" title="Pestilence Flask">Pestilence Flask</a>&nbsp;• <a href="/wiki/Stone_Skin_Potion" title="Stone Skin Potion">Stone Skin Potion</a>&nbsp;• <a href="/wiki/Strong_Demonic_Flask" title="Strong Demonic Flask">Strong Demonic Flask</a>&nbsp;• <a href="/wiki/Strong_Divine_Potion" title="Strong Divine Potion">Strong Divine Potion</a>&nbsp;• <a href="/wiki/Strong_Health_Potion" title="Strong Health Potion">Strong Health Potion</a>&nbsp;• <a href="/wiki/Strong_Heroic_Potion" title="Strong Heroic Potion">Strong Heroic Potion</a>&nbsp;• <a href="/wiki/Strong_Mana_Potion" title="Strong Mana Potion">Strong Mana Potion</a>&nbsp;• <a href="/wiki/Strong_Pestilence_Flask" title="Strong Pestilence Flask">Strong Pestilence Flask</a>&nbsp;• <a href="/wiki/Strong_Stone_Skin_Potion" title="Strong Stone Skin Potion">Strong Stone Skin Potion</a>&nbsp;• <a href="/wiki/Strong_Vampiric_Potion" title="Strong Vampiric Potion">Strong Vampiric Potion</a>&nbsp;• <a href="/wiki/Vampiric_Potion" title="Vampiric Potion">Vampiric Potion</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Shield" title="Shield">Shield</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Frozen_Buckler" title="Frozen Buckler">Frozen Buckler</a>&nbsp;• <a href="/wiki/Moon_Shield" title="Moon Shield">Moon Shield</a>&nbsp;• <a href="/wiki/Shield_of_Valor" title="Shield of Valor">Shield of Valor</a>&nbsp;• <a href="/wiki/Spiked_Shield" title="Spiked Shield">Spiked Shield</a>&nbsp;• <a href="/wiki/Sun_Shield" title="Sun Shield">Sun Shield</a>&nbsp;• <a href="/wiki/Wooden_Buckler" title="Wooden Buckler">Wooden Buckler</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row alt"><th class="navbox-group" scope="row"><a href="/wiki/Shoes" title="Shoes">Shoes</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Dragonskin_Boots" title="Dragonskin Boots">Dragonskin Boots</a>&nbsp;• <a href="/wiki/Leather_Boots" title="Leather Boots">Leather Boots</a>&nbsp;• <a href="/wiki/Stone_Shoes" title="Stone Shoes">Stone Shoes</a>&nbsp;• <a href="/wiki/Winged_Boots" title="Winged Boots">Winged Boots</a></div></td></tr><tr class="navbox-gutter"><td colspan="2"></td></tr><tr class="navbox-row"><th class="navbox-group" scope="row"><a href="/wiki/Weapon" title="Weapon">Weapon</a></th><td class="navbox-list"><div class="hlist"><a href="/wiki/Amethyst_Whelp" title="Amethyst Whelp">Amethyst Whelp</a>&nbsp;• <a href="/wiki/Armored_Courage_Puppy" title="Armored Courage Puppy">Armored Courage Puppy</a>&nbsp;• <a href="/wiki/Artifact_Stone:_Cold" title="Artifact Stone: Cold">Artifact Stone: Cold</a>&nbsp;• <a href="/wiki/Artifact_Stone:_Death" title="Artifact Stone: Death">Artifact Stone: Death</a>&nbsp;• <a href="/wiki/Artifact_Stone:_Heat" title="Artifact Stone: Heat">Artifact Stone: Heat</a>&nbsp;• <a href="/wiki/Axe" title="Axe">Axe</a>&nbsp;• <a href="/wiki/Belladonna%27s_Shade" title="Belladonna's Shade">Belladonna's Shade</a>&nbsp;• <a href="/wiki/Belladonna%27s_Whisper" title="Belladonna's Whisper">Belladonna's Whisper</a>&nbsp;• <a href="/wiki/Blood_Harvester" title="Blood Harvester">Blood Harvester</a>&nbsp;• <a href="/wiki/Bloodthorne" title="Bloodthorne">Bloodthorne</a>&nbsp;• <a href="/wiki/Bloody_Dagger" title="Bloody Dagger">Bloody Dagger</a>&nbsp;• <a href="/wiki/Bow_and_Arrow" title="Bow and Arrow">Bow and Arrow</a>&nbsp;• <a href="/wiki/Brass_Knuckles" title="Brass Knuckles">Brass Knuckles</a>&nbsp;• <a href="/wiki/Broom" title="Broom">Broom</a>&nbsp;• <a href="/wiki/Burning_Blade" title="Burning Blade">Burning Blade</a>&nbsp;• <a href="/wiki/Burning_Sword" title="Burning Sword">Burning Sword</a>&nbsp;• <a href="/wiki/Burning_Torch" title="Burning Torch">Burning Torch</a>&nbsp;• <a href="/wiki/Busted_Blade" title="Busted Blade">Busted Blade</a>&nbsp;• <a href="/wiki/Chain_Whip" title="Chain Whip">Chain Whip</a>&nbsp;• <a href="/wiki/Claws_of_Attack" title="Claws of Attack">Claws of Attack</a>&nbsp;• <a href="/wiki/Courage_Puppy" title="Courage Puppy">Courage Puppy</a>&nbsp;• <a href="/wiki/Critwood_Staff" title="Critwood Staff">Critwood Staff</a>&nbsp;• <a href="/wiki/Crossblades" title="Crossblades">Crossblades</a>&nbsp;• <a href="/wiki/Cursed_Dagger" title="Cursed Dagger">Cursed Dagger</a>&nbsp;• <a href="/wiki/Dagger" title="Dagger">Dagger</a>&nbsp;• <a href="/wiki/Dancing_Dragon" title="Dancing Dragon">Dancing Dragon</a>&nbsp;• <a href="/wiki/Darksaber" title="Darksaber">Darksaber</a>&nbsp;• <a href="/wiki/Death_Scythe" title="Death Scythe">Death Scythe</a>&nbsp;• <a href="/wiki/Double_Axe" title="Double Axe">Double Axe</a>&nbsp;• <a href="/wiki/Eggscalibur" title="Eggscalibur">Eggscalibur</a>&nbsp;• <a href="/wiki/Emerald_Whelp" title="Emerald Whelp">Emerald Whelp</a>&nbsp;• <a href="/wiki/Falcon_Blade" title="Falcon Blade">Falcon Blade</a>&nbsp;• <a href="/wiki/Fancy_Fencing_Rapier" title="Fancy Fencing Rapier">Fancy Fencing Rapier</a>&nbsp;• <a href="/wiki/Flame_Whip" title="Flame Whip">Flame Whip</a>&nbsp;• <a href="/wiki/Forging_Hammer" title="Forging Hammer">Forging Hammer</a>&nbsp;• <a href="/wiki/Fortuna%27s_Grace" title="Fortuna's Grace">Fortuna's Grace</a>&nbsp;• <a href="/wiki/Fortuna%27s_Hope" title="Fortuna's Hope">Fortuna's Hope</a>&nbsp;• <a href="/wiki/Frostbite" title="Frostbite">Frostbite</a>&nbsp;• <a href="/wiki/Hammer" title="Hammer">Hammer</a>&nbsp;• <a href="/wiki/Hero_Longsword" title="Hero Longsword">Hero Longsword</a>&nbsp;• <a href="/wiki/Hero_Sword" title="Hero Sword">Hero Sword</a>&nbsp;• <a href="/wiki/Holy_Spear" title="Holy Spear">Holy Spear</a>&nbsp;• <a href="/wiki/Hungry_Blade" title="Hungry Blade">Hungry Blade</a>&nbsp;• <a href="/wiki/Ice_Dragon" title="Ice Dragon">Ice Dragon</a>&nbsp;• <a href="/wiki/Impractically_Large_Greatsword" title="Impractically Large Greatsword">Impractically Large Greatsword</a>&nbsp;• <a href="/wiki/Katana" title="Katana">Katana</a>&nbsp;• <a href="/wiki/Lightsaber" title="Lightsaber">Lightsaber</a>&nbsp;• <a href="/wiki/Magic_Staff" title="Magic Staff">Magic Staff</a>&nbsp;• <a href="/wiki/Magic_Torch" title="Magic Torch">Magic Torch</a>&nbsp;• <a href="/wiki/Manathirst" title="Manathirst">Manathirst</a>&nbsp;• <a href="/wiki/Molten_Dagger" title="Molten Dagger">Molten Dagger</a>&nbsp;• <a href="/wiki/Molten_Spear" title="Molten Spear">Molten Spear</a>&nbsp;• <a href="/wiki/Obsidian_Dragon" title="Obsidian Dragon">Obsidian Dragon</a>&nbsp;• <a href="/wiki/Pan" title="Pan">Pan</a>&nbsp;• <a href="/wiki/Pandamonium" title="Pandamonium">Pandamonium</a>&nbsp;• <a href="/wiki/Phoenix" title="Phoenix">Phoenix</a>&nbsp;• <a href="/wiki/Poison_Dagger" title="Poison Dagger">Poison Dagger</a>&nbsp;• <a href="/wiki/Pop" title="Pop">Pop</a>&nbsp;• <a href="/wiki/Prismatic_Sword" title="Prismatic Sword">Prismatic Sword</a>&nbsp;• <a href="/wiki/Pumpkin" title="Pumpkin">Pumpkin</a>&nbsp;• <a href="/wiki/Ripsaw_Blade" title="Ripsaw Blade">Ripsaw Blade</a>&nbsp;• <a href="/wiki/Ruby_Chonk" title="Ruby Chonk">Ruby Chonk</a>&nbsp;• <a href="/wiki/Ruby_Whelp" title="Ruby Whelp">Ruby Whelp</a>&nbsp;• <a href="/wiki/Sapphire_Whelp" title="Sapphire Whelp">Sapphire Whelp</a>&nbsp;• <a href="/wiki/Serpent_Staff" title="Serpent Staff">Serpent Staff</a>&nbsp;• <a href="/wiki/Shell_Totem" title="Shell Totem">Shell Totem</a>&nbsp;• <a href="/wiki/Shortbow" title="Shortbow">Shortbow</a>&nbsp;• <a href="/wiki/Shovel" title="Shovel">Shovel</a>&nbsp;• <a href="/wiki/Snow_Stick" title="Snow Stick">Snow Stick</a>&nbsp;• <a href="/wiki/Spear" title="Spear">Spear</a>&nbsp;• <a href="/wiki/Spectral_Dagger" title="Spectral Dagger">Spectral Dagger</a>&nbsp;• <a href="/wiki/Spiked_Staff" title="Spiked Staff">Spiked Staff</a>&nbsp;• <a href="/wiki/Squirrel_Archer" title="Squirrel Archer">Squirrel Archer</a>&nbsp;• <a href="/wiki/Staff_of_Fire" title="Staff of Fire">Staff of Fire</a>&nbsp;• <a href="/wiki/Staff_of_Unhealing" title="Staff of Unhealing">Staff of Unhealing</a>&nbsp;• <a href="/wiki/Stone" title="Stone">Stone</a>&nbsp;• <a href="/wiki/Stone_Golem" title="Stone Golem">Stone Golem</a>&nbsp;• <a href="/wiki/Thorn_Whip" title="Thorn Whip">Thorn Whip</a>&nbsp;• <a href="/wiki/Thornbloom" title="Thornbloom">Thornbloom</a>&nbsp;• <a href="/wiki/Torch" title="Torch">Torch</a>&nbsp;• <a href="/wiki/Tusk_Piercer" title="Tusk Piercer">Tusk Piercer</a>&nbsp;• <a href="/wiki/Tusk_Poker" title="Tusk Poker">Tusk Poker</a>&nbsp;• <a href="/wiki/Villain_Sword" title="Villain Sword">Villain Sword</a>&nbsp;• <a href="/wiki/Wooden_Sword" title="Wooden Sword">Wooden Sword</a></div></td></tr></tbody></table>

<!-- 
NewPP limit report
Cached time: 20250510233752
Cache expiry: 2592000
Reduced expiry: false
Complications: []
CPU time usage: 0.157 seconds
Real time usage: 0.337 seconds
Preprocessor visited node count: 1170/1000000
Post‐expand include size: 18110/4194304 bytes
Template argument size: 5240/4194304 bytes
Highest expansion depth: 11/100
Expensive parser function count: 0/500
Unstrip recursion depth: 0/20
Unstrip post‐expand size: 8241/10000000 bytes
Lua time usage: 0.007/15.000 seconds
Lua memory usage: 636935/52428800 bytes
Number of processed Cargo queries: 17
Time spent processing Cargo queries: 0.132 s (avg. 0.008 s)
Number of Cargo row insertion attempts: 1
-->
<!--
Transclusion expansion time report (%,ms,calls,template)
100.00%  205.031      1 -total
 57.07%  117.009      1 Template:Items
 55.68%  114.158      1 Template:Navbox
 42.87%   87.893      1 Template:Item_infobox
  2.62%    5.367      1 Template:Icon/nature
  1.96%    4.021      1 Template:Icon/Neutral
  1.45%    2.970      1 Template:Grid
  1.44%    2.959      1 Template:Icon/gold
  1.17%    2.402      1 Template:Icon/regeneration
  1.15%    2.363     22 Template:!((
-->

<!-- Saved in parser cache with key backpackbattles_en:pcache:idhash:175-0!canonical and timestamp 20250510233752 and revision id 10140. Rendering was triggered because: page-view
 -->
</div>
<div class="printfooter" data-nosnippet="">Retrieved from "<a dir="ltr" href="https://backpackbattles.wiki.gg/wiki/Healing_Herbs?oldid=10140">https://backpackbattles.wiki.gg/wiki/Healing_Herbs?oldid=10140</a>"</div></div>
					<div id="catlinks" class="catlinks" data-mw="interface"><div id="mw-normal-catlinks" class="mw-normal-catlinks"><a href="/wiki/Special:Categories" title="Special:Categories">Categories</a>: <ul><li><a href="/wiki/Category:Item" title="Category:Item">Item</a></li><li><a href="/wiki/Category:Common" title="Category:Common">Common</a></li><li><a href="/wiki/Category:Accessory" title="Category:Accessory">Accessory</a></li><li><a href="/wiki/Category:Nature" title="Category:Nature">Nature</a></li><li><a href="/wiki/Category:Regeneration" title="Category:Regeneration">Regeneration</a></li></ul></div></div>
				</div>
			</main>
			<aside id="wikigg-sl-rail" data-mw="WggShowcaseLayout">
        <div class="wikigg-showcase__unit wikigg-showcase__reserved--mrec" data-wgg-unit-type="pubco"><div id="nn_mpu1"></div></div>
        <div class="wikigg-showcase__unit wikigg-showcase__reserved--mrec" data-wgg-unit-type="internal"></div>
        <div class="wikigg-showcase__unit wikigg-showcase__reserved--mrec" data-wgg-unit-type="pubco"><div id="nn_mpu2"></div></div>
</aside>

		</div>
		<aside id="wikigg-sl-footer" data-mw="WggShowcaseLayout">
        <div class="wikigg-showcase__unit wikigg-showcase__reserved--lb" data-wgg-unit-type="internal"></div>
</aside>

	</div>
</div>
<div id="mw-data-after-content">
	<div class="mw-cookiewarning-container"><div class="mw-cookiewarning-text"><span>Cookies help us deliver our services. By using our services, you agree to our use of cookies.</span></div><form method="POST"><div class="oo-ui-layout oo-ui-horizontalLayout"><span class="oo-ui-widget oo-ui-widget-enabled oo-ui-buttonElement oo-ui-buttonElement-framed oo-ui-labelElement oo-ui-flaggedElement-progressive oo-ui-buttonWidget"><a role="button" tabindex="0" href="https://www.indie.io/privacy-policy" rel="nofollow" class="oo-ui-buttonElement-button"><span class="oo-ui-iconElement-icon oo-ui-iconElement-noIcon oo-ui-image-progressive"></span><span class="oo-ui-labelElement-label">More information</span><span class="oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator oo-ui-image-progressive"></span></a></span><span class="oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-buttonElement oo-ui-buttonElement-framed oo-ui-labelElement oo-ui-flaggedElement-primary oo-ui-flaggedElement-progressive oo-ui-buttonInputWidget"><button type="submit" tabindex="0" name="disablecookiewarning" value="OK" class="oo-ui-inputWidget-input oo-ui-buttonElement-button"><span class="oo-ui-iconElement-icon oo-ui-iconElement-noIcon oo-ui-image-invert"></span><span class="oo-ui-labelElement-label">OK</span><span class="oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator oo-ui-image-invert"></span></button></span></div></form></div>
</div>


<footer id="footer" class="mw-footer">
	<ul id="footer-info">
	<li id="footer-info-lastmod"> This page was last edited on 8 November 2024, at 07:07.</li>
	<li id="footer-info-copyright"><div class="wikigg-copyright-box">Pages that were created prior to November 2023 are adapted from the Backpack Battles Fandom wiki.<br>Page content is under <a class="external" rel="nofollow" href="https://creativecommons.org/licenses/by-sa/4.0">Creative Commons Attribution-ShareAlike 4.0 License</a> unless otherwise noted.</div></li>
</ul>

	<ul id="footer-places">
	<li id="footer-places-wikigg-tos"><a href="https://www.indie.io/terms-of-service">Terms of Service</a></li>
	<li id="footer-places-wikigg-privacy"><a href="https://www.indie.io/privacy-policy">Privacy policy</a></li>
	<li id="footer-places-wikigg-support"><a href="https://support.wiki.gg">Support Wiki</a></li>
	<li id="footer-places-wikigg-servicedesk"><a href="https://wiki.gg/go/servicedesk">Send a ticket to wiki.gg</a></li>
	<li id="footer-places-wikigg-statuspage"><a href="https://wikiggstatus.com">Status page</a></li>
	<li id="footer-places-wikigg-pcmp"><a href="#" class="nn-cmp-show">Manage cookie settings</a></li>
</ul>

	<ul id="footer-icons" class="noprint">
	<li id="footer-copyrightico"><a href="https://creativecommons.org/licenses/by-sa/4.0" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><img src="https://commons.wiki.gg/images/1/14/CC-BY-SA_footer_badge.svg?d931d3" alt="Creative Commons Attribution-ShareAlike 4.0 License" height="31" width="88" loading="lazy" data-theme="light"><img src="https://commons.wiki.gg/images/7/71/CC-BY-SA_footer_badge_dark.svg?55845c" alt="Creative Commons Attribution-ShareAlike 4.0 License" height="31" width="88" loading="lazy" data-theme="dark"></a></li>
	<li id="footer-poweredbyico"><a href="https://www.mediawiki.org/" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><img src="/mw-1.43/resources/assets/poweredby_mediawiki.svg" alt="Powered by MediaWiki" width="88" height="31" loading="lazy" data-theme="light"><img src="https://commons.wiki.gg/images/1/1c/MediaWiki_footer_badge_dark.svg?12ec0a" alt="Powered by MediaWiki" width="88" height="31" loading="lazy" data-theme="dark"></a></li>
	<li id="footer-partofico"><a href="https://wiki.gg" class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled"><img src="https://commons.wiki.gg/images/d/d1/Network_footer_badge.svg?9d5a96" alt="Part of wiki.gg" height="31" width="88" loading="lazy" data-theme="light"><img src="https://commons.wiki.gg/images/2/23/Network_footer_badge_dark.svg?9cf3e8" alt="Part of wiki.gg" height="31" width="88" loading="lazy" data-theme="dark"></a></li>
</ul>

</footer>


<script>(RLQ=window.RLQ||[]).push(function(){mw.config.set({"wgBackendResponseTime":199,"wgPageParseReport":{"limitreport":{"cputime":"0.157","walltime":"0.337","ppvisitednodes":{"value":1170,"limit":1000000},"postexpandincludesize":{"value":18110,"limit":4194304},"templateargumentsize":{"value":5240,"limit":4194304},"expansiondepth":{"value":11,"limit":100},"expensivefunctioncount":{"value":0,"limit":500},"unstrip-depth":{"value":0,"limit":20},"unstrip-size":{"value":8241,"limit":10000000},"timingprofile":["100.00%  205.031      1 -total"," 57.07%  117.009      1 Template:Items"," 55.68%  114.158      1 Template:Navbox"," 42.87%   87.893      1 Template:Item_infobox","  2.62%    5.367      1 Template:Icon/nature","  1.96%    4.021      1 Template:Icon/Neutral","  1.45%    2.970      1 Template:Grid","  1.44%    2.959      1 Template:Icon/gold","  1.17%    2.402      1 Template:Icon/regeneration","  1.15%    2.363     22 Template:!(("]},"scribunto":{"limitreport-timeusage":{"value":"0.007","limit":"15.000"},"limitreport-memusage":{"value":636935,"limit":52428800}},"librarian":{"limitreport-queries":17,"limitreport-querytime":["0.132 s (avg. 0.008 s)"],"limitreport-insertions":1},"cachereport":{"timestamp":"20250510233752","ttl":2592000,"transientcontent":false}}});});</script>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'9520b7f01bf7635d',t:'MTc1MDMxMTkwNy4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script><iframe height="1" width="1" style="position: absolute; top: 0px; left: 0px; border: none; visibility: hidden;"></iframe><script defer="" src="https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon="{&quot;rayId&quot;:&quot;9520b7f01bf7635d&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;26b2c6e7c852417d8f9d11b0c7f02309&quot;}" crossorigin="anonymous"></script>

<iframe name="__tcfapiLocator" style="display: none;"></iframe><div id="mw-teleport-target" class="vector-body"></div><iframe name="__uspapiLocator" style="display: none;"></iframe><iframe name="__gppLocator" style="display: none;"></iframe><iframe id="ura_f7f2" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 600px; min-height: 630px; padding: 10px 10px 0px; border: 2px solid rgba(0, 0, 0, 0.25); z-index: 999999; border-radius: 4px; background-color: rgb(255, 255, 255); box-shadow: rgba(0, 0, 0, 0.16) 0px 2px 5px 0px, rgba(0, 0, 0, 0.5) 0px 2px 10px 0px; display: none;"></iframe></body></html>