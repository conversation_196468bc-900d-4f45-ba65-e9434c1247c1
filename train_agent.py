import json
import os
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.neural_network import MLPClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score
import joblib

DATA_DIR = 'cloning_data'
MODEL_FILE = 'backpack_battles_agent.joblib'

def load_data():
    """Loads all labeled data from the cloning_data directory."""
    files = [os.path.join(DATA_DIR, f) for f in os.listdir(DATA_DIR) if f.endswith('.json')]
    
    X = []  # Features
    y = []  # Labels
    
    for file_path in files:
        with open(file_path, 'r') as f:
            try:
                data = json.load(f)
                if data.get('action'): # Only use labeled data
                    features = extract_features(data['game_state'])
                    X.append(features)
                    y.append(data['action'])
            except (json.JSONDecodeError, KeyError):
                print(f"Warning: Skipping corrupted or invalid file: {file_path}")
                continue
    return np.array(X), np.array(y)

def extract_features(game_state):
    """Converts a game_state dictionary into a flat feature vector."""
    features = []
    
    # Simple stats features
    stats = game_state.get('stats', {})
    features.extend([
        stats.get('Gold', 0),
        stats.get('Health', 0),
        stats.get('Round', 0),
        stats.get('Wins', 0)
    ])
    
    # Item features (simple count for now)
    features.append(len(game_state.get('backpack', [])))
    features.append(len(game_state.get('shop', [])))
    
    # Pad to a fixed length (e.g., 100)
    while len(features) < 100:
        features.append(0)
    
    return features[:100]

def main():
    """
    Main function to load data, train the model, and save it.
    """
    print("Loading labeled data...")
    X, y = load_data()
    
    if len(X) == 0:
        print("No labeled data found. Please run the labeling script first.")
        return

    print(f"Loaded {len(X)} data points.")
    
    # Split data into training and testing sets
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Scale features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    print("Training the agent...")
    # Using a Multi-layer Perceptron (a type of neural network)
    agent = MLPClassifier(hidden_layer_sizes=(128, 64), max_iter=500, random_state=42,
                          activation='relu', solver='adam', alpha=0.0001)
    
    agent.fit(X_train_scaled, y_train)
    
    print("Training complete.")
    
    # Evaluate the agent
    y_pred = agent.predict(X_test_scaled)
    accuracy = accuracy_score(y_test, y_pred)
    print(f"Agent accuracy on test data: {accuracy:.2f}")
    
    # Save the trained agent and the scaler
    print(f"Saving trained agent to {MODEL_FILE}...")
    joblib.dump({'agent': agent, 'scaler': scaler}, MODEL_FILE)
    print("Agent saved successfully.")

if __name__ == "__main__":
    main()