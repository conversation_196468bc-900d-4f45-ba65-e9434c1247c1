from typing import Optional, List, Tuple

class Item:
    """
    Represents an item in the game, holding all relevant properties including
    its shape, position, and other defining attributes.
    """
    def __init__(
        self,
        name: str,
        grid_layout: List[List[int]],
        grid_size: Tuple[int, int],
        item_type: str,
        position: Optional[Tuple[int, int]] = None,
        current_rotation: int = 0
    ):
        self.name = name
        self.grid_layout = grid_layout
        self.grid_size = grid_size
        self.item_type = item_type
        self.position = position
        self.current_rotation = current_rotation
    
    def __repr__(self):
        return f"Item(name='{self.name}', position={self.position})"