import cv2
import numpy as np
import os
import json

def find_grid_squares(image_path, output_path):
    """
    Analyzes an inventory screenshot to find the precise coordinates of each 
    light square, and generates a new calibrated grid file.

    Args:
        image_path (str): Path to the screenshot of the inventory.
        output_path (str): Path to save the new calibration file.
    """
    if not os.path.exists(image_path):
        print(f"Error: Image not found at '{image_path}'")
        return

    image = cv2.imread(image_path)
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Use template matching to find the grid squares
    # First, create a template from a known grid square
    # These coordinates are hand-picked from an empty inventory screenshot
    template_x, template_y, template_w, template_h = 114, 80, 102, 101
    template = gray[template_y:template_y + template_h, template_x:template_x + template_w]

    # Perform template matching
    res = cv2.matchTemplate(gray, template, cv2.TM_CCOEFF_NORMED)
    
    # Define a threshold for matching
    threshold = 0.85 # A slightly stricter threshold to filter out the false positive
    loc = np.where(res >= threshold)

    # Group the detected points to avoid overlapping boxes
    grid_squares = []
    for pt in zip(*loc[::-1]):
        # Check if this point is too close to an already added square
        is_new_square = True
        for square in grid_squares:
            dist_x = abs(pt[0] - square['x1'])
            dist_y = abs(pt[1] - square['y1'])
            if dist_x < template_w / 2 and dist_y < template_h / 2:
                is_new_square = False
                break
        
        if is_new_square:
            # Convert numpy types to standard Python integers for JSON serialization
            grid_squares.append({
                "x1": int(pt[0]), "y1": int(pt[1]),
                "x2": int(pt[0] + template_w), "y2": int(pt[1] + template_h),
                "center_x": int(pt[0] + template_w / 2),
                "center_y": int(pt[1] + template_h / 2),
                "width": int(template_w),
                "height": int(template_h)
            })

    # The number of squares should be 63 (9x7 grid)
    if len(grid_squares) != 63:
        print(f"Warning: Detected {len(grid_squares)} squares, but expected 63.")
        # You might need to adjust the threshold or contour area
        
    # Sort the squares by their y and then x coordinates to order them correctly
    grid_squares.sort(key=lambda s: (s['center_y'], s['center_x']))

    # Re-assign row and column numbers
    calibrated_grid = []
    for i, square in enumerate(grid_squares):
        row = i // 9
        col = i % 9
        calibrated_grid.append({
            "row": row,
            "col": col,
            "abs_x1": square['x1'], "abs_y1": square['y1'],
            "abs_x2": square['x2'], "abs_y2": square['y2'],
            "center_x": square['center_x'], "center_y": square['center_y'],
            "width": square['width'], "height": square['height']
        })

    # Write the new calibration data to a file
    with open(output_path, 'w') as f:
        f.write("CALIBRATED_GRID_COORDS = ")
        json.dump(calibrated_grid, f, indent=4)

    print(f"Successfully generated new grid calibration file at '{output_path}'")
    
    # Draw the detected grid on the image for verification
    debug_image = image.copy()
    for cell in calibrated_grid:
        cv2.rectangle(debug_image, (cell['abs_x1'], cell['abs_y1']), (cell['abs_x2'], cell['abs_y2']), (0, 255, 0), 2)
        cv2.circle(debug_image, (cell['center_x'], cell['center_y']), 5, (0, 0, 255), -1)
        label = f"{cell['row']},{cell['col']}"
        cv2.putText(debug_image, label, (cell['abs_x1'] + 5, cell['abs_y1'] + 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    cv2.imwrite("recalibrated_grid_overlay.png", debug_image)
    print("Saved visual verification to 'recalibrated_grid_overlay.png'")


if __name__ == "__main__":
    # Use a clean screenshot of the inventory for the best results
    INVENTORY_SCREENSHOT = "data/screenshots/Empty inventory storage and shop.png"
    NEW_CALIBRATION_FILE = "new_calibrated_grid.py"
    find_grid_squares(INVENTORY_SCREENSHOT, NEW_CALIBRATION_FILE)
