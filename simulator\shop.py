import sqlite3
import random
import math
from typing import List, Dict, Optional, Tuple, Any

from simulator.core import Item, ItemInstance, Player

class Shop:
    """Manages the player's shop, including item offerings and player actions."""

    def __init__(self, all_items: Dict[str, Item]):
        """
        Initializes the Shop.
        Args:
            all_items: A dictionary of all items in the game, keyed by item name.
        """
        self.all_items: Dict[str, List[Item]] = self._categorize_items_by_rarity(all_items)
        self.current_offerings: List[Optional[Item]] = [None] * 5
        self.reroll_cost: int = 1
        self.locks: List[bool] = [False] * 5
        self.db_path = "GameData.db" # Required for check_for_combinations

    def _categorize_items_by_rarity(self, all_items: Dict[str, Item]) -> Dict[str, List[Item]]:
        """
        Categorizes a dictionary of items by their rarity.
        """
        items_by_rarity: Dict[str, List[Item]] = {}
        for item in all_items.values():
            if item.rarity not in items_by_rarity:
                items_by_rarity[item.rarity] = []
            items_by_rarity[item.rarity].append(item)
        return items_by_rarity

    def _get_rarity_probabilities(self, round_number: int) -> Dict[str, float]:
        """
        Gets the item rarity probabilities for the current round.
        Based on https://backpack-battles.fandom.com/wiki/Shop
        """
        if round_number in [1, 2]:
            return {"Common": 1.0}
        if round_number in [3, 4]:
            return {"Common": 0.80, "Uncommon": 0.20}
        if round_number in [5, 6]:
            return {"Common": 0.60, "Uncommon": 0.35, "Rare": 0.05}
        if round_number in [7, 8]:
            return {"Common": 0.40, "Uncommon": 0.45, "Rare": 0.15}
        if round_number == 9:
            return {"Common": 0.20, "Uncommon": 0.50, "Rare": 0.25, "Epic": 0.05}
        if round_number >= 10:
            return {"Common": 0.10, "Uncommon": 0.40, "Rare": 0.40, "Epic": 0.10}
        return {"Common": 1.0} # Default/fallback

    def generate_new_offerings(self, round_number: int):
        """
        Generates a new set of items to be offered in the shop.
        Args:
            round_number: The current game round.
        """
        probabilities = self._get_rarity_probabilities(round_number)
        rarities = list(probabilities.keys())
        weights = list(probabilities.values())

        for i in range(len(self.current_offerings)):
            if not self.locks[i]:
                # Select a rarity based on the probabilities
                chosen_rarity = random.choices(rarities, weights, k=1)[0]

                # Pick a random item of that rarity
                if self.all_items.get(chosen_rarity):
                    chosen_item = random.choice(self.all_items[chosen_rarity])
                    
                    # 10% chance for an item to be on sale
                    if random.random() < 0.1:
                        # Create a temporary copy to modify cost for the sale
                        sale_item_data = chosen_item.to_dict()
                        sale_item_data['cost'] = max(1, math.ceil(chosen_item.cost / 2))
                        sale_item = Item(sale_item_data)
                        sale_item.cost = max(1, math.ceil(chosen_item.cost / 2))
                        self.current_offerings[i] = sale_item
                    else:
                        self.current_offerings[i] = chosen_item
                else:
                    self.current_offerings[i] = None # No items of this rarity
        
        # Reset reroll cost
        self.reroll_cost = 1

    def buy_item(self, player: Player, shop_slot_index: int, position: tuple[int, int], rotation: int) -> bool:
        """
        Allows a player to buy an item from the shop.
        Args:
            player: The Player making the purchase.
            shop_slot_index: The index of the item in the shop offerings.
            position: The (x, y) coordinates to place the item in the backpack.
            rotation: The rotation of the item.
        Returns:
            True if the purchase was successful, False otherwise.
        """
        if not (0 <= shop_slot_index < len(self.current_offerings)):
            return False

        item_to_buy = self.current_offerings[shop_slot_index]
        if item_to_buy is None or player.gold < item_to_buy.cost:
            return False

        item_instance = ItemInstance(item_to_buy, position[0], position[1], rotation)
        
        if player.backpack.can_place_item(item_instance):
            player.gold -= item_to_buy.cost
            player.backpack.place_item(item_instance)
            self.current_offerings[shop_slot_index] = None
            return True
        return False

    def sell_item(self, player: Player, item_instance_id: int):
        """
        Allows a player to sell an item from their backpack.
        Args:
            player: The Player selling the item.
            item_instance_id: The instance ID of the item to sell.
        """
        if item_instance_id in player.backpack.items:
            item_to_sell = player.backpack.items[item_instance_id]
            player.gold += math.ceil(item_to_sell.item.cost / 2)
            player.backpack.remove_item(item_instance_id)

    def reroll_shop(self, player: Player, round_number: int):
        """
        Allows a player to reroll the shop offerings for a cost.
        Args:
            player: The Player rerolling the shop.
            round_number: The current game round.
        """
        if player.gold >= self.reroll_cost:
            player.gold -= self.reroll_cost
            self.generate_new_offerings(round_number)
            self.reroll_cost += 1 # Reroll cost increases by 1 each time within a round

    def check_for_combinations(self, player: Player):
        """
        Checks for and executes item combinations in the player's backpack.
        Args:
            player: The Player whose backpack to check.
        """
        con = sqlite3.connect(self.db_path)
        con.row_factory = sqlite3.Row
        cur = con.cursor()
        cur.execute("SELECT * FROM Recipes")
        recipes = cur.fetchall()
        con.close()

        backpack_item_names = {inst.item.name for inst in player.backpack.items.values()}

        for recipe in recipes:
            ingredients = json.loads(recipe['ingredients'])
            
            # Check if all ingredient names are in the backpack
            if all(name in backpack_item_names for name in ingredients):
                
                # For this simple version, we assume 1 of each is enough.
                # A more complex version would need to handle quantities.
                ingredient_instances_to_remove: List[ItemInstance] = []
                
                temp_backpack_items = list(player.backpack.items.values())
                
                for ing_name in ingredients:
                    for inst in temp_backpack_items:
                        if inst.item.name == ing_name:
                            ingredient_instances_to_remove.append(inst)
                            temp_backpack_items.remove(inst) # Avoid reusing the same instance
                            break
                
                # If we found instances for all ingredients
                if len(ingredient_instances_to_remove) == len(ingredients):
                    print(f"Combining recipe for: {recipe['result_item_name']}")
                    
                    # Remove ingredients
                    for inst in ingredient_instances_to_remove:
                        player.backpack.remove_item(inst.instance_id)
                    
                    # Add resulting item
                    # We need to load the full item data for the result
                    new_item_data = self._load_item_by_name(recipe['result_item_name'])
                    if new_item_data:
                        # Simple placement: find first available top-left slot
                        self._place_item_in_first_available_slot(player, new_item_data)
                    
                    # Restart check in case new item completes another recipe
                    self.check_for_combinations(player)
                    return # Exit after one combination to restart the scan

    def _load_item_by_name(self, name: str) -> Optional[Item]:
        """Helper to load a single item's data by its name."""
        for rarity_list in self.all_items.values():
            for item in rarity_list:
                if item.name == name:
                    return item
        return None

    def _place_item_in_first_available_slot(self, player: Player, item: Item):
        """A simple placement strategy for combined items."""
        for y in range(player.backpack.height):
            for x in range(player.backpack.width):
                # Try all 4 rotations
                for rot in range(4):
                    instance = ItemInstance(item, x, y, rot)
                    if player.backpack.can_place_item(instance):
                        player.backpack.place_item(instance)
                        return