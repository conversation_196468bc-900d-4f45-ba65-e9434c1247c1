import json
import os
from pprint import pprint

DATA_DIR = 'cloning_data'

def get_sorted_data_files():
    """Gets all JSON data files, sorted by timestamp."""
    files = [os.path.join(DATA_DIR, f) for f in os.listdir(DATA_DIR) if f.endswith('.json')]
    
    records = []
    for file_path in files:
        with open(file_path, 'r') as f:
            # Handle empty or corrupted files
            try:
                records.append(json.load(f))
            except json.JSONDecodeError:
                print(f"Warning: Skipping corrupted or empty file: {file_path}")
                continue
            
    # Sort records based on the timestamp
    records.sort(key=lambda x: x['timestamp'])
    return records

def display_state_diff(state1, state2):
    """Displays a simplified difference between two game states."""
    print("\n--- STATE 1 (Before Action) ---")
    pprint(state1['game_state']['stats'])
    print("Backpack:", [item['item_name'] for item in state1['game_state']['backpack']])
    print("Shop:", [item['item_name'] for item in state1['game_state']['shop']])
    
    print("\n--- STATE 2 (After Action) ---")
    pprint(state2['game_state']['stats'])
    print("Backpack:", [item['item_name'] for item in state2['game_state']['backpack']])
    print("Shop:", [item['item_name'] for item in state2['game_state']['shop']])
    print("---------------------------------")

def main():
    """
    Main loop for labeling actions between captured states.
    """
    print("Starting action labeling process.")
    records = get_sorted_data_files()
    
    for i in range(len(records) - 1):
        record1 = records[i]
        record2 = records[i+1]

        # Skip if action is already labeled
        if record1.get('action') is not None:
            print(f"Skipping already labeled record {record1['id']}")
            continue

        display_state_diff(record1, record2)
        
        while True:
            action = input("Describe the action taken between State 1 and State 2 (or 'skip', 'exit'): ")
            
            if action.lower() == 'exit':
                return
            if action.lower() == 'skip':
                break
                
            record1['action'] = action
            
            # Save the updated record back to its file
            file_path = os.path.join(DATA_DIR, f"{record1['id']}.json")
            with open(file_path, 'w') as f:
                json.dump(record1, f, indent=4)
            
            print(f"Action '{action}' saved for {record1['id']}.")
            break

    print("\nAction labeling complete.")

if __name__ == "__main__":
    main()