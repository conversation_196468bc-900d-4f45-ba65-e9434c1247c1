from typing import List, Dict, <PERSON><PERSON>
from bbagent.game_manager import GameManager
from simulator.core import ItemInstance
import json

class ActionExecutor:
    """
    Analyzes inventory layouts and generates a sequence of actions 
    to transform the current layout into the optimal one.
    """

    def __init__(self, game_manager: GameManager):
        """
        Initializes the ActionExecutor.

        Args:
            game_manager: The instance of the GameManager.
        """
        self.game_manager = game_manager

    def generate_action_plan(self, optimal_layout: List[ItemInstance]) -> List[str]:
        """
        Compares the current layout to the optimal layout and generates a JSON string
        of actions required to transform it.

        Args:
            optimal_layout: The target layout to achieve.

        Returns:
            A list of action strings.
        """
        current_layout = self.game_manager.get_current_inventory()
        
        # Match items between the two layouts to determine what needs to change
        to_sell, to_move, to_buy = self._analyze_layout_delta(current_layout, optimal_layout)

        # Generate the sequence of actions
        actions = self._create_action_sequence(to_sell, to_move, to_buy, current_layout)
        
        return actions

    def _analyze_layout_delta(self, current_layout: List[ItemInstance], optimal_layout: List[ItemInstance]) -> <PERSON><PERSON>[List[ItemInstance], List[Tuple[ItemInstance, ItemInstance]], List[ItemInstance]]:
        """
        Compares layouts to identify items to sell, move, or buy.

        Returns:
            A tuple containing lists of items to sell, pairs of items to move 
            (source, destination), and items to buy.
        """
        current_items_map = {item.item.name: [] for item in current_layout}
        for item in current_layout:
            current_items_map[item.item.name].append(item)

        optimal_items_map = {item.item.name: [] for item in optimal_layout}
        for item in optimal_layout:
            optimal_items_map[item.item.name].append(item)
            
        unmatched_optimal = [item for item in optimal_layout]
        unmatched_current = [item for item in current_layout]
        to_move = []

        # Find direct matches (same item, same position, same rotation)
        # and potential moves (same item, different position/rotation)
        for i in range(len(unmatched_current) - 1, -1, -1):
            current_item = unmatched_current[i]
            for j in range(len(unmatched_optimal) - 1, -1, -1):
                optimal_item = unmatched_optimal[j]
                if current_item.item.name == optimal_item.item.name:
                    # Found a potential match, mark it as a move
                    to_move.append((current_item, optimal_item))
                    unmatched_current.pop(i)
                    unmatched_optimal.pop(j)
                    break
        
        to_sell = unmatched_current
        to_buy = unmatched_optimal

        return to_sell, to_move, to_buy

    def _create_action_sequence(self, to_sell: List[ItemInstance], to_move: List[Tuple[ItemInstance, ItemInstance]], to_buy: List[ItemInstance], current_layout: List[ItemInstance]) -> List[str]:
        """
        Generates an ordered list of actions (SELL, MOVE, ROTATE, BUY).
        """
        actions = []
        
        # 1. Generate SELL actions first to free up space
        for item in to_sell:
            actions.append(f"SELL({item.x}, {item.y})")

        # 2. Generate MOVE and ROTATE actions
        # A more complex implementation would handle move conflicts (e.g., swapping two items)
        # For now, we assume simple moves.
        for current_item, optimal_item in to_move:
            if current_item.x != optimal_item.x or current_item.y != optimal_item.y:
                actions.append(f"MOVE({current_item.x}, {current_item.y}, {optimal_item.x}, {optimal_item.y})")
            if current_item.rotation != optimal_item.rotation:
                # Assuming the move happens first, so rotate is at the new position
                actions.append(f"ROTATE({optimal_item.x}, {optimal_item.y})")

        # 3. Generate BUY actions last
        for item in to_buy:
            actions.append(f"BUY({item.item.name}, {item.x}, {item.y})")
            # If the item needs to be bought and rotated
            if item.rotation != 0:
                actions.append(f"ROTATE({item.x}, {item.y})")

        return actions