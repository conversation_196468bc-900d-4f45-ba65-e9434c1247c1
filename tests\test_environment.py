import sys
import os
import pytest
import numpy as np

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from rl.environment import BackpackBattlesEnv

@pytest.fixture
def mock_opponent_builds():
    """Provides a mock list of opponent builds."""
    return [
        {"name": "Opponent1", "class": "Fighter", "backpack": []},
        {"name": "Opponent2", "class": "Ranger", "backpack": []}
    ]

@pytest.fixture
def env(mock_opponent_builds):
    """Initializes the BackpackBattlesEnv for testing."""
    return BackpackBattlesEnv(opponent_builds=mock_opponent_builds)

def test_environment_initialization(env):
    """
    Tests that the environment is initialized correctly.
    """
    assert env.observation_space is not None, "Observation space should not be None"
    assert env.action_space is not None, "Action space should not be None"

def test_reset_method(env):
    """
    Tests the reset method of the environment.
    """
    obs, info = env.reset()

    assert isinstance(obs, np.ndarray), "Observation should be a NumPy array"
    assert env.observation_space.contains(obs), "Observation should be within the observation space"
    assert "action_mask" in info, "Info dictionary should contain 'action_mask'"

def test_step_method(env):
    """
    Tests the step method of the environment.
    """
    env.reset()
    
    # Create a valid sample action (END_TURN)
    action = {
        "action_type": 3,
        "item_slot": 0,
        "target_slot": 0,
        "rotation": 0
    }

    obs, reward, terminated, truncated, info = env.step(action)

    assert isinstance(obs, np.ndarray), "Observation should be a NumPy array"
    assert isinstance(reward, (int, float)), "Reward should be a number"
    assert isinstance(terminated, bool), "Terminated flag should be a boolean"
    assert isinstance(truncated, bool), "Truncated flag should be a boolean"
    assert isinstance(info, dict), "Info should be a dictionary"
    assert env.observation_space.contains(obs), "Observation should be within the observation space"