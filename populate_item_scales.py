import os
import json
from PIL import Image

def analyze_item_images(item_dir, scaling_config_path):
    """
    Analyzes all item images in a directory to determine their optimal scaling
    based on their transparent bounding box. Updates a JSON configuration file
    with these scales.

    Args:
        item_dir (str): The directory containing item images.
        scaling_config_path (str): The path to the item scaling JSON file.
    """
    if not os.path.exists(item_dir):
        print(f"Error: Item directory not found at '{item_dir}'")
        return

    # Load the existing scaling configuration
    if os.path.exists(scaling_config_path):
        with open(scaling_config_path, 'r') as f:
            scaling_data = json.load(f)
    else:
        scaling_data = {
            "default": {
                "scale": 0.95,
                "offset_x": 0,
                "offset_y": 0
            }
        }

    print(f"Analyzing images in '{item_dir}'...")
    
    # Iterate through all PNG files in the directory
    for filename in os.listdir(item_dir):
        if filename.endswith(".png"):
            item_name = os.path.splitext(filename)[0]
            
            # Skip if this item already has a specific entry (to preserve manual tweaks)
            if item_name in scaling_data and item_name != "default":
                continue

            image_path = os.path.join(item_dir, filename)
            
            try:
                with Image.open(image_path) as img:
                    # Ensure image has an alpha channel
                    if img.mode != 'RGBA':
                        img = img.convert('RGBA')

                    # Get the bounding box of the non-transparent parts
                    bbox = img.getbbox()

                    if bbox:
                        # The actual content dimensions
                        content_width = bbox[2] - bbox[0]
                        content_height = bbox[3] - bbox[1]
                        
                        # The total image dimensions
                        total_width = img.width
                        total_height = img.height

                        # Calculate how much of the image is taken up by the actual content
                        # We take the maximum of the width/height ratios to determine the scale
                        scale_x = content_width / total_width
                        scale_y = content_height / total_height
                        
                        # The scale should be the larger of the two ratios
                        scale = max(scale_x, scale_y)

                        # Add the new item's scaling info
                        scaling_data[item_name] = {
                            "scale": round(scale, 3)
                        }
                    else:
                        # Handle fully transparent images
                        scaling_data[item_name] = {"scale": 1.0}

            except Exception as e:
                print(f"Could not process {filename}: {e}")

    # Write the updated configuration back to the file
    with open(scaling_config_path, 'w') as f:
        json.dump(scaling_data, f, indent=2, sort_keys=True)

    print(f"Successfully updated '{scaling_config_path}' with scales for {len(scaling_data) - 1} items.")

if __name__ == "__main__":
    ITEM_IMAGE_DIRECTORY = "data/item_images"
    SCALING_CONFIG_FILE = "item_scaling.json"
    analyze_item_images(ITEM_IMAGE_DIRECTORY, SCALING_CONFIG_FILE)
