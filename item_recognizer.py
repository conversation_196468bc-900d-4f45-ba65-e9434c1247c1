import os
import logging
import cv2
import numpy as np

def load_item_templates(template_folder):
    """Loads all item templates from the given folder."""
    templates = {}
    for filename in os.listdir(template_folder):
        if filename.endswith('.png'):
            item_name = os.path.splitext(filename)[0]
            path = os.path.join(template_folder, filename)
            template_img = cv2.imread(path)
            if template_img is not None:
                templates[item_name] = template_img
            else:
                print(f"Warning: Could not load template image '{filename}'")
    return templates

def find_all_items(screenshot_image, templates, threshold=0.8):
    """Finds all items from the templates in the screenshot using a more efficient multi-scale matching."""
    if screenshot_image is None:
        print("Error: Invalid image provided to find_all_items.")
        return []

    all_detections = []
    
    # Iterate over a few scales for the screenshot
    for scale in [1.0, 0.75, 0.5]: # Example scales, can be adjusted
        resized_screenshot = cv2.resize(screenshot_image, (0, 0), fx=scale, fy=scale)
        screenshot_gray = cv2.cvtColor(resized_screenshot, cv2.COLOR_BGR2GRAY)

        for item_name, template_image in templates.items():
            template_gray = cv2.cvtColor(template_image, cv2.COLOR_BGR2GRAY)
            t_w, t_h = template_gray.shape[::-1]

            # If template is larger than the scaled screenshot, skip
            if t_h > screenshot_gray.shape[0] or t_w > screenshot_gray.shape[1]:
                continue
                
            result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
            loc = np.where(result >= threshold)
            
            for pt in zip(*loc[::-1]):
                # Convert coordinates back to original screenshot size
                original_x = int(pt[0] / scale)
                original_y = int(pt[1] / scale)
                original_w = int(t_w / scale)
                original_h = int(t_h / scale)

                all_detections.append({
                    'rect': (original_x, original_y, original_w, original_h),
                    'confidence': result[pt[1], pt[0]],
                    'item_name': item_name
                })
            
    if not all_detections:
        return []

    rects = [d['rect'] for d in all_detections]
    confidences = [d['confidence'] for d in all_detections]

    # Use a placeholder for groupRectangles since it's complex to map grouped rects back to names
    # A more advanced implementation would handle this better
    grouped_rects, _ = cv2.groupRectangles(rects, 1, 0.5)

    final_results = []
    for (x, y, w, h) in grouped_rects:
        # Find which original detection is inside this grouped rectangle
        # This is a simplification; a better method might average confidence or choose the best match
        best_match = None
        highest_confidence = 0
        for det in all_detections:
            r_x, r_y, r_w, r_h = det['rect']
            if x <= r_x < x + w and y <= r_y < y + h:
                if det['confidence'] > highest_confidence:
                    highest_confidence = det['confidence']
                    best_match = det
        
        if best_match:
            final_results.append({
                'item_name': best_match['item_name'],
                'position': (x, y, w, h),
                'confidence': highest_confidence
            })

    return final_results

if __name__ == "__main__":
    TEMPLATE_FOLDER = 'data/item_images/'
    SCREENSHOT_PATH = 'SampleScreenshot-Shop.jpg'

    item_templates = load_item_templates(TEMPLATE_FOLDER)

    if os.path.exists(SCREENSHOT_PATH):
        test_image = cv2.imread(SCREENSHOT_PATH)
        found_items = find_all_items(test_image, item_templates)
        if found_items:
            print("Found Items:")
            for item in found_items:
                print(f"  - Item: {item['item_name']}, Position: {item['position']}, Confidence: {item['confidence']:.2f}")
        else:
            print("No items found in the screenshot.")
    else:
        print(f"Screenshot not found at '{SCREENSHOT_PATH}'. Skipping item search.")
        if item_templates:
            print(f"Successfully loaded {len(item_templates)} item templates.")