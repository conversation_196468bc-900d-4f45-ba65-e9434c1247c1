from PIL import Image
import os
import numpy as np

def analyze_grid_from_screenshots():
    """
    Analyze actual game screenshots to determine precise grid coordinates.
    """
    print("Analyzing game screenshots to determine grid coordinates...")
    
    screenshot_dir = "data/screenshots/bbss"
    screenshots = [f for f in os.listdir(screenshot_dir) if f.endswith('.png')]
    
    # Current inventory ROI (approximate)
    inventory_roi = (114, 80, 1129, 833)
    roi_x, roi_y, roi_x2, roi_y2 = inventory_roi
    
    print(f"Current ROI: {inventory_roi}")
    print(f"Found {len(screenshots)} screenshots to analyze")
    
    # Analyze a few key screenshots
    sample_screenshots = screenshots[:5]  # First 5 for analysis
    
    for i, screenshot_file in enumerate(sample_screenshots):
        print(f"\nAnalyzing {screenshot_file}...")
        
        screenshot_path = os.path.join(screenshot_dir, screenshot_file)
        img = Image.open(screenshot_path)
        
        print(f"Screenshot size: {img.size}")
        
        # Extract inventory area
        inventory_area = img.crop(inventory_roi)
        inventory_area.save(f"debug_screenshot_{i+1}_inventory.png")
        
        # Convert to numpy for analysis
        inv_array = np.array(inventory_area)
        
        # Analyze the inventory area to find grid patterns
        # Look for consistent patterns in the image that indicate grid lines
        
        # Save a few sample grid cells for manual inspection
        # Assuming 9x7 grid, extract some cells
        inv_w, inv_h = inventory_area.size
        cell_w = inv_w / 9
        cell_h = inv_h / 7
        
        print(f"Calculated cell size: {cell_w:.2f} x {cell_h:.2f}")
        
        # Extract some sample cells
        for row in range(min(3, 7)):
            for col in range(min(3, 9)):
                cell_x = int(col * cell_w)
                cell_y = int(row * cell_h)
                cell_x2 = int((col + 1) * cell_w)
                cell_y2 = int((row + 1) * cell_h)
                
                cell_img = inventory_area.crop((cell_x, cell_y, cell_x2, cell_y2))
                cell_img.save(f"debug_screenshot_{i+1}_cell_{row}_{col}.png")
        
        print(f"Extracted sample cells from {screenshot_file}")
    
    # Now let's try to detect the actual grid by looking for patterns
    print(f"\nAnalyzing grid patterns...")
    
    # Load the first screenshot for detailed analysis
    first_screenshot = Image.open(os.path.join(screenshot_dir, sample_screenshots[0]))
    inventory_area = first_screenshot.crop(inventory_roi)
    
    # Convert to grayscale for easier analysis
    gray_inventory = inventory_area.convert('L')
    gray_array = np.array(gray_inventory)
    
    # Look for horizontal and vertical lines that might indicate grid boundaries
    # This is a simplified approach - in practice, we might need more sophisticated detection
    
    print("Saved debug images for manual inspection:")
    print("- debug_screenshot_*_inventory.png: Full inventory areas")
    print("- debug_screenshot_*_cell_*.png: Individual cell samples")
    
    # Manual grid coordinate detection based on visual inspection
    # These would need to be determined by examining the debug images
    print(f"\nRecommendation: Examine the debug images to determine:")
    print("1. Exact pixel coordinates of each grid cell")
    print("2. The lightened area boundaries")
    print("3. How items are positioned within cells")

if __name__ == "__main__":
    analyze_grid_from_screenshots()
