# Technical Design: Auto-Battler Assistant v2

## 1. Introduction

This document outlines the technical architecture for a new, robust auto-battler game assistant. It replaces a prior, unsuccessful implementation by introducing a deterministic and intelligent system for game state parsing, inventory management, and strategic decision-making. The design is modular and leverages existing data sources, including `vision_system.py`, `simulator/battle.py`, and the game data within `Backpack Battle Items.json`.

## 2. High-Level Architecture

The system is designed around a central `GameManager` that operates as a state machine, following the game's natural cycle: **Shop Phase -> Battle Phase -> Post-Battle Phase**. This manager coordinates a set of specialized services responsible for perception, planning, and action.

```mermaid
graph TD
    subgraph "Game Loop"
        A[Shop Phase] --> B[Battle Phase];
        B --> C[Post-Battle Phase];
        C --> A;
    end

    subgraph "Core Services"
        GameManager -- Manages --> StateManager;
        GameManager -- Manages --> InventoryManager;
        GameManager -- Manages --> RecipeManager;
        GameManager -- Manages --> ActionExecutor;
        GameManager -- Manages --> BattleSimulator;
    end

    subgraph "Data & Perception"
        VisionSystem[vision_system.py] --> RawGameState[Raw Game State];
        ItemDatabase["Backpack Battle Items.json"] --> ItemLoader;
        RawGameState --> StateManager;
        ItemLoader --> RecipeManager;
        ItemLoader --> InventoryManager;
    end

    subgraph "Control & Execution"
        Agent[Agent / Human Player] -- Issues Commands --> GameManager;
        ActionExecutor -- Controls --> MouseAndKeyboard;
    end


```

## 3. Core Components

### 3.1. `GameManager`
-   **Responsibility:** Orchestrates the entire system. It runs the main loop, determines the current game phase, and delegates tasks to other managers. It is the primary point of entry for control by a human player or an automated agent.
-   **State Machine Logic:**
    1.  **Shop Phase:**
        -   Parses the game state to identify shop items, player stats, and inventory contents.
        -   Executes buy/sell/placement strategies via the `InventoryManager` and `ActionExecutor`.
        -   Checks for item combinations via the `RecipeManager`.
        -   When ready, instructs the `ActionExecutor` to click the "Next Battle" button.
    2.  **Battle Phase:**
        -   Monitors the game state for the end of combat (e.g., by checking player health or a victory/defeat screen).
        -   Feeds the current board layout to the `BattleSimulator` to run parallel simulations if needed.
        -   Can control simulation speed by adjusting delays.
    3.  **Post-Battle Phase:**
        -   Waits for the battle summary screen.
        -   Instructs the `ActionExecutor` to click to proceed to the next shop phase.

### 3.2. `StateManager`
-   **Responsibility:** To interpret the raw data from `vision_system.py` and maintain a structured, logical representation of the current game state.
-   **Key Methods:**
    -   `update_from_vision(raw_state: dict)`: Ingests the JSON output from the vision system.
    -   `get_grid_coordinates(pixel_pos: tuple, roi: dict, grid_dims: tuple)`: Translates absolute pixel coordinates into the 1,1-based grid coordinates of the backpack (9x7).
    -   `get_current_phase()`: Determines if the game is in the "Shop", "Battle", or "Post-Battle" phase by looking for specific visual cues (e.g., presence of the "Next Battle" button, visible health bars, etc.).
    -   `get_inventory_items() -> List[Item]`: Returns a list of `Item` objects currently in the backpack.
    -   `get_shop_items() -> List[Item]`: Returns a list of `Item` objects in the shop.

### 3.3. Data Structures: `Item`, `Gem`, `Recipe`
-   **`Item` (Data Class):**
    -   Loaded from `Backpack Battle Items.json`.
    -   `name: str`
    -   `grid_layout: List[List[int]]`: A 2D matrix representing the item's shape (1 for a cell, 0 for empty space).
    -   `grid_size: tuple` (width, height)
    -   `current_rotation: int` (0, 90, 180, 270)
    -   `position: tuple` (grid coordinates (x,y))
    -   `gem_slots: List[tuple]` (coordinates for gem placement)
    -   `gems: List[Gem]`
-   **`Gem` (Data Class):**
    -   `name: str`
    -   `effects: dict`
-   **`Recipe` (Data Class):**
    -   Loaded from the `recipes` section of `Backpack Battle Items.json`.
    -   `result: str`
    -   `ingredients: List[str]`
    -   `catalyst: Optional[str]`

### 3.4. `InventoryManager`
-   **Responsibility:** The brain behind all item placement and inventory manipulation.
-   **Attributes:**
    -   `backpack_grid: np.ndarray`: A 7x9 numpy array representing the backpack, initialized to zeros.
-   **Key Methods:**
    -   `_rotate_layout(layout: List[List[int]], angle: int) -> List[List[int]]`: Rotates the item's `grid_layout` matrix.
    -   `_can_place_at(item_layout: List[List[int]], pos: tuple) -> bool`: Checks if a given item layout can be placed at `pos` without collision.
    -   `find_placement_with_backtracking(item: Item) -> Optional[tuple]`:
        -   **Algorithm:**
            1. For each of the four possible rotations (0, 90, 180, 270):
            2.  Rotate the item's `grid_layout`.
            3.  Iterate through every possible cell `(x, y)` in the 9x7 backpack grid.
            4.  If `_can_place_at()` returns `True` for the rotated layout at `(x, y)`, return the position and rotation.
            5. If all cells are checked for the current rotation, continue to the next rotation.
            6. If all rotations and all cells have been checked, return `None`.
    -   `add_item_to_grid(item: Item, pos: tuple)`: Places an item by updating `backpack_grid`.
    -   `handle_stash(target_item_name: str) -> List[Action]`: Generates a sequence of moves to retrieve an item from the stash.
        -   **Logic:** It will not simulate physics. Instead, it will identify all items occluding the `target_item`. If the stash is more than 70% visually full, it will generate drag actions to move occluding items to a temporary free space until the target is accessible.

### 3.5. `RecipeManager`
-   **Responsibility:** Handles item crafting.
-   **Methods:**
    -   `load_recipes()`: Loads and parses the `recipes` array from `Backpack Battle Items.json`.
    -   `check_for_combinations(grid: np.ndarray, items: List[Item]) -> Optional[Recipe]`:
        -   Triggered at the start of the shop phase.
        -   Iterates through all adjacent pairs of items on the grid.
        -   Cross-references adjacent item names with the loaded list of recipes.
        -   If a valid combination is found (including checking for a nearby catalyst if required), it returns the `Recipe` object. It prioritizes recipes involving the most recently placed item.

### 3.6. `ActionExecutor`
-   **Responsibility:** Translates abstract commands into low-level mouse and keyboard actions.
-   **Methods:**
    -   `click_at(coords: tuple)`: Performs a simple mouse click.
    -   `drag_and_drop(start_coords: tuple, end_coords: tuple, rotation_clicks: int = 0)`:
        -   Simulates a mouse-down at `start_coords`.
        -   Moves the mouse to `end_coords`.
        -   Performs `rotation_clicks` number of right-clicks during the drag to rotate the item.
        -   Simulates a mouse-up at `end_coords`.

### 3.7. Code Cleanup Plan
The following files from the previous implementation are identified as obsolete and will be moved to the `old_imp/` directory during the implementation phase:
-`optimizer.py`
-`inventory_optimizer.py`
-`optimization_design.md`

## 4. Conclusion
This design provides a clear, robust, and data-driven path forward. It directly addresses the shortcomings of the previous system by using a powerful placement algorithm, leveraging existing data files for accuracy, and structuring the logic around the game's actual flow.