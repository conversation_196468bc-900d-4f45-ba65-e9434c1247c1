import json
from generate_synthetic_dataset import load_item_data_from_json, filename_to_name

def debug_item_shapes():
    """Debug item shapes to see what's happening with the conversion."""
    
    # Load item data
    item_data = load_item_data_from_json("data/Backpack Battle Items.json")
    
    # Test specific items we know should have different shapes
    test_items = ["Axe", "Dagger", "Hero Sword", "Banana", "Shield"]
    
    print("Debugging item shapes:")
    print("=" * 60)
    
    for item_name in test_items:
        if item_name in item_data:
            item = item_data[item_name]
            print(f"\n{item_name}:")
            print(f"  Shape: {item.shape}")
            print(f"  Shape dimensions: {len(item.shape)}x{len(item.shape[0]) if item.shape else 0}")
            
            # Check the original JSON data
            with open("data/Backpack Battle Items.json", 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            for json_item in json_data.get('enhanced_items', []):
                if json_item.get('name') == item_name:
                    print(f"  Original grid_layout: {json_item.get('grid_layout', [])}")
                    print(f"  Grid width/height: {json_item.get('grid_width', 0)}x{json_item.get('grid_height', 0)}")
                    break
        else:
            print(f"\n{item_name}: Not found in item data")

if __name__ == "__main__":
    debug_item_shapes()
