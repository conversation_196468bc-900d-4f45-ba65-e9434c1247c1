# Precise grid coordinates detected from actual game screenshots
# Each coordinate represents one grid cell in the 9x7 inventory

INVENTORY_GRID_COORDS = [
    {'row': 0, 'col': 0, 'x1': 124, 'y1': 90, 'x2': 234, 'y2': 194, 'center_x': 179, 'center_y': 142, 'width': 110, 'height': 104},
    {'row': 0, 'col': 1, 'x1': 234, 'y1': 90, 'x2': 345, 'y2': 194, 'center_x': 289, 'center_y': 142, 'width': 111, 'height': 104},
    {'row': 0, 'col': 2, 'x1': 345, 'y1': 90, 'x2': 455, 'y2': 194, 'center_x': 400, 'center_y': 142, 'width': 110, 'height': 104},
    {'row': 0, 'col': 3, 'x1': 455, 'y1': 90, 'x2': 566, 'y2': 194, 'center_x': 510, 'center_y': 142, 'width': 111, 'height': 104},
    {'row': 0, 'col': 4, 'x1': 566, 'y1': 90, 'x2': 676, 'y2': 194, 'center_x': 621, 'center_y': 142, 'width': 110, 'height': 104},
    {'row': 0, 'col': 5, 'x1': 676, 'y1': 90, 'x2': 787, 'y2': 194, 'center_x': 732, 'center_y': 142, 'width': 111, 'height': 104},
    {'row': 0, 'col': 6, 'x1': 787, 'y1': 90, 'x2': 897, 'y2': 194, 'center_x': 842, 'center_y': 142, 'width': 110, 'height': 104},
    {'row': 0, 'col': 7, 'x1': 897, 'y1': 90, 'x2': 1008, 'y2': 194, 'center_x': 953, 'center_y': 142, 'width': 111, 'height': 104},
    {'row': 0, 'col': 8, 'x1': 1008, 'y1': 90, 'x2': 1119, 'y2': 194, 'center_x': 1063, 'center_y': 142, 'width': 111, 'height': 104},
    {'row': 1, 'col': 0, 'x1': 124, 'y1': 194, 'x2': 234, 'y2': 299, 'center_x': 179, 'center_y': 247, 'width': 110, 'height': 105},
    {'row': 1, 'col': 1, 'x1': 234, 'y1': 194, 'x2': 345, 'y2': 299, 'center_x': 289, 'center_y': 247, 'width': 111, 'height': 105},
    {'row': 1, 'col': 2, 'x1': 345, 'y1': 194, 'x2': 455, 'y2': 299, 'center_x': 400, 'center_y': 247, 'width': 110, 'height': 105},
    {'row': 1, 'col': 3, 'x1': 455, 'y1': 194, 'x2': 566, 'y2': 299, 'center_x': 510, 'center_y': 247, 'width': 111, 'height': 105},
    {'row': 1, 'col': 4, 'x1': 566, 'y1': 194, 'x2': 676, 'y2': 299, 'center_x': 621, 'center_y': 247, 'width': 110, 'height': 105},
    {'row': 1, 'col': 5, 'x1': 676, 'y1': 194, 'x2': 787, 'y2': 299, 'center_x': 732, 'center_y': 247, 'width': 111, 'height': 105},
    {'row': 1, 'col': 6, 'x1': 787, 'y1': 194, 'x2': 897, 'y2': 299, 'center_x': 842, 'center_y': 247, 'width': 110, 'height': 105},
    {'row': 1, 'col': 7, 'x1': 897, 'y1': 194, 'x2': 1008, 'y2': 299, 'center_x': 953, 'center_y': 247, 'width': 111, 'height': 105},
    {'row': 1, 'col': 8, 'x1': 1008, 'y1': 194, 'x2': 1119, 'y2': 299, 'center_x': 1063, 'center_y': 247, 'width': 111, 'height': 105},
    {'row': 2, 'col': 0, 'x1': 124, 'y1': 299, 'x2': 234, 'y2': 404, 'center_x': 179, 'center_y': 351, 'width': 110, 'height': 105},
    {'row': 2, 'col': 1, 'x1': 234, 'y1': 299, 'x2': 345, 'y2': 404, 'center_x': 289, 'center_y': 351, 'width': 111, 'height': 105},
    {'row': 2, 'col': 2, 'x1': 345, 'y1': 299, 'x2': 455, 'y2': 404, 'center_x': 400, 'center_y': 351, 'width': 110, 'height': 105},
    {'row': 2, 'col': 3, 'x1': 455, 'y1': 299, 'x2': 566, 'y2': 404, 'center_x': 510, 'center_y': 351, 'width': 111, 'height': 105},
    {'row': 2, 'col': 4, 'x1': 566, 'y1': 299, 'x2': 676, 'y2': 404, 'center_x': 621, 'center_y': 351, 'width': 110, 'height': 105},
    {'row': 2, 'col': 5, 'x1': 676, 'y1': 299, 'x2': 787, 'y2': 404, 'center_x': 732, 'center_y': 351, 'width': 111, 'height': 105},
    {'row': 2, 'col': 6, 'x1': 787, 'y1': 299, 'x2': 897, 'y2': 404, 'center_x': 842, 'center_y': 351, 'width': 110, 'height': 105},
    {'row': 2, 'col': 7, 'x1': 897, 'y1': 299, 'x2': 1008, 'y2': 404, 'center_x': 953, 'center_y': 351, 'width': 111, 'height': 105},
    {'row': 2, 'col': 8, 'x1': 1008, 'y1': 299, 'x2': 1119, 'y2': 404, 'center_x': 1063, 'center_y': 351, 'width': 111, 'height': 105},
    {'row': 3, 'col': 0, 'x1': 124, 'y1': 404, 'x2': 234, 'y2': 508, 'center_x': 179, 'center_y': 456, 'width': 110, 'height': 104},
    {'row': 3, 'col': 1, 'x1': 234, 'y1': 404, 'x2': 345, 'y2': 508, 'center_x': 289, 'center_y': 456, 'width': 111, 'height': 104},
    {'row': 3, 'col': 2, 'x1': 345, 'y1': 404, 'x2': 455, 'y2': 508, 'center_x': 400, 'center_y': 456, 'width': 110, 'height': 104},
    {'row': 3, 'col': 3, 'x1': 455, 'y1': 404, 'x2': 566, 'y2': 508, 'center_x': 510, 'center_y': 456, 'width': 111, 'height': 104},
    {'row': 3, 'col': 4, 'x1': 566, 'y1': 404, 'x2': 676, 'y2': 508, 'center_x': 621, 'center_y': 456, 'width': 110, 'height': 104},
    {'row': 3, 'col': 5, 'x1': 676, 'y1': 404, 'x2': 787, 'y2': 508, 'center_x': 732, 'center_y': 456, 'width': 111, 'height': 104},
    {'row': 3, 'col': 6, 'x1': 787, 'y1': 404, 'x2': 897, 'y2': 508, 'center_x': 842, 'center_y': 456, 'width': 110, 'height': 104},
    {'row': 3, 'col': 7, 'x1': 897, 'y1': 404, 'x2': 1008, 'y2': 508, 'center_x': 953, 'center_y': 456, 'width': 111, 'height': 104},
    {'row': 3, 'col': 8, 'x1': 1008, 'y1': 404, 'x2': 1119, 'y2': 508, 'center_x': 1063, 'center_y': 456, 'width': 111, 'height': 104},
    {'row': 4, 'col': 0, 'x1': 124, 'y1': 508, 'x2': 234, 'y2': 613, 'center_x': 179, 'center_y': 561, 'width': 110, 'height': 105},
    {'row': 4, 'col': 1, 'x1': 234, 'y1': 508, 'x2': 345, 'y2': 613, 'center_x': 289, 'center_y': 561, 'width': 111, 'height': 105},
    {'row': 4, 'col': 2, 'x1': 345, 'y1': 508, 'x2': 455, 'y2': 613, 'center_x': 400, 'center_y': 561, 'width': 110, 'height': 105},
    {'row': 4, 'col': 3, 'x1': 455, 'y1': 508, 'x2': 566, 'y2': 613, 'center_x': 510, 'center_y': 561, 'width': 111, 'height': 105},
    {'row': 4, 'col': 4, 'x1': 566, 'y1': 508, 'x2': 676, 'y2': 613, 'center_x': 621, 'center_y': 561, 'width': 110, 'height': 105},
    {'row': 4, 'col': 5, 'x1': 676, 'y1': 508, 'x2': 787, 'y2': 613, 'center_x': 732, 'center_y': 561, 'width': 111, 'height': 105},
    {'row': 4, 'col': 6, 'x1': 787, 'y1': 508, 'x2': 897, 'y2': 613, 'center_x': 842, 'center_y': 561, 'width': 110, 'height': 105},
    {'row': 4, 'col': 7, 'x1': 897, 'y1': 508, 'x2': 1008, 'y2': 613, 'center_x': 953, 'center_y': 561, 'width': 111, 'height': 105},
    {'row': 4, 'col': 8, 'x1': 1008, 'y1': 508, 'x2': 1119, 'y2': 613, 'center_x': 1063, 'center_y': 561, 'width': 111, 'height': 105},
    {'row': 5, 'col': 0, 'x1': 124, 'y1': 613, 'x2': 234, 'y2': 718, 'center_x': 179, 'center_y': 665, 'width': 110, 'height': 105},
    {'row': 5, 'col': 1, 'x1': 234, 'y1': 613, 'x2': 345, 'y2': 718, 'center_x': 289, 'center_y': 665, 'width': 111, 'height': 105},
    {'row': 5, 'col': 2, 'x1': 345, 'y1': 613, 'x2': 455, 'y2': 718, 'center_x': 400, 'center_y': 665, 'width': 110, 'height': 105},
    {'row': 5, 'col': 3, 'x1': 455, 'y1': 613, 'x2': 566, 'y2': 718, 'center_x': 510, 'center_y': 665, 'width': 111, 'height': 105},
    {'row': 5, 'col': 4, 'x1': 566, 'y1': 613, 'x2': 676, 'y2': 718, 'center_x': 621, 'center_y': 665, 'width': 110, 'height': 105},
    {'row': 5, 'col': 5, 'x1': 676, 'y1': 613, 'x2': 787, 'y2': 718, 'center_x': 732, 'center_y': 665, 'width': 111, 'height': 105},
    {'row': 5, 'col': 6, 'x1': 787, 'y1': 613, 'x2': 897, 'y2': 718, 'center_x': 842, 'center_y': 665, 'width': 110, 'height': 105},
    {'row': 5, 'col': 7, 'x1': 897, 'y1': 613, 'x2': 1008, 'y2': 718, 'center_x': 953, 'center_y': 665, 'width': 111, 'height': 105},
    {'row': 5, 'col': 8, 'x1': 1008, 'y1': 613, 'x2': 1119, 'y2': 718, 'center_x': 1063, 'center_y': 665, 'width': 111, 'height': 105},
    {'row': 6, 'col': 0, 'x1': 124, 'y1': 718, 'x2': 234, 'y2': 822, 'center_x': 179, 'center_y': 770, 'width': 110, 'height': 104},
    {'row': 6, 'col': 1, 'x1': 234, 'y1': 718, 'x2': 345, 'y2': 822, 'center_x': 289, 'center_y': 770, 'width': 111, 'height': 104},
    {'row': 6, 'col': 2, 'x1': 345, 'y1': 718, 'x2': 455, 'y2': 822, 'center_x': 400, 'center_y': 770, 'width': 110, 'height': 104},
    {'row': 6, 'col': 3, 'x1': 455, 'y1': 718, 'x2': 566, 'y2': 822, 'center_x': 510, 'center_y': 770, 'width': 111, 'height': 104},
    {'row': 6, 'col': 4, 'x1': 566, 'y1': 718, 'x2': 676, 'y2': 822, 'center_x': 621, 'center_y': 770, 'width': 110, 'height': 104},
    {'row': 6, 'col': 5, 'x1': 676, 'y1': 718, 'x2': 787, 'y2': 822, 'center_x': 732, 'center_y': 770, 'width': 111, 'height': 104},
    {'row': 6, 'col': 6, 'x1': 787, 'y1': 718, 'x2': 897, 'y2': 822, 'center_x': 842, 'center_y': 770, 'width': 110, 'height': 104},
    {'row': 6, 'col': 7, 'x1': 897, 'y1': 718, 'x2': 1008, 'y2': 822, 'center_x': 953, 'center_y': 770, 'width': 111, 'height': 104},
    {'row': 6, 'col': 8, 'x1': 1008, 'y1': 718, 'x2': 1119, 'y2': 822, 'center_x': 1063, 'center_y': 770, 'width': 111, 'height': 104},
]

def get_grid_cell(row, col):
    """Get coordinates for a specific grid cell (0-indexed)."""
    for coord in INVENTORY_GRID_COORDS:
        if coord['row'] == row and coord['col'] == col:
            return coord
    return None
