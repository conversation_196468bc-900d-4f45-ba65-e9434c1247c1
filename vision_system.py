import mss
import cv2
import numpy as np
import pytesseract
import time
import pprint
from new_item_recognizer import NewItemRecognizer
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Placeholder for Regions of Interest (ROIs)
ROIS = {
    "Full screen": {"left": 0, "top": 0, "width": 2559, "height": 1439},
    "Next Battle": {"left": 1195, "top": 101, "width": 208, "height": 131},
    "Inventory": {"left": 114, "top": 80, "width": 1015, "height": 753},
    "Sell Item": {"left": 1686, "top": 985, "width": 301, "height": 370},
    "shop": {
        "item1": {"left": 1920, "top": 101, "width": 408, "height": 251},
        "item2": {"left": 2003, "top": 415, "width": 361, "height": 223},
        "item3": {"left": 1609, "top": 413, "width": 389, "height": 239},
        "item4": {"left": 2024, "top": 722, "width": 358, "height": 220},
        "item5": {"left": 1611, "top": 722, "width": 367, "height": 224},
    },
    "Reroll Items": {"left": 1570, "top": 108, "width": 243, "height": 204},
    "Stash": {"left": 1069, "top": 282, "width": 535, "height": 1056},
    "stats": {
        "Gold": {"left": 229, "top": 1004, "width": 172, "height": 48},
        "Class": {"left": 209, "top": 962, "width": 287, "height": 42},
        "Health": {"left": 238, "top": 1060, "width": 144, "height": 40},
        "Stamina": {"left": 262, "top": 1112, "width": 90, "height": 39},
        "Round": {"left": 224, "top": 1203, "width": 69, "height": 55},
        "Wins": {"left": 209, "top": 1267, "width": 80, "height": 44},
        "Tries": {"left": 210, "top": 1322, "width": 72, "height": 34},
        "Stamina Usage": {"left": 421, "top": 1142, "width": 136, "height": 43},
    }
}

def preprocess_for_ocr(image):
    """Converts an image to grayscale and applies a binary threshold for OCR."""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)
    return thresh

def get_game_state(recognizer):
    """Captures the screen, and parses the game state from the defined ROIs."""
    with mss.mss() as sct:
        monitor = sct.monitors[1]
        screenshot = np.array(sct.grab(monitor))

    game_state = {'backpack': [], 'shop': [], 'stats': {}}

    # Parse Stats
    for stat, roi in ROIS["stats"].items():
        x, y, w, h = roi['left'], roi['top'], roi['width'], roi['height']
        stat_img = screenshot[y:y+h, x:x+w]
        
        # Preprocess for OCR
        processed_img = preprocess_for_ocr(stat_img)
        
        # Extract text using Tesseract
        custom_config = r'--psm 7 -c tessedit_char_whitelist=0123456789'
        text = pytesseract.image_to_string(processed_img, config=custom_config)
        
        # Clean and store the stat
        try:
            game_state['stats'][stat] = int(text.strip())
        except ValueError:
            game_state['stats'][stat] = 0 # Default to 0 if OCR fails

    # Parse Shop
    for shop_item_name, roi in ROIS["shop"].items():
        x, y, w, h = roi['left'], roi['top'], roi['width'], roi['height']
        shop_item_img = screenshot[y:y+h, x:x+w]
        found_items = recognizer.find_all_items(shop_item_img)
        game_state['shop'].extend(found_items)

    # Parse Backpack
    backpack_roi = ROIS["Inventory"]
    x, y, w, h = backpack_roi['left'], backpack_roi['top'], backpack_roi['width'], backpack_roi['height']
    backpack_img = screenshot[y:y+h, x:x+w]
    game_state['backpack'] = recognizer.find_all_items(backpack_img)

    return game_state

if __name__ == "__main__":
    print("Vision system starting...")
    recognizer = NewItemRecognizer(model_path='models/best.pt')

    while True:
        game_state = get_game_state(recognizer)
        print("Current Game State:")
        pprint.pprint(game_state)
        time.sleep(1)