import random
from typing import List, Dict, Any, Optional

from simulator.core import Item, ItemInstance, Backpack, Player
from simulator.battle import BattleSimulator


class Layout:
    """Represents a single, complete, and valid backpack arrangement."""

    def __init__(self, items: List[ItemInstance]):
        """
        Initializes a Layout with a specific set of item instances.
        The caller is responsible for ensuring the initial layout is valid.
        """
        self.item_instances: List[ItemInstance] = items
        self.fitness: float = -1.0  # Fitness score, initialized to a non-evaluated state

    def is_valid(self, backpack_width: int = 9, backpack_height: int = 7) -> bool:
        """
        Checks if the current layout is valid (no overlapping items).
        This is a crucial check after mutations or crossovers.
        """
        backpack = Backpack(backpack_width, backpack_height)
        for item_instance in self.item_instances:
            if not backpack.place_item(item_instance):
                # If any item cannot be placed, the layout is invalid
                return False
        return True

    @classmethod
    def create_random(cls, items: List[Item], backpack_width: int = 9, backpack_height: int = 7) -> 'Layout':
        """
        Creates a new Layout with a random, valid arrangement of the given items.
        This version uses a "first fit" approach which is more reliable than pure random placement.
        """
        
        def find_first_fit(backpack: Backpack, item: Item, rotation: int):
            instance = ItemInstance(item, 0, 0, rotation)
            rotated_shape = instance.get_rotated_shape()
            shape_h = len(rotated_shape)
            shape_w = len(rotated_shape[0]) if shape_h > 0 else 0

            if shape_w == 0 or shape_h == 0: return None

            for y in range(backpack_height - shape_h + 1):
                for x in range(backpack_width - shape_w + 1):
                    instance.x = x
                    instance.y = y
                    if backpack.can_place_item(instance):
                        return instance
            return None

        shuffled_items = list(items)
        random.shuffle(shuffled_items)

        placed_items = []
        backpack = Backpack(backpack_width, backpack_height)

        for item in shuffled_items:
            placed = False
            # Try all 4 rotations
            rotations = [0, 1, 2, 3]
            random.shuffle(rotations)
            for rotation in rotations:
                instance = find_first_fit(backpack, item, rotation)
                if instance:
                    backpack.place_item(instance)
                    placed_items.append(instance)
                    placed = True
                    break
            if not placed:
                 # This case is less likely now but could happen with a very full inventory.
                 # For simplicity, we'll just create a layout with the items that did fit.
                 print(f"Warning: Could not find a placement for {item.name}")

        return cls(placed_items)
class Evaluator:
    """
    Runs battle simulations for a given layout to determine its fitness.
    """

    def __init__(self, db_connection, opponent_player: Player, num_simulations: int = 100):
        """
        Initializes the Evaluator.
        Args:
            db_connection: A connection object for the database.
            opponent_player: A pre-configured Player object to battle against.
            num_simulations: The number of battles to run for each evaluation.
        """
        self.db_connection = db_connection
        self.opponent_player = opponent_player
        self.num_simulations = num_simulations

    def evaluate_layout(self, layout: Layout) -> float:
        """
        Evaluates the fitness of a single layout by running simulations.
        Args:
            layout: The Layout object to evaluate.

        Returns:
            The win rate (fitness score) of the layout, from 0.0 to 1.0.
        """
        wins = 0
        player1_name = "OptimizerPlayer"

        for _ in range(self.num_simulations):
            # Create a fresh player for the simulation
            player1 = Player(name=player1_name, player_class="Pyromancer")
            
            # Create a new backpack and place items according to the layout
            backpack = Backpack()
            for item_instance in layout.item_instances:
                backpack.place_item(item_instance)
            player1.backpack = backpack

            # Ensure the opponent is reset for a fair fight
            self.opponent_player.health = self.opponent_player.max_health

            simulator = BattleSimulator(player1, self.opponent_player, self.db_connection)
            winner = simulator.run_battle()
            if winner == player1_name:
                wins += 1
        
        win_rate = wins / self.num_simulations
        layout.fitness = win_rate # Cache the fitness score
        return win_rate
class Optimizer:
    """
    The main orchestrator for the genetic algorithm.
    """

    def __init__(self, items_to_place: List[Item], evaluator: Evaluator, population_size: int = 50, generations: int = 100, mutation_rate: float = 0.1, crossover_rate: float = 0.8):
        self.items_to_place = items_to_place
        self.evaluator = evaluator
        self.population_size = population_size
        self.generations = generations
        self.mutation_rate = mutation_rate
        self.crossover_rate = crossover_rate
        self.population: List[Layout] = []
        self.best_layout: Optional[Layout] = None

    def _initialize_population(self):
        """Creates the initial population of random layouts."""
        for _ in range(self.population_size):
            # The create_random method needs to be robust enough to find a valid layout
            layout = Layout.create_random(self.items_to_place)
            self.population.append(layout)

    def _select_parents(self) -> List[Layout]:
        """Selects two parents from the population using tournament selection."""
        # Simple tournament selection
        tournament_size = 5
        
        def run_tournament():
            tournament_contenders = random.sample(self.population, tournament_size)
            # Sort contenders by fitness (higher is better)
            winner = max(tournament_contenders, key=lambda x: x.fitness)
            return winner

        parent1 = run_tournament()
        parent2 = run_tournament()
        return [parent1, parent2]

    def _crossover(self, parent1: Layout, parent2: Layout) -> Layout:
        """Performs crossover between two parents to create a new child layout."""
        # Simple one-point crossover on the list of item instances
        if random.random() > self.crossover_rate:
            return parent1 # No crossover, just return one of the parents

        child_items = []
        
        # This is a naive crossover. A better implementation would be more careful
        # about avoiding duplicates and ensuring all items from the original set are present.
        num_items = len(self.items_to_place)
        
        # If there are not enough items to perform a meaningful crossover, just return a parent
        if num_items < 2:
            return parent1

        crossover_point = random.randint(1, num_items - 1)
        
        parent1_items = {inst.item.name: inst for inst in parent1.item_instances}
        parent2_items = {inst.item.name: inst for inst in parent2.item_instances}
        
        child_item_instances = {}

        # Take items from parent1 up to the crossover point
        for item in self.items_to_place[:crossover_point]:
            if item.name in parent1_items:
                 child_item_instances[item.name] = parent1_items[item.name]

        # Take remaining items from parent2
        for item in self.items_to_place[crossover_point:]:
            if item.name in parent2_items:
                 child_item_instances[item.name] = parent2_items[item.name]

        # Ensure all items are present, fill any missing
        for item in self.items_to_place:
            if item.name not in child_item_instances:
                # Find which parent had it and add it
                 if item.name in parent2_items:
                    child_item_instances[item.name] = parent2_items[item.name]
                 elif item.name in parent1_items:
                     child_item_instances[item.name] = parent1_items[item.name]

        new_layout = Layout(list(child_item_instances.values()))
        
        # If crossover results in an invalid layout, create a new random one
        # to maintain population diversity and size.
        if not new_layout.is_valid():
            return Layout.create_random(self.items_to_place)
            
        return new_layout

    def _mutate(self, layout: Layout) -> Layout:
        """Applies mutation to a layout."""
        if random.random() > self.mutation_rate:
            return layout # No mutation

        mutated_items = list(layout.item_instances)
        if not mutated_items:
            return layout

        # Choose a random item to mutate
        item_to_mutate_idx = random.randint(0, len(mutated_items) - 1)
        
        # Simple mutation: randomly change position and rotation
        # A more advanced mutation might try to "nudge" the item.
        mutated_items[item_to_mutate_idx].x = random.randint(0, self.evaluator.opponent_player.backpack.width - 1)
        mutated_items[item_to_mutate_idx].y = random.randint(0, self.evaluator.opponent_player.backpack.height - 1)
        mutated_items[item_to_mutate_idx].rotation = random.randint(0, 3)
        
        mutated_layout = Layout(mutated_items)
        
        # If mutation results in an invalid layout, try to find a new valid random spot
        # for just the mutated item to preserve the rest of the layout.
        if not mutated_layout.is_valid():
            # Revert the specific item's position and try again
            original_instance = layout.item_instances[item_to_mutate_idx]
            mutated_items[item_to_mutate_idx] = original_instance
            
            # Try to find a new valid position for the item
            for _ in range(100):
                new_x = random.randint(0, self.evaluator.opponent_player.backpack.width - 1)
                new_y = random.randint(0, self.evaluator.opponent_player.backpack.height - 1)
                new_rotation = random.randint(0, 3)
                
                # Create a test layout
                test_items = list(layout.item_instances)
                test_items[item_to_mutate_idx] = ItemInstance(original_instance.item, new_x, new_y, new_rotation)
                test_layout = Layout(test_items)
                
                if test_layout.is_valid():
                    return test_layout # Return the new valid layout
            
            # If a valid mutation can't be found, return the original layout
            return layout

        return mutated_layout

    def run_optimization(self):
        """The main loop for the genetic algorithm."""
        self._initialize_population()

        for generation in range(self.generations):
            print(f"--- Generation {generation + 1}/{self.generations} ---")

            # Evaluate the entire population
            for layout in self.population:
                self.evaluator.evaluate_layout(layout)

            # Sort by fitness and find the best in this generation
            self.population.sort(key=lambda x: x.fitness, reverse=True)
            current_best = self.population[0]

            if not self.best_layout or current_best.fitness > self.best_layout.fitness:
                self.best_layout = current_best

            print(f"Best fitness in generation: {current_best.fitness:.2f}")
            print(f"Overall best fitness: {self.best_layout.fitness:.2f}")

            # Create the next generation
            next_generation = [self.best_layout] # Elitism: carry the best over

            while len(next_generation) < self.population_size:
                parent1, parent2 = self._select_parents()
                child = self._crossover(parent1, parent2)
                child = self._mutate(child)
                next_generation.append(child)
            
            self.population = next_generation

        print("\n--- Optimization Finished ---")
        print(f"Best layout found with fitness: {self.best_layout.fitness}")
        return self.best_layout