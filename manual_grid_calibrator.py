from PIL import Image, ImageTk
import tkinter as tk
import json

class ManualGridCalibrator:
    def __init__(self, image_path):
        self.image_path = image_path
        self.points = []
        self.root = tk.Tk()
        self.root.title("Manual Grid Calibrator")

        self.image = Image.open(image_path)
        self.photo = ImageTk.PhotoImage(self.image)

        self.canvas = tk.Canvas(self.root, width=self.image.width, height=self.image.height)
        self.canvas.pack()
        self.canvas.create_image(0, 0, anchor=tk.NW, image=self.photo)
        self.canvas.bind("<Button-1>", self.on_click)

        self.label = tk.Label(self.root, text="Click the four corners of the inventory grid in order: top-left, top-right, bottom-right, bottom-left.")
        self.label.pack()

        self.root.mainloop()

    def on_click(self, event):
        if len(self.points) < 4:
            x, y = event.x, event.y
            self.points.append((x, y))
            self.canvas.create_oval(x - 3, y - 3, x + 3, y + 3, fill="red", outline="red")
            self.label.config(text=f"Point {len(self.points)} captured: ({x}, {y}). Click the next corner.")
            if len(self.points) == 4:
                self.label.config(text="All four points captured. Closing in 3 seconds...")
                self.save_coordinates()
                self.root.after(3000, self.root.destroy)
        else:
            self.label.config(text="Four points already captured. Please wait for the script to close.")

    def save_coordinates(self):
        # Coordinates for the 9x7 grid
        # self.points should be (top_left, top_right, bottom_right, bottom_left)
        calibrated_coords = {
            "top_left": self.points[0],
            "top_right": self.points[1],
            "bottom_right": self.points[2],
            "bottom_left": self.points[3]
        }

        with open("calibrated_grid_coordinates.py", "w") as f:
            f.write("# This file was generated by manual_grid_calibrator.py\n")
            f.write("# Please replace the old calibrated_grid_coordinates.py with this file.\n")
            f.write(f"CALIBRATED_GRID_COORDINATES = {calibrated_coords}\n")
        print("Calibrated coordinates saved to calibrated_grid_coordinates.py")


if __name__ == "__main__":
    image_path = "data/screenshots/Empty inventory storage and shop.png"
    calibrator = ManualGridCalibrator(image_path)
