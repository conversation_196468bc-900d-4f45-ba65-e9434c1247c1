import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk, ImageDraw
import json
import os
import cv2
import numpy as np

class ManualPlacementAnalyzer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Manual Placement Analyzer")
        self.root.geometry("1600x1000")
        
        # Data storage
        self.inventory_image = None
        self.item_templates = {}
        self.placements = []
        self.current_item = None
        self.drawing = False
        self.start_x = 0
        self.start_y = 0
        
        # Load item templates
        self.load_item_templates()
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface."""
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Control panel (left side)
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # File loading
        ttk.Label(control_frame, text="1. Load Inventory Screenshot:").pack(anchor=tk.W, pady=(0, 5))
        ttk.Button(control_frame, text="Load Screenshot", command=self.load_screenshot).pack(fill=tk.X, pady=(0, 10))
        
        # Item selection
        ttk.Label(control_frame, text="2. Select Item to Mark:").pack(anchor=tk.W)
        self.item_var = tk.StringVar()
        self.item_combo = ttk.Combobox(control_frame, textvariable=self.item_var, width=30)
        self.item_combo.pack(fill=tk.X, pady=(0, 10))
        
        # Instructions
        instructions = """
3. Mark Items in Screenshot:
- Select an item from dropdown
- Click and drag to draw rectangle around the item in the inventory
- The rectangle should tightly fit the item
- Repeat for all visible items

4. Analysis:
- Tool will analyze item sizes and positions
- Extract grid placement patterns
- Generate scaling data for synthetic images
        """
        ttk.Label(control_frame, text=instructions, justify=tk.LEFT, 
                 wraplength=250, font=('Arial', 9)).pack(pady=(0, 10))
        
        # Current placements list
        ttk.Label(control_frame, text="Marked Items:").pack(anchor=tk.W)
        self.placement_listbox = tk.Listbox(control_frame, height=10)
        self.placement_listbox.pack(fill=tk.X, pady=(0, 10))
        
        # Buttons
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="Clear Last", command=self.clear_last).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="Clear All", command=self.clear_all).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="Analyze Placements", command=self.analyze_placements).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="Save Data", command=self.save_data).pack(fill=tk.X, pady=(0, 5))
        
        # Canvas (right side)
        canvas_frame = ttk.Frame(main_frame)
        canvas_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # Create canvas with scrollbars
        self.canvas = tk.Canvas(canvas_frame, bg='white', cursor='crosshair')
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        h_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)
        self.canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack scrollbars and canvas
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Bind mouse events
        self.canvas.bind('<Button-1>', self.on_mouse_down)
        self.canvas.bind('<B1-Motion>', self.on_mouse_drag)
        self.canvas.bind('<ButtonRelease-1>', self.on_mouse_up)
        
    def load_item_templates(self):
        """Load item template images for reference."""
        item_dir = "data/item_images"
        if os.path.exists(item_dir):
            item_files = [f for f in os.listdir(item_dir) if f.endswith('.png')]
            
            # Focus on key items first
            priority_items = ['Axe.png', 'Dagger.png', 'Hero_Sword.png', 'Banana.png', 
                            'Shield.png', 'Bow.png', 'Sword.png', 'Apple.png']
            
            # Put priority items first, then others
            sorted_items = []
            for item in priority_items:
                if item in item_files:
                    sorted_items.append(item.replace('.png', ''))
            for item in item_files:
                item_name = item.replace('.png', '')
                if item_name not in sorted_items:
                    sorted_items.append(item_name)
            
            if hasattr(self, 'item_combo'):
                self.item_combo['values'] = sorted_items
                if sorted_items:
                    self.item_combo.set(sorted_items[0])
        
    def load_screenshot(self):
        """Load an inventory screenshot."""
        file_path = filedialog.askopenfilename(
            title="Select Inventory Screenshot",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.bmp *.gif")]
        )
        
        if file_path:
            self.inventory_image = Image.open(file_path)
            print(f"Loaded screenshot: {self.inventory_image.size[0]}x{self.inventory_image.size[1]} pixels")
            
            # Display on canvas
            self.display_image()
            
    def display_image(self):
        """Display the inventory image on canvas."""
        if not self.inventory_image:
            return
            
        # Create a copy for display with markings
        display_img = self.inventory_image.copy()
        draw = ImageDraw.Draw(display_img)
        
        # Draw existing placements
        for i, placement in enumerate(self.placements):
            x1, y1, x2, y2 = placement['bbox']
            item_name = placement['item']
            
            # Draw rectangle
            draw.rectangle([x1, y1, x2, y2], outline='red', width=3)
            
            # Draw label
            draw.text((x1, y1-20), f"{i+1}. {item_name}", fill='red')
        
        # Convert to PhotoImage
        self.photo = ImageTk.PhotoImage(display_img)
        
        # Clear canvas and add image
        self.canvas.delete("all")
        self.canvas.create_image(0, 0, anchor=tk.NW, image=self.photo)
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        
    def on_mouse_down(self, event):
        """Handle mouse down for starting rectangle."""
        if not self.inventory_image:
            messagebox.showwarning("Warning", "Please load a screenshot first!")
            return
            
        if not self.item_var.get():
            messagebox.showwarning("Warning", "Please select an item first!")
            return
            
        self.drawing = True
        self.start_x = self.canvas.canvasx(event.x)
        self.start_y = self.canvas.canvasy(event.y)
        
    def on_mouse_drag(self, event):
        """Handle mouse drag for drawing rectangle."""
        if self.drawing:
            # Remove previous temporary rectangle
            self.canvas.delete("temp_rect")
            
            # Draw current rectangle
            current_x = self.canvas.canvasx(event.x)
            current_y = self.canvas.canvasy(event.y)
            
            self.canvas.create_rectangle(
                self.start_x, self.start_y, current_x, current_y,
                outline='blue', width=2, tags="temp_rect"
            )
            
    def on_mouse_up(self, event):
        """Handle mouse up for finishing rectangle."""
        if self.drawing:
            self.drawing = False
            
            # Get final coordinates
            end_x = self.canvas.canvasx(event.x)
            end_y = self.canvas.canvasy(event.y)
            
            # Ensure proper order (top-left to bottom-right)
            x1 = min(self.start_x, end_x)
            y1 = min(self.start_y, end_y)
            x2 = max(self.start_x, end_x)
            y2 = max(self.start_y, end_y)
            
            # Only add if rectangle is large enough
            if abs(x2 - x1) > 10 and abs(y2 - y1) > 10:
                placement = {
                    'item': self.item_var.get(),
                    'bbox': [int(x1), int(y1), int(x2), int(y2)],
                    'width': int(x2 - x1),
                    'height': int(y2 - y1),
                    'center_x': int((x1 + x2) / 2),
                    'center_y': int((y1 + y2) / 2)
                }
                
                self.placements.append(placement)
                
                # Update listbox
                self.placement_listbox.insert(tk.END, 
                    f"{placement['item']}: {placement['width']}x{placement['height']}")
                
                # Refresh display
                self.display_image()
                
            # Remove temporary rectangle
            self.canvas.delete("temp_rect")
            
    def clear_last(self):
        """Remove the last placement."""
        if self.placements:
            self.placements.pop()
            self.placement_listbox.delete(tk.END)
            self.display_image()
            
    def clear_all(self):
        """Clear all placements."""
        self.placements = []
        self.placement_listbox.delete(0, tk.END)
        self.display_image()
        
    def analyze_placements(self):
        """Analyze the manual placements to extract patterns."""
        if not self.placements:
            messagebox.showwarning("Warning", "No placements to analyze!")
            return
            
        print("\nAnalyzing manual placements...")
        print("=" * 50)
        
        # Group by item type
        item_sizes = {}
        for placement in self.placements:
            item_name = placement['item']
            if item_name not in item_sizes:
                item_sizes[item_name] = []
            item_sizes[item_name].append({
                'width': placement['width'],
                'height': placement['height']
            })
        
        # Calculate average sizes
        analysis_results = {}
        for item_name, sizes in item_sizes.items():
            avg_width = sum(s['width'] for s in sizes) / len(sizes)
            avg_height = sum(s['height'] for s in sizes) / len(sizes)
            
            analysis_results[item_name] = {
                'average_width': round(avg_width, 1),
                'average_height': round(avg_height, 1),
                'samples': len(sizes),
                'all_sizes': sizes
            }
            
            print(f"{item_name}:")
            print(f"  Average size: {avg_width:.1f}x{avg_height:.1f} pixels")
            print(f"  Samples: {len(sizes)}")
            if len(sizes) > 1:
                widths = [s['width'] for s in sizes]
                heights = [s['height'] for s in sizes]
                print(f"  Width range: {min(widths)}-{max(widths)}")
                print(f"  Height range: {min(heights)}-{max(heights)}")
            print()
        
        # Save analysis
        with open('manual_placement_analysis.json', 'w') as f:
            json.dump(analysis_results, f, indent=2)
            
        messagebox.showinfo("Analysis Complete", 
                          f"Analyzed {len(self.placements)} placements for {len(item_sizes)} item types.\n"
                          f"Results saved to manual_placement_analysis.json")
        
    def save_data(self):
        """Save all placement data."""
        if not self.placements:
            messagebox.showwarning("Warning", "No placements to save!")
            return
            
        data = {
            'screenshot_size': self.inventory_image.size if self.inventory_image else None,
            'placements': self.placements,
            'total_items': len(self.placements)
        }
        
        with open('manual_placement_data.json', 'w') as f:
            json.dump(data, f, indent=2)
            
        messagebox.showinfo("Saved", f"Saved {len(self.placements)} placements to manual_placement_data.json")
        
    def run(self):
        """Run the application."""
        self.root.mainloop()

if __name__ == "__main__":
    app = ManualPlacementAnalyzer()
    app.run()
