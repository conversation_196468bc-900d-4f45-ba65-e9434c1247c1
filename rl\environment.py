import gymnasium as gym
from gymnasium import spaces
import numpy as np
import sqlite3
import random

from simulator.core import Game<PERSON><PERSON>, <PERSON>, Item, ItemInstance, load_all_item_data
from simulator.battle import BattleSimulator
from simulator.shop import Shop


class BackpackBattlesEnv(gym.Env):
    """A Gymnasium environment for the Backpack Battles simulator."""
    metadata = {'render.modes': ['human']}

    def __init__(self, opponent_builds: list):
        """
        Initializes the Backpack Battles environment.

        Args:
            opponent_builds (list): A list of builds for opponents.
        """
        super().__init__()
        self.db_path = 'GameData.db'
        self.db_conn = sqlite3.connect(self.db_path)
        self.all_items = load_all_item_data(self.db_path)
        
        self.opponent_builds = opponent_builds
        self.game: GameState = None
        self.player: Player = None
        self.shop: Shop = None

        # Define action and observation spaces
        self.observation_space = spaces.Box(low=0, high=1, shape=(82,), dtype=np.float32)
        self.action_space = spaces.Dict({
            "action_type": spaces.Discrete(4),  # 0: BUY, 1: SELL, 2: MOVE, 3: END_TURN
            "item_slot": spaces.Discrete(5 + (9 * 7)),  # 5 shop slots & 63 backpack slots
            "target_slot": spaces.Discrete(9 * 7),
            "rotation": spaces.Discrete(4)
        })

    def _get_obs(self) -> np.ndarray:
        """
        Converts the current game state into a flattened NumPy array.
        """
        if self.player is None:
            return np.zeros(self.observation_space.shape, dtype=np.float32)

        # Player stats (4 values)
        player_stats = np.array([
            self.player.gold / 50.0,  # Max gold
            self.player.health / 100.0, # Max health
            self.player.lives / 10.0, # Max lives
            self.game.current_round / 18.0 # Max rounds
        ], dtype=np.float32)

        # Shop items (5 slots * 3 features = 15 values)
        shop_features = []
        for item in self.shop.current_offerings:
            if item:
                # Normalize features (e.g., by max values)
                shop_features.extend([
                    item.id / len(self.all_items),
                    item.cost / 30.0,  # Max cost
                    item.rarity / 5.0 # Max rarity
                ])
            else:
                shop_features.extend([0, 0, 0])
        # Pad if less than 5 items
        while len(shop_features) < 15:
            shop_features.extend([0,0,0])
        
        shop_features = np.array(shop_features[:15], dtype=np.float32)

        # Backpack grid (9x7 = 63 values)
        # Assuming grid stores item IDs. Normalizing by total number of items.
        backpack_grid = np.array(self.player.backpack.grid, dtype=np.float32).flatten() / len(self.all_items)

        # Concatenate all parts
        obs = np.concatenate([player_stats, shop_features, backpack_grid])
        return obs
    
    def _get_action_mask(self) -> dict:
        """
        Computes a mask of valid actions.
        For now, returns a mask indicating all actions are valid.
        """
        return {
              "action_type": np.ones(4, dtype=np.int8),
              "item_slot": np.ones(5 + (9*7), dtype=np.int8),
              "target_slot": np.ones(9*7, dtype=np.int8),
              "rotation": np.ones(4, dtype=np.int8)
        }

    def _get_info(self) -> dict:
        """
        Returns a dictionary with auxiliary data.
        """
        if self.player is None:
            return {}
        info = {
            "round": self.game.current_round,
            "lives": self.player.lives,
            "wins": self.player.wins,
            "gold": self.player.gold,
        }
        info['action_mask'] = self._get_action_mask()
        return info

    def reset(self, seed=None, options=None) -> tuple:
        """
        Resets the environment to an initial state.

        Args:
            seed (int, optional): The seed for the random number generator.
            options (dict, optional): Additional options for resetting the environment.

        Returns:
            A tuple containing the initial observation and info.
        """
        super().reset(seed=seed)

        self.player = Player(name="Agent", player_class="Fighter")
        self.game = GameState(players=[self.player])
        self.shop = Shop(self.all_items)
        self.shop.generate_new_offerings(self.game.current_round)

        observation = self._get_obs()
        info = self._get_info()

        return observation, info

    def step(self, action) -> tuple:
        """
        Executes one time step within the environment.

        Args:
            action: The action to be performed.

        Returns:
            A tuple containing the observation, reward, terminated flag, truncated flag, and info.
        """
        # 1. Apply action
        action_type = action["action_type"]
        if action_type == 0:  # BUY
            # This is a simplified interpretation. A real implementation would be more complex.
            item_slot = action["item_slot"]
            target_slot = action["target_slot"]
            if item_slot < 5 and self.shop.current_offerings[item_slot]:
                x = target_slot % self.player.backpack.width
                y = target_slot // self.player.backpack.width
                # self.shop.buy_item(self.player, self.shop.current_items[item_slot].name, x, y)
        elif action_type == 1:  # SELL
            item_slot = action["item_slot"]
            if item_slot >= 5:
                # Need a way to map from item_slot to an item_instance_id
                pass
        elif action_type == 2: # MOVE
             # Needs a robust way to map slots to items and positions
             pass
        # action_type 3 is END_TURN, which proceeds to battle.

        # 2. Select an opponent
        opponent_build_data = random.choice(self.opponent_builds)
        opponent = Player(name=opponent_build_data['name'], player_class=opponent_build_data['class'])
        # (Load opponent's backpack based on build data)

        # 3. Run the battle
        simulator = BattleSimulator(self.player, opponent, self.db_conn)
        winner_name = simulator.run_battle()

        # 4. Calculate reward
        reward = 0
        if winner_name == self.player.name:
            reward = 1
            self.player.wins +=1
        elif winner_name == "Draw":
            reward = 0
        else:
            reward = -1
            self.player.lives -= 1
        
        self.game.current_round += 1

        # 5. Check for termination
        terminated = self.player.lives <= 0 or self.player.wins >= 10
        
        # 6. Roll shop for next turn
        if not terminated:
            self.shop.generate_new_offerings(self.game.current_round)
            
        observation = self._get_obs()
        info = self._get_info()

        return observation, reward, terminated, False, info

    def render(self, mode='human'):
        """
        Renders the environment.
        """
        if self.player is None:
            print("Game has not been reset yet.")
            return

        print("\n" + "="*30)
        print(f"| Player: {self.player.name} | Round: {self.game.current_round} |")
        print(f"| Gold: {self.player.gold} | Health: {self.player.health} | Lives: {self.player.lives} | Wins: {self.player.wins} |")
        print("="*30)
        print("Backpack:")
        
        # Create a display grid
        display_grid = [['.' for _ in range(self.player.backpack.width)] for _ in range(self.player.backpack.height)]
        
        for item_instance in self.player.backpack.items.values():
            shape = item_instance.get_rotated_shape()
            for r, row in enumerate(shape):
                for c, cell in enumerate(row):
                    if cell == 1:
                        y, x = item_instance.y + r, item_instance.x + c
                        if 0 <= y < self.player.backpack.height and 0 <= x < self.player.backpack.width:
                            display_grid[y][x] = item_instance.item.name[0]
        
        for row in display_grid:
            print(" ".join(row))
        print("="*30)

    def close(self):
        """
        Performs any necessary cleanup.
        """
        if self.db_conn:
            self.db_conn.close()
        print("Database connection closed.")