import sqlite3
import logging
import json
import requests
from bs4 import BeautifulSoup
import re

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def setup_database():
    """
    Connects to the database, clears existing tables, and sets up new tables.
    """
    conn = sqlite3.connect('GameData.db')
    cursor = conn.cursor()
    logging.info("Successfully connected to database.")

    # Drop existing tables to ensure a fresh start
    cursor.execute("DROP TABLE IF EXISTS Items")
    cursor.execute("DROP TABLE IF EXISTS Recipes")
    cursor.execute("DROP TABLE IF EXISTS Mechanics")
    logging.info("Cleared existing tables.")

    # Create Items table
    cursor.execute("""
    CREATE TABLE Items (
        ItemID INTEGER PRIMARY KEY AUTOINCREMENT,
        Name TEXT NOT NULL UNIQUE,
        Rarity TEXT,
        Cost INTEGER,
        Class TEXT,
        Type TEXT,
        Shape TEXT,
        Description TEXT,
        RawStats TEXT,
        SynergyTriggers TEXT,
        SynergyEffects TEXT
    )
    """)
    logging.info("Created 'Items' table.")


    # Create Recipes table
    cursor.execute("""
    CREATE TABLE Recipes (
        RecipeID INTEGER PRIMARY KEY AUTOINCREMENT,
        ResultingItemName TEXT NOT NULL UNIQUE,
        Ingredients TEXT NOT NULL
    )
    """)
    logging.info("Created 'Recipes' table.")

    # Create Mechanics table
    cursor.execute("""
    CREATE TABLE Mechanics (
        MechanicID INTEGER PRIMARY KEY AUTOINCREMENT,
        Name TEXT NOT NULL UNIQUE,
        Type TEXT,
        Description TEXT,
        EffectFormula TEXT
    )
    """)
    logging.info("Created 'Mechanics' table.")

    return conn, cursor

def scrape_single_item_page(url, db_cursor):
    """
    Scrapes data for a single item from its dedicated wiki page.
    """
    try:
        logging.info(f"Scraping item page: {url}")
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')

        item_data = {
            "Name": None, "Rarity": None, "Cost": None, "Class": None,
            "Type": None, "Shape": None, "Description": "", "RawStats": None,
            "SynergyTriggers": None, "SynergyEffects": None
        }

        # Get Name from title
        title_tag = soup.find('h1', id='firstHeading')
        if title_tag:
            item_data['Name'] = title_tag.text.strip().replace(" (Item)", "")


        infobox = soup.find('table', class_='infobox')
        if not infobox and item_data['Name'] :
             logging.warning(f"No infobox found on page: {url}, for item {item_data['Name']}")
        elif not infobox:
             logging.warning(f"No infobox found on page: {url}, and no name found")
             return
        
        # Helper to find data in infobox
        def get_infobox_data(label_text):
            element = infobox.find(lambda tag: tag.name == 'th' and label_text in tag.get_text(strip=True))
            if element and element.find_next_sibling('td'):
                return element.find_next_sibling('td').text.strip()
            return None

        if infobox:
            item_data['Rarity'] = get_infobox_data('Rarity')
            item_data['Cost'] = get_infobox_data('Cost')
            item_data['Class'] = get_infobox_data('Class')
            item_data['Type'] = get_infobox_data('Type')

            # Clean up cost
            if item_data['Cost']:
                cost_match = re.search(r'\d+', item_data['Cost'])
                item_data['Cost'] = int(cost_match.group()) if cost_match else None


            # Description
            desc_div = infobox.find('div', class_='item-description')
            if desc_div:
                item_data['Description'] = desc_div.text.strip()
        
        # Fallback for description outside infobox if needed
        if not item_data['Description']:
            desc_header = soup.find('span', class_='mw-headline', id='Description')
            if desc_header:
                desc_p = desc_header.find_next('p')
                if desc_p:
                    item_data['Description'] = desc_p.text.strip()


        # Shape
        shape_table = soup.find('table', class_='item-shape')
        if shape_table:
            shape_grid = []
            for row in shape_table.select('tr'):
                shape_row = [1 if 'item-shape-cell-full' in cell.get('class', []) else 0 for cell in row.select('td')]
                shape_grid.append(shape_row)
            item_data['Shape'] = json.dumps(shape_grid) if shape_grid else None
        
        # Stats
        stats_table = soup.find('table', class_='wikitable item-stats')
        if stats_table:
            stats = {}
            for row in stats_table.find_all('tr')[1:]: # Skip header
                cells = row.find_all(['th', 'td'])
                if len(cells) == 2:
                    key = cells[0].text.strip()
                    value = cells[1].text.strip()
                    stats[key] = value
            item_data['RawStats'] = json.dumps(stats) if stats else None


        # Synergy
        # For now, we will leave them blank as parsing them accurately from unstructured
        # text is very complex.
        item_data['SynergyTriggers'] = json.dumps([])
        item_data['SynergyEffects'] = json.dumps([])


        # --- Database Insertion ---
        if item_data['Name']:
            db_cursor.execute("""
                INSERT OR IGNORE INTO Items (Name, Rarity, Cost, Class, Type, Shape, Description, RawStats, SynergyTriggers, SynergyEffects)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                item_data['Name'], item_data['Rarity'], item_data['Cost'], item_data['Class'],
                item_data['Type'], item_data['Shape'], item_data['Description'], item_data['RawStats'],
                item_data['SynergyTriggers'], item_data['SynergyEffects']
            ))
            if db_cursor.rowcount > 0:
                logging.info(f"Successfully inserted '{item_data['Name']}' into the database.")
            else:
                logging.warning(f"Item '{item_data['Name']}' already exists or failed to insert.")
        else:
            logging.warning(f"Skipping insertion due to missing name for URL: {url}")

    except requests.exceptions.RequestException as e:
        logging.error(f"Error fetching item page {url}: {e}")
    except Exception as e:
        logging.error(f"An unexpected error occurred while a single item page {url}: {e}", exc_info=True)


def scrape_items(db_cursor):
    """
    Scrapes item data from the Backpack Battles Wiki and inserts it into the database.
    """
    main_url = 'https://backpackbattles.wiki.gg/wiki/Items'
    base_url = 'https://backpackbattles.wiki.gg'
    logging.info(f"Starting to scrape items from {main_url}")

    try:
        response = requests.get(main_url, timeout=10)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')

        item_link_tags = soup.select('.navbox .hlist a')
        logging.info(f"Found {len(item_link_tags)} item links in navboxes.")
        
        item_links = set()
        for tag in item_link_tags:
            link = tag.get('href')
            if link and not link.startswith('/wiki/Recipe'):
                item_links.add(base_url + link)

        logging.info(f"Found {len(item_links)} unique item links.")
        for link in list(item_links):
            scrape_single_item_page(link, db_cursor)

    except requests.exceptions.RequestException as e:
        logging.error(f"Error fetching main items page: {e}")
    except Exception as e:
        logging.error(f"An unexpected error occurred during item scraping: {e}", exc_info=True)


def scrape_recipes(db_cursor):
    """
    Scrapes recipe data from the Backpack Battles Wiki and inserts it into the database.
    """
    # The wiki structure has changed and recipe data is now found on individual item pages.
    url = "https://backpackbattles.wiki.gg/wiki/Recipe"

    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')

        # Find all tables with the class 'recipe-table'
        recipe_tables = soup.find_all('table', class_='wikitable')
        logging.info(f"Found {len(recipe_tables)} recipe tables.")

        for table in recipe_tables:
            # Iterate over each row in the table body, skipping the header
            for row in table.select('tbody tr'):
                cells = row.find_all('td')
                if len(cells) < 3:
                    continue

                try:
                    # Extract ingredient names from the first cell
                    ingredients_cell = cells[0]
                    ingredient_tags = ingredients_cell.find_all('a')
                    # Sort ingredients to ensure consistency
                    ingredients = sorted([a.get('title', '').replace(" (Item)", "").strip() for a in ingredient_tags if a.get('title')])
                    
                    if not ingredients:
                        logging.warning("No ingredients found in a recipe row, skipping.")
                        continue

                    # Extract resulting item name from the third cell
                    result_cell = cells[2]
                    result_tag = result_cell.find('a')
                    if not result_tag or not result_tag.get('title'):
                        logging.warning("No resulting item found in a recipe row, skipping.")
                        continue
                    
                    result_name = result_tag.get('title').replace(" (Item)", "").strip()
                    
                    # Store ingredients as a JSON formatted string
                    ingredients_json = json.dumps(ingredients)

                    # Insert the recipe into the database, ignoring if it already exists
                    db_cursor.execute("""
                        INSERT OR IGNORE INTO Recipes (ResultingItemName, Ingredients)
                        VALUES (?, ?)
                    """, (result_name, ingredients_json))

                    if db_cursor.rowcount > 0:
                        logging.info(f"Successfully scraped and inserted recipe for: {result_name}")
                    
                except Exception as e:
                    logging.error(f"Error processing a recipe row: {e}", exc_info=True)

    except requests.exceptions.RequestException as e:
        logging.error(f"Error fetching the recipes page {url}: {e}")
    except Exception as e:
        logging.error(f"An unexpected error occurred during recipe scraping: {e}", exc_info=True)

def scrape_mechanics(db_cursor):
    """
    Scrapes game mechanics data from the Backpack Battles Wiki and inserts it into the database.
    """
    url = 'https://backpackbattles.wiki.gg/wiki/Game_Mechanics'
    logging.info(f"Starting to scrape mechanics from {url}")

    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')

        # Sections are expected to be under h2 tags
        sections = soup.find_all('h2')
        
        for section in sections:
            section_title = section.find('span', class_='mw-headline')
            if not section_title:
                continue
                
            mechanic_type = section_title.text.strip()
            
            # Find the list or table that contains the mechanics for this section
            container = section.find_next_sibling(['ul', 'table'])
            
            if not container:
                logging.warning(f"No list or table found for section: {mechanic_type}")
                continue

            if container.name == 'ul':
                list_items = container.find_all('li')
                for item in list_items:
                    # Extract name and description
                    name_tag = item.find('b')
                    if not name_tag:
                        continue
                    
                    name = name_tag.text.strip().replace(':', '')
                    description = item.text.split(':', 1)[-1].strip()
                    formula = None # Formulae are less common in these lists
                    
                    db_cursor.execute("""
                        INSERT OR IGNORE INTO Mechanics (Name, Type, Description, EffectFormula)
                        VALUES (?, ?, ?, ?)
                    """, (name, mechanic_type, description, formula))
                    if db_cursor.rowcount > 0:
                        logging.info(f"Successfully scraped and inserted mechanic: {name} ({mechanic_type})")

            elif container.name == 'table' and 'wikitable' in container.get('class', []):
                rows = container.find_all('tr')[1:] # Skip header row
                for row in rows:
                    cells = row.find_all('td')
                    if len(cells) >= 2:
                        name = cells[0].text.strip()
                        description = cells[1].text.strip()
                        # Some tables might have a formula in the 3rd column
                        formula = cells[2].text.strip() if len(cells) > 2 else None
                        
                        db_cursor.execute("""
                            INSERT OR IGNORE INTO Mechanics (Name, Type, Description, EffectFormula)
                            VALUES (?, ?, ?, ?)
                        """, (name, mechanic_type, description, formula))
                        if db_cursor.rowcount > 0:
                            logging.info(f"Successfully scraped and inserted mechanic: {name} ({mechanic_type})")

    except requests.exceptions.RequestException as e:
        logging.error(f"Error fetching the mechanics page {url}: {e}")
    except Exception as e:
        logging.error(f"An unexpected error occurred during mechanics scraping: {e}", exc_info=True)


if __name__ == "__main__":
    conn, cursor = setup_database()

    # Scrape data
    scrape_items(cursor)
    scrape_recipes(cursor)
    scrape_mechanics(cursor)

    conn.commit()
    conn.close()
    logging.info("Database transactions complete. Connection closed.")