import json
import arg<PERSON><PERSON>

def convert_grid_layout_to_shape(grid_layout, grid_width=None, grid_height=None):
    """
    Converts the JSON grid_layout to a compact, numeric shape.
    This version handles both 2D and flattened 1D grid_layouts by using
    grid_width and grid_height to reconstruct the 2D shape if necessary.
    It treats 'cell', 'star', 'triangle', and 'diamond' as part of the shape.
    """
    if not grid_layout or not isinstance(grid_layout, list):
        return [[1]]

    # If it's a 1D list, attempt to reshape it using provided width/height
    if not isinstance(grid_layout[0], list):
        if grid_width is not None and grid_height is not None and grid_width > 0 and grid_height > 0:
            if len(grid_layout) != (grid_width * grid_height):
                 print(f"Warning: Flattened grid_layout length ({len(grid_layout)}) does not match grid_width ({grid_width}) * grid_height ({grid_height}). Attempting to reshape based on grid_width.")
            
            reshaped_grid_layout = []
            for r in range(grid_height):
                row = grid_layout[r * grid_width : min((r + 1) * grid_width, len(grid_layout))]
                reshaped_grid_layout.append(row)
            grid_layout = reshaped_grid_layout
        else:
            # If it's a 1D list and no valid width/height, assume 1xn shape
            grid_layout = [grid_layout] # Wrap it in a list to make it 2D (1 row)


    # Now grid_layout is guaranteed to be a 2D list (list of lists)
    cell_positions = []
    for r, row in enumerate(grid_layout):
        for c, cell_value in enumerate(row):
            if cell_value and cell_value != "":  # Treat any non-empty/non-null cell as part of the shape
                cell_positions.append((r, c))

    if not cell_positions:
        return [[1]]

    # Find the bounding box of all non-empty cells
    min_r = min(pos[0] for pos in cell_positions)
    max_r = max(pos[0] for pos in cell_positions)
    min_c = min(pos[1] for pos in cell_positions)
    max_c = max(pos[1] for pos in cell_positions)

    # Create the shape array
    shape = []
    for r in range(min_r, max_r + 1):
        row_shape = []
        for c in range(min_c, max_c + 1):
            if (r, c) in cell_positions:
                row_shape.append(1)
            else:
                row_shape.append(0)
        shape.append(row_shape)

    return shape

def verify_item_shape(item_name):
    """
    Loads the item data, finds the specified item, and prints its calculated shape.
    """
    json_path = "Backpack Battle Items.json"
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    item_found = False
    for item_data in data.get('enhanced_items', []):
        if item_data.get('name', '').lower() == item_name.lower():
            item_found = True
            grid_layout = item_data.get('grid_layout', [[1]])
            grid_width = item_data.get('grid_width')
            grid_height = item_data.get('grid_height')

            print(f"Raw grid_layout for '{item_data.get('name')}':")
            print(grid_layout)
            print(f"grid_width: {grid_width}, grid_height: {grid_height}")
            
            shape = convert_grid_layout_to_shape(grid_layout, grid_width, grid_height)
            
            print(f"Calculated Shape for '{item_data.get('name')}':")
            for row in shape:
                print(row)
            break
    
    if not item_found:
        print(f"Item '{item_name}' not found in the JSON data.")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Verify the calculated shape of a given item.")
    parser.add_argument("item_name", type=str, help="The name of the item to verify (e.g., 'Poison Bow').")
    args = parser.parse_args()
    verify_item_shape(args.item_name)
