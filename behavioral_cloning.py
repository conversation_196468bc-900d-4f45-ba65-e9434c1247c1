import json
import time
import uuid
import os
import mouse
import keyboard
import threading
import numpy as np
from vision_system import get_game_state
from item_recognizer import load_item_templates

TEMPLATE_FOLDER = 'data/item_images/'
DATA_DIR = 'cloning_data'
if not os.path.exists(DATA_DIR):
    os.makedirs(DATA_DIR)

class NpEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        return super(NpEncoder, self).default(obj)

# Load templates once to be used by the hotkey callback
ITEM_TEMPLATES = load_item_templates(TEMPLATE_FOLDER)

# Add a lock to prevent race conditions during file I/O
capture_lock = threading.Lock()

def capture_game_state():
    """Captures the current game state and saves it to a file."""
    print("Mouse clicked! Capturing game state...")
    
    with capture_lock:
        # 1. Capture the current game state
        game_state = get_game_state(ITEM_TEMPLATES)
        
        # 2. Save the state to a unique file
        file_id = str(uuid.uuid4())
        file_path = os.path.join(DATA_DIR, f"{file_id}.json")
        
        record = {
            'id': file_id,
            'timestamp': time.time(),
            'game_state': game_state,
            'action': None,  # Action will be labeled later
        }
        
        with open(file_path, 'w') as f:
            json.dump(record, f, indent=4, cls=NpEncoder)
            
        print(f"Successfully saved game state to {file_path}")

def main():
    """
    Cleans the data directory and sets up a hotkey to capture game states.
    """
    # print(f"Clearing old data from {DATA_DIR}...")
    # for filename in os.listdir(DATA_DIR):
    #     if filename.endswith('.json'):
    #         os.remove(os.path.join(DATA_DIR, filename))
    
    print("Starting behavioral cloning data capture with mouse clicks.")
    print("Left-click in your game to capture the state.")
    print("Press 'ESC' to stop the script.")

    # Assign the capture function to the left mouse button
    mouse.on_click(lambda: threading.Thread(target=capture_game_state).start())

    # Use keyboard to wait for 'esc' to stop the script
    keyboard.wait('esc')

    print("\nData capture stopped. Dataset saved in:", DATA_DIR)

if __name__ == "__main__":
    main()