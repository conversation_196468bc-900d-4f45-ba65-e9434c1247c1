import json
from typing import List, Dict, Any, Optional
import numpy as np
from .item import Item
from .inventory_manager import InventoryManager

class Recipe:
    def __init__(self, result: str, ingredients: List[str], catalyst: Optional[str] = None):
        self.result = result
        self.ingredients = sorted(ingredients)
        self.catalyst = catalyst

    def __repr__(self):
        return f"Recipe(result='{self.result}', ingredients={self.ingredients}, catalyst='{self.catalyst}')"

class RecipeManager:
    """
    Manages all crafting recipes and checks for valid combinations
    in the backpack.
    """
    def __init__(self, recipes_data: List[Dict[str, Any]]):
        self.recipes = self._load_recipes_from_data(recipes_data)

    def _load_recipes_from_data(self, recipes_data: List[Dict[str, Any]]) -> List[Recipe]:
        """Loads and parses recipes from a list of dictionaries."""
        if not recipes_data:
            return []
        return [Recipe(**recipe_data) for recipe_data in recipes_data]

    def _are_items_adjacent(self, item1: Item, item2: Item) -> bool:
        """Check if two items are orthogonally adjacent."""
        # Bounding box for item1
        x1_min, y1_min = item1.position
        x1_max, y1_max = x1_min + item1.grid_size[0], y1_min + item1.grid_size[1]
        
        # Bounding box for item2
        x2_min, y2_min = item2.position
        x2_max, y2_max = x2_min + item2.grid_size[0], y2_min + item2.grid_size[1]

        # Check for horizontal adjacency
        x_adjacent = (x1_max == x2_min or x2_max == x1_min) and (y1_min < y2_max and y2_min < y1_max)
        
        # Check for vertical adjacency
        y_adjacent = (y1_max == y2_min or y2_max == y1_min) and (x1_min < x2_max and x2_min < x1_max)

        return x_adjacent or y_adjacent

    def check_for_combinations(self, inventory_manager: InventoryManager) -> Optional[Recipe]:
        """
        Triggered at the start of the shop phase.
        Iterates through all adjacent pairs of items on the grid.
        Cross-references adjacent item names with the loaded list of recipes.
        If a valid combination is found, it returns the Recipe object.
        """
        items = inventory_manager.get_all_items()
        if len(items) < 2:
            return None

        # Create a list of all unique pairs of items
        item_pairs = []
        for i in range(len(items)):
            for j in range(i + 1, len(items)):
                item_pairs.append((items[i], items[j]))

        for item1, item2 in item_pairs:
            if not self._are_items_adjacent(item1, item2):
                continue
            
            # Check if this pair of items is a recipe
            sorted_ingredients = sorted([item1.name, item2.name])

            for recipe in self.recipes:
                if recipe.ingredients == sorted_ingredients:
                    # Basic recipe match, now check for catalyst if needed
                    if not recipe.catalyst:
                        return recipe
                    
                    # Catalyst required, check for it
                    for other_item in items:
                        if other_item not in [item1, item2] and other_item.name == recipe.catalyst:
                            if self._are_items_adjacent(item1, other_item) or self._are_items_adjacent(item2, other_item):
                                return recipe
        
        return None