import unittest
import sqlite3
from unittest.mock import MagicMock

from simulator.core import Player, Item, ItemInstance
from simulator.battle import BattleSimulator

class TestBattleSimulator(unittest.TestCase):

    def setUp(self):
        """Set up mock objects for testing."""
        # Mock DB connection
        self.mock_db = MagicMock()

        # Create basic player objects
        self.player1 = Player(name="<PERSON>", player_class="Fighter")
        self.player2 = Player(name="<PERSON>", player_class="Ranger")

        # Create a simple weapon item definition
        self.sword_data = {
            "id": 1,
            "name": "Wooden Sword",
            "rarity": "Common",
            "cost": 1,
            "item_class": "Weapon",
            "item_type": "Sword",
            "shape": [[1]],
            "description": "A simple sword.",
            "raw_stats": {"damage": 5, "cooldown": 1.0},
            "synergy_triggers": {},
            "synergy_effects": {}
        }
        self.sword_item = Item(self.sword_data)

    def test_simple_battle_winner(self):
        """
        Test a simple battle where one player has a weapon and the other does not.
        """
        # Give player 1 the sword
        sword_instance = ItemInstance(self.sword_item, x=0, y=0)
        self.player1.backpack.place_item(sword_instance)

        # Initialize the simulator
        simulator = BattleSimulator(self.player1, self.player2, self.mock_db)

        # Run the battle
        winner = simulator.run_battle()

        # Assert that player 1 is the winner
        self.assertEqual(winner, self.player1.name)
        self.assertTrue(self.player2.health <= 0)
        self.assertTrue(self.player1.health > 0)

    def test_battle_draw_on_timeout(self):
        """
        Test that a battle with no damage results in a draw after the timeout.
        """
        # No items are given to players
        simulator = BattleSimulator(self.player1, self.player2, self.mock_db)

        # Run the battle
        winner = simulator.run_battle()

        # Assert the result is a Draw because time runs out
        self.assertEqual(winner, "Draw")
        self.assertAlmostEqual(simulator.time, 60.0)

if __name__ == '__main__':
    unittest.main()