import json
import os

def load_reference_sizes():
    """Load the reference item sizes from analysis."""
    if os.path.exists('item_size_lookup.json'):
        with open('item_size_lookup.json', 'r') as f:
            return json.load(f)
    return {}

def get_reference_item_size(item_name, grid_width, grid_height):
    """
    Get the reference size for an item, with fallbacks for items not in lookup.
    """
    reference_sizes = load_reference_sizes()
    
    # Direct lookup first
    if item_name in reference_sizes:
        return reference_sizes[item_name]['width'], reference_sizes[item_name]['height']
    
    # Fallback: estimate based on grid dimensions and known reference cell size
    # From the analysis, we can see that:
    # - 1x1 items (Banana): ~102x102 pixels
    # - 2x1 items (<PERSON>gger): ~48x96 pixels  
    # - 1x2 items (Hero_Sword): ~48x96 pixels
    # - 2x2 items (Axe): ~88x164 pixels
    
    # Calculate base cell size from known items
    base_cell_width = 48  # From Dagger (2x1) and Hero_Sword (1x2)
    base_cell_height = 48  # From Dagger and Hero_Sword
    
    # Estimate size based on grid dimensions
    estimated_width = base_cell_width * grid_width
    estimated_height = base_cell_height * grid_height
    
    return estimated_width, estimated_height

def get_corrected_item_dimensions(item_name, grid_width, grid_height, original_image_size):
    """
    Get the corrected dimensions for an item based on reference analysis.
    """
    target_width, target_height = get_reference_item_size(item_name, grid_width, grid_height)
    original_width, original_height = original_image_size
    
    # Calculate scale factors for both dimensions
    scale_x = target_width / original_width
    scale_y = target_height / original_height
    
    # Use the smaller scale to maintain aspect ratio
    scale_factor = min(scale_x, scale_y)
    
    # Calculate actual scaled dimensions (maintaining aspect ratio)
    scaled_width = int(original_image_size[0] * scale_factor)
    scaled_height = int(original_image_size[1] * scale_factor)
    
    return scaled_width, scaled_height, scale_factor
