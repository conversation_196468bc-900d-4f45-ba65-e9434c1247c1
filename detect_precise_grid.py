from PIL import Image
import os
import numpy as np

def detect_grid_coordinates():
    """
    Analyze the extracted inventory images to detect precise grid coordinates.
    """
    print("Detecting precise grid coordinates from extracted images...")
    
    # Load one of the inventory debug images
    inventory_img = Image.open("debug_screenshot_1_inventory.png")
    inv_array = np.array(inventory_img)
    
    print(f"Inventory area size: {inventory_img.size}")
    
    # The inventory should be 9 columns x 7 rows
    # Let's try to detect the actual grid by looking for visual patterns
    
    # Convert to grayscale for easier analysis
    gray_img = inventory_img.convert('L')
    gray_array = np.array(gray_img)
    
    height, width = gray_array.shape
    print(f"Inventory dimensions: {width} x {height}")
    
    # Look for repeating patterns that indicate grid structure
    # Method 1: Look for vertical lines (column separators)
    print("\nAnalyzing for vertical grid lines...")
    
    # Sum pixel intensities along each column
    col_sums = np.sum(gray_array, axis=0)
    
    # Look for local minima that might indicate grid lines
    # Grid lines would typically be darker
    col_diffs = np.diff(col_sums)
    
    # Method 2: Look for horizontal lines (row separators)
    print("Analyzing for horizontal grid lines...")
    
    # Sum pixel intensities along each row
    row_sums = np.sum(gray_array, axis=1)
    row_diffs = np.diff(row_sums)
    
    # Method 3: Manual grid detection based on expected 9x7 layout
    print("\nCalculating expected grid coordinates...")
    
    # Based on visual inspection, let's try to find the actual grid
    # The grid might not start exactly at (0,0) in the inventory area
    
    # Look for the actual content area within the inventory ROI
    # This requires examining the actual pixel patterns
    
    # For now, let's create a more precise grid based on visual analysis
    # These coordinates would need to be refined based on actual inspection
    
    # Estimated grid parameters (to be refined)
    grid_start_x = 10  # Offset from left edge of inventory area
    grid_start_y = 10  # Offset from top edge of inventory area
    grid_end_x = width - 10  # Offset from right edge
    grid_end_y = height - 10  # Offset from bottom edge
    
    actual_grid_width = grid_end_x - grid_start_x
    actual_grid_height = grid_end_y - grid_start_y
    
    cell_width = actual_grid_width / 9
    cell_height = actual_grid_height / 7
    
    print(f"Estimated actual grid area: {grid_start_x}, {grid_start_y} to {grid_end_x}, {grid_end_y}")
    print(f"Estimated cell size: {cell_width:.2f} x {cell_height:.2f}")
    
    # Generate precise grid coordinates
    grid_coords = []
    for row in range(7):
        for col in range(9):
            x1 = grid_start_x + col * cell_width
            y1 = grid_start_y + row * cell_height
            x2 = x1 + cell_width
            y2 = y1 + cell_height
            
            grid_coords.append({
                'row': row,
                'col': col,
                'x1': int(x1),
                'y1': int(y1),
                'x2': int(x2),
                'y2': int(y2),
                'center_x': int(x1 + cell_width/2),
                'center_y': int(y1 + cell_height/2)
            })
    
    # Save grid coordinates for use in the synthetic dataset generator
    print(f"\nGenerated {len(grid_coords)} grid cell coordinates")
    
    # Extract a few cells using the new coordinates for verification
    print("Extracting cells using detected coordinates...")
    for i, coord in enumerate(grid_coords[:9]):  # First row
        cell_img = inventory_img.crop((coord['x1'], coord['y1'], coord['x2'], coord['y2']))
        cell_img.save(f"debug_precise_cell_{coord['row']}_{coord['col']}.png")
    
    # Create a visual grid overlay to verify accuracy
    print("Creating grid overlay for verification...")
    overlay_img = inventory_img.copy()
    
    # This would require PIL drawing capabilities to draw grid lines
    # For now, just save the coordinates
    
    return grid_coords

def save_grid_coordinates(grid_coords):
    """Save the detected grid coordinates to a file for use in the dataset generator."""
    
    # Convert to format suitable for the synthetic dataset generator
    # Calculate the absolute coordinates (relative to full screenshot)
    inventory_roi = (114, 80, 1129, 833)
    roi_x, roi_y = inventory_roi[0], inventory_roi[1]
    
    absolute_coords = []
    for coord in grid_coords:
        abs_coord = {
            'row': coord['row'],
            'col': coord['col'],
            'x1': coord['x1'] + roi_x,
            'y1': coord['y1'] + roi_y,
            'x2': coord['x2'] + roi_x,
            'y2': coord['y2'] + roi_y,
            'center_x': coord['center_x'] + roi_x,
            'center_y': coord['center_y'] + roi_y,
            'width': coord['x2'] - coord['x1'],
            'height': coord['y2'] - coord['y1']
        }
        absolute_coords.append(abs_coord)
    
    # Save to a Python file that can be imported
    with open('grid_coordinates.py', 'w') as f:
        f.write("# Precise grid coordinates detected from actual game screenshots\n")
        f.write("# Each coordinate represents one grid cell in the 9x7 inventory\n\n")
        f.write("INVENTORY_GRID_COORDS = [\n")
        for coord in absolute_coords:
            f.write(f"    {coord},\n")
        f.write("]\n\n")
        f.write("def get_grid_cell(row, col):\n")
        f.write("    \"\"\"Get coordinates for a specific grid cell (0-indexed).\"\"\"\n")
        f.write("    for coord in INVENTORY_GRID_COORDS:\n")
        f.write("        if coord['row'] == row and coord['col'] == col:\n")
        f.write("            return coord\n")
        f.write("    return None\n")
    
    print("Saved grid coordinates to grid_coordinates.py")

if __name__ == "__main__":
    grid_coords = detect_grid_coordinates()
    save_grid_coordinates(grid_coords)
