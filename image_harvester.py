import os
import logging
import requests
import re
import time
from bs4 import BeautifulSoup
# Set up basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

WIKI_ITEMS_URL = 'https://backpackbattles.wiki.gg/wiki/Items'
IMAGE_DIR = 'data/item_images/'

def harvest_images():
    """
    Goes to the Backpack Battles wiki, finds all item pages, navigates to each,
    and downloads the full-resolution item image.
    It can be resumed if interrupted and will retry on rate-limiting.
    """
    logging.info("Starting image harvesting process.")

    # 1. Get a list of already downloaded images to avoid re-downloading
    try:
        downloaded_filenames = {os.path.splitext(f)[0] for f in os.listdir(IMAGE_DIR)}
        logging.info(f"Found {len(downloaded_filenames)} already downloaded images.")
    except FileNotFoundError:
        downloaded_filenames = set()
        logging.info("Image directory does not exist yet. Starting fresh.")

    base_url = 'https://backpackbattles.wiki.gg'
    headers = {
        'User-Agent': 'BackpackBattles-AI-Agent/1.0 (https://github.com/your-repo; <EMAIL>)'
    }

    # 2. Fetch and parse the main items page to get all unique item URLs
    try:
        logging.info(f"Fetching item list from {WIKI_ITEMS_URL}")
        response = requests.get(WIKI_ITEMS_URL, headers=headers, timeout=15)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')

        item_link_tags = soup.select('div.hlist a[href^="/wiki/"]')
        item_urls = set()
        for tag in item_link_tags:
            href = tag.get('href')
            if href and not any(x in href for x in ['Template:', 'Category:', 'Recipes', 'Game_Mechanics']):
                if not tag.find('img'):
                    item_urls.add(base_url + href)

        logging.info(f"Found {len(item_urls)} unique item pages on the wiki.")

    except requests.exceptions.RequestException as e:
        logging.error(f"Failed to fetch the main items page: {e}")
        return

    # 3. Loop through URLs, find the direct image link, and download it
    for url in item_urls:
        item_name = url.split('/')[-1]
        sanitized_name = re.sub(r'[^a-zA-Z0-9_-]+', '', item_name)

        # Skip if already downloaded
        if sanitized_name in downloaded_filenames:
            logging.debug(f"Skipping '{item_name}' as it is already downloaded.")
            continue

        logging.info(f"Processing new item: {item_name}")
        
        # Retry logic for downloading a single item
        download_successful = False
        while not download_successful:
            try:
                # Fetch the item's wiki page
                item_page_response = requests.get(url, headers=headers, timeout=10)
                item_page_response.raise_for_status()
                item_soup = BeautifulSoup(item_page_response.text, 'html.parser')

                # Primary strategy: Look for the main item image link in the infobox
                file_page_link = item_soup.select_one('a.image[href*="/File:"]')
                if not file_page_link:
                    # Fallback strategy: Look for any link pointing to a file page that contains the item name
                    # This can help with items that don't use the standard infobox.
                    file_page_link = item_soup.find('a', href=re.compile(f"/File:.*{re.escape(item_name.replace('_', ' '))}", re.IGNORECASE))

                if not file_page_link or not file_page_link.has_attr('href'):
                    logging.warning(f"Could not find file page link for {item_name} on {url}. Structure might be different.")
                    break

                file_page_url = base_url + file_page_link['href']

                # Fetch the "File:" page to find the full resolution image link
                file_page_response = requests.get(file_page_url, headers=headers, timeout=10)
                file_page_response.raise_for_status()
                file_soup = BeautifulSoup(file_page_response.text, 'html.parser')
                
                # The most direct link to the high-res image is usually in `div.fullImageLink a`
                full_image_link = file_soup.select_one('div.fullImageLink a')
                if not full_image_link or not full_image_link.has_attr('href'):
                    logging.warning(f"Could not find full image link for {item_name} on {file_page_url}. Trying direct asset link.")
                    # If that fails, the direct image URL might be available in an `<img>` tag with the original filename
                    full_image_link = file_soup.find('img', {'alt': file_page_link.get('title')})

                if not full_image_link or not (full_image_link.has_attr('href') or full_image_link.has_attr('src')):
                     logging.error(f"Failed to find any valid image source for '{item_name}' on page {file_page_url}.")
                     break

                # Determine if the link is from an 'a' tag (href) or 'img' tag (src)
                image_url = full_image_link.get('href') or full_image_link.get('src')
                if not image_url.startswith('http'):
                    image_url = base_url + image_url
                logging.info(f"Downloading image for {item_name} from {image_url}")

                image_response = requests.get(image_url, headers=headers, timeout=10)
                image_response.raise_for_status()

                filename = os.path.join(IMAGE_DIR, f"{sanitized_name}.png")

                with open(filename, 'wb') as f:
                    f.write(image_response.content)
                
                logging.info(f"Successfully saved '{item_name}' to '{filename}'")
                download_successful = True # Exit the while loop

            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 429: # Rate limited
                    logging.warning(f"Rate limited for {item_name}. Waiting 10 seconds before retry.")
                    time.sleep(10)
                else: # Other HTTP error
                    logging.error(f"HTTP error for {item_name}: {e}")
                    break # Exit while loop
            except requests.exceptions.RequestException as e:
                logging.error(f"Failed to download image for {item_name}: {e}")
                break # Exit while loop
            except Exception as e:
                logging.error(f"An unexpected error occurred for {item_name}: {e}")
                break # Exit while loop
        
        # Be a good web-scraping citizen by adding a delay between different items
        time.sleep(1)

if __name__ == "__main__":
    # Create the directory for images if it doesn't already exist
    try:
        os.makedirs(IMAGE_DIR, exist_ok=True)
        logging.info(f"Directory '{IMAGE_DIR}' created or already exists.")
    except OSError as e:
        logging.error(f"Error creating directory {IMAGE_DIR}: {e}")

    harvest_images()