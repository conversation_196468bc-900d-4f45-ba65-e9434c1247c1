from ultralytics import YOLO

class NewItemRecognizer:
    def __init__(self, model_path='models/best.pt'):
        self.model = YOLO(model_path)

    def find_all_items(self, screenshot_image):
        results = self.model(screenshot_image)
        detections = []
        for result in results:
            for box in result.boxes:
                x1, y1, x2, y2 = box.xyxy[0]
                detections.append({
                    'item_name': self.model.names[int(box.cls)],
                    'position': (int(x1), int(y1), int(x2 - x1), int(y2 - y1)),
                    'confidence': float(box.conf)
                })
        return detections