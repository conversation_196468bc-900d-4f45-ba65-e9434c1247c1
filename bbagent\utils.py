import json
from typing import Dict, Any
from .config import ITEM_DATA_PATH, RECIPE_DATA_PATH

def load_item_data() -> Dict[str, Any]:
    """
    Loads the item and recipe data from the main JSON file.
    """
    try:
        with open(ITEM_DATA_PATH, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: The item data file was not found at {ITEM_DATA_PATH}")
        return {}
    except json.JSONDecodeError:
        print(f"Error: Could not decode the JSON from {ITEM_DATA_PATH}")
        return {}
def load_recipe_data() -> Dict[str, Any]:
    """
    Loads the recipe data from the JSON file.
    """
    try:
        with open(RECIPE_DATA_PATH, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: The recipe data file was not found at {RECIPE_DATA_PATH}")
        raise
    except json.JSONDecodeError:
        print(f"Error: Could not decode the JSON from {RECIPE_DATA_PATH}")
        return {}