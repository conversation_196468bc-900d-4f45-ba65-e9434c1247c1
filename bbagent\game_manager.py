from time import sleep
from .inventory_manager import Inventory<PERSON>anager
from .recipe_manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .utils import load_item_data, load_recipe_data
from .item import Item

class GameManager:
    """
    The central orchestrator of the agent. It manages the game state,
    coordinates the different managers, and executes the main game loop.
    """
    def __init__(self):
        """Initializes the GameManager, loading all necessary resources."""
        self.item_data = load_item_data()
        recipes_data = load_recipe_data().get('recipes', [])
        if not recipes_data:
            raise ValueError("No recipes found in data/recipes.json or file is empty.")
        self.inventory_manager = InventoryManager()
        self.recipe_manager = RecipeManager(recipes_data)
        # It's better to manage the inventory's items here, not in the inventory_manager directly.
        self.current_inventory_items = []
        self.game_state = 'shop'  # Initial state can be 'shop', 'battle', or 'post_battle'

    def update_inventory_state(self, new_items_data: list):
        """
        Updates the inventory with a new set of items, usually from the vision system.
        This clears the old inventory and rebuilds it.
        """
        self.inventory_manager.clear()
        self.current_inventory_items = []
        for item_info in new_items_data:
            item_name = item_info.get("name")
            item_details = self.item_data.get("items", {}).get(item_name)
            if item_details:
                item = Item(
                    name=item_name,
                    grid_layout=item_details.get("grid_layout", []),
                    grid_size=item_details.get("grid_size", (0, 0)),
                    item_type=item_details.get("type", "other")
                )
                placement = self.inventory_manager.find_placement_with_backtracking(item)
                if placement:
                    pos, angle = placement
                    item.current_rotation = angle
                    self.inventory_manager.add_item_to_grid(item, pos)
                    self.current_inventory_items.append(item)
                else:
                    print(f"Could not find placement for {item.name}")

    def run(self):
        """The main game loop that represents one cycle of the agent's logic."""
        print("Game Manager is running...")
        while True:
            if self.game_state == 'shop':
                self.handle_shop_phase()
            elif self.game_state == 'battle':
                self.handle_battle_phase()
            
            # A sleep to prevent a busy loop. In a real scenario, this would
            # be event-driven or tied to the game's actual update rate.
            sleep(5)  # Placeholder delay

    def handle_shop_phase(self):
        """
        Manages the agent's actions during the shop phase.
        - Check for combinations.
        - If combination found, execute it.
        - Placeholder for other actions (buy, sell, etc.).
        """
        print("Handling shop phase...")
        
        # 1. Check for potential item combinations
        recipe_to_craft = self.recipe_manager.check_for_combinations(self.inventory_manager)

        if recipe_to_craft:
            print(f"Found a valid recipe: {recipe_to_craft.result}")
            # 2. Perform the combination
            self.execute_combination(recipe_to_craft)
        else:
            # 3. Placeholder for other logic
            print("No new combinations found. Considering other actions...")
            # Here you would add logic for:
            # - Evaluating shop items
            # - Deciding whether to buy an item
            # - Optimizing inventory layout
            # - Ending the turn
            pass

    def execute_combination(self, recipe: 'Recipe'):
        """
        Removes ingredient items and adds the resulting item to the inventory.
        """
        print(f"Executing combination for: {recipe.result}")
        ingredients_to_remove = []
        
        # Find the item instances corresponding to the recipe ingredients
        for needed_ingredient in recipe.ingredients:
            for item in self.current_inventory_items:
                if item.name == needed_ingredient:
                    ingredients_to_remove.append(item)
                    self.current_inventory_items.remove(item)
                    break
        
        # Remove the ingredients from the inventory grid
        for item in ingredients_to_remove:
            self.inventory_manager.remove_item_from_grid(item)
            
        print(f"Removed: {[item.name for item in ingredients_to_remove]}")

        # Create and add the new item
        new_item_details = self.item_data.get("items", {}).get(recipe.result)
        if new_item_details:
            new_item = Item(
                name=recipe.result,
                grid_layout=new_item_details.get("grid_layout", []),
                grid_size=new_item_details.get("grid_size", (0, 0)),
                item_type=new_item_details.get("type", "other")
            )
            
            placement = self.inventory_manager.find_placement_with_backtracking(new_item)
            if placement:
                pos, angle = placement
                new_item.current_rotation = angle
                self.inventory_manager.add_item_to_grid(new_item, pos)
                self.current_inventory_items.append(new_item)
                print(f"Added: {new_item.name} to inventory.")
            else:
                print(f"Failed to place {new_item.name} in inventory. Inventory might be full.")
        else:
            print(f"Could not find item details for {recipe.result}")

    def handle_battle_phase(self):
        """Monitors the battle and prepares for the next phase."""
        print("Monitoring battle phase... (Not implemented)")
        # In the future, this will monitor the game state for the end of combat.
        pass