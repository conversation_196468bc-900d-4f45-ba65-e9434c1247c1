<!DOCTYPE html>
<html class="client-nojs view-unknown theme-none skin-theme-clientpref-day" dir="ltr" lang="en">
 <head>
  <meta charset="utf-8"/>
  <title>
   Items - The Backpack Battles Wiki
  </title>
  <script>
   (function(){var className="client-js view-unknown theme-none skin-theme-clientpref-day";var cookie=document.cookie.match(/(?:^|; )commons_enmwclientpreferences=([^;]+)/);if(cookie){cookie[1].split('%2C').forEach(function(pref){className=className.replace(new RegExp('(^| )'+pref.replace(/-clientpref-\w+$|[^\w-]+/g,'')+'-clientpref-\\w+( |$)'),'$1'+pref+'$2');});}document.documentElement.className=className;}());RLCONF={"wgBreakFrames":false,"wgSeparatorTransformTable":["",""],"wgDigitTransformTable":["",""],"wgDefaultDateFormat":"dmy","wgMonthNames":["","January","February","March","April","May","June","July","August","September","October","November","December"],"wgRequestId":"7ebfff880d5efaf33713b2bb","wgCanonicalNamespace":"","wgCanonicalSpecialPageName":false,"wgNamespaceNumber":0,"wgPageName":"Items","wgTitle":"Items","wgCurRevisionId":10081,"wgRevisionId":10081,"wgArticleId":315,"wgIsArticle":true,"wgIsRedirect":false,"wgAction":"view","wgUserName":null,"wgUserGroups":["*"],
"wgCategories":["Items"],"wgPageViewLanguage":"en","wgPageContentLanguage":"en","wgPageContentModel":"wikitext","wgRelevantPageName":"Items","wgRelevantArticleId":315,"wgIsProbablyEditable":false,"wgRelevantPageIsProbablyEditable":false,"wgRestrictionEdit":[],"wgRestrictionMove":[],"wgPageFormsTargetName":null,"wgPageFormsAutocompleteValues":[],"wgPageFormsAutocompleteOnAllChars":false,"wgPageFormsFieldProperties":[],"wgPageFormsCargoFields":[],"wgPageFormsDependentFields":[],"wgPageFormsCalendarValues":[],"wgPageFormsCalendarParams":[],"wgPageFormsCalendarHTML":null,"wgPageFormsGridValues":[],"wgPageFormsGridParams":[],"wgPageFormsContLangYes":null,"wgPageFormsContLangNo":null,"wgPageFormsContLangMonths":[],"wgPageFormsHeightForMinimizingInstances":800,"wgPageFormsDelayReload":false,"wgPageFormsShowOnSelect":[],"wgPageFormsScriptPath":"/mw-1.43/extensions/PageForms","edgValues":[],"wgPageFormsEDSettings":null,"wgAmericanDates":false,"wgCargoDefaultQueryLimit":2000,
"wgCargoMapClusteringMinimum":80,"wgCargoMonthNamesShort":["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],"egAppHost":"https://app.wiki.gg","wgMediaViewerOnClick":true,"wgMediaViewerEnabledByDefault":true,"wgCiteReferencePreviewsActive":true,"wgCheckUserClientHintsHeadersJsApi":["architecture","bitness","brands","fullVersionList","mobile","model","platform","platformVersion"]};RLSTATE={"ext.globalCssJs.user.styles":"ready","site.styles":"ready","user.styles":"ready","ext.globalCssJs.user":"ready","user":"ready","user.options":"loading","skins.vector.styles.legacy":"ready","jquery.makeCollapsible.styles":"ready","ext.CookieWarning.styles":"ready","oojs-ui-core.styles":"ready","oojs-ui.styles.indicators":"ready","mediawiki.widgets.styles":"ready","oojs-ui-core.icons":"ready","ext.removeredlinks.styles":"ready","ext.usergroupbadges.styles":"ready","ext.embedVideo.styles":"ready","ext.globalui.styles":"ready"};RLPAGEMODULES=["ext.cargo.main","site",
"mediawiki.page.ready","jquery.makeCollapsible","mediawiki.toc","skins.vector.legacy.js","ext.CookieWarning","ext.removeredlinks","ext.themes.switcher","ext.gadget.interwikiDropdownButton","mmv.bootstrap","ext.checkUser.clientHints","ext.embedVideo.overlay","ext.cargo.purge","ext.globalui"];
  </script>
  <script>
   (RLQ=window.RLQ||[]).push(function(){mw.loader.impl(function(){return["user.options@12s5i",function($,jQuery,require,module){mw.user.tokens.set({"patrolToken":"+\\","watchToken":"+\\","csrfToken":"+\\"});
}];});});
  </script>
  <link href="/load.php?lang=en&amp;modules=ext.CookieWarning.styles%7Cext.embedVideo.styles%7Cext.globalui.styles%7Cext.removeredlinks.styles%7Cext.usergroupbadges.styles%7Cjquery.makeCollapsible.styles%7Cmediawiki.widgets.styles%7Coojs-ui-core.icons%2Cstyles%7Coojs-ui.styles.indicators%7Cskins.vector.styles.legacy&amp;only=styles&amp;skin=vector" rel="stylesheet"/>
  <script async="" src="/load.php?lang=en&amp;modules=startup&amp;only=scripts&amp;raw=1&amp;skin=vector">
  </script>
  <meta content="" name="ResourceLoaderDynamicStyles"/>
  <link href="/load.php?lang=en&amp;modules=site.styles&amp;only=styles&amp;skin=vector" rel="stylesheet"/>
  <script async="" src="/load.php?lang=en&amp;skin=vector&amp;modules=ext.themes.apply&amp;only=scripts&amp;skin=vector&amp;raw=1">
  </script>
  <meta content="MediaWiki 1.43.0" name="generator"/>
  <meta content="max-image-preview:standard" name="robots"/>
  <meta content="telephone=no" name="format-detection"/>
  <meta content="Items can be placed in the inventory where they will be brought into battle, or kept in the storage where they will not have any effect.
Each shop item has a 10% chance to go on sale at half of the cost rounded up. The player may sell items at half price by dragging them into the chest. Buying an item..." name="description"/>
  <meta content="width=device-width, initial-scale=1.0, user-scalable=yes, minimum-scale=0.25, maximum-scale=5.0" name="viewport"/>
  <style type="text/css">
   @font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/cyrillic/wght/normal.woff2);unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/latin-ext/wght/normal.woff2);unicode-range:U+0100-02AF,U+0304,U+0308,U+0329,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/vietnamese/wght/normal.woff2);unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/latin/wght/normal.woff2);unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;font-display:swap;}@font-face {font-family:Nunito;font-style:normal;font-weight:400;src:url(/cf-fonts/v/nunito/5.0.16/cyrillic-ext/wght/normal.woff2);unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;font-display:swap;}
  </style>
  <link href="/images/4/4a/Site-favicon.ico" rel="apple-touch-icon"/>
  <link href="/images/4/4a/Site-favicon.ico" rel="icon"/>
  <link href="/rest.php/v1/search" rel="search" title="Backpack Battles Wiki (en)" type="application/opensearchdescription+xml"/>
  <link href="https://backpackbattles.wiki.gg/api.php?action=rsd" rel="EditURI" type="application/rsd+xml"/>
  <link href="https://backpackbattles.wiki.gg/wiki/Items" rel="canonical"/>
  <link href="https://creativecommons.org/licenses/by-sa/4.0" rel="license"/>
  <link href="/wiki/Special:RecentChanges?feed=atom" rel="alternate" title="Backpack Battles Wiki Atom feed" type="application/atom+xml"/>
  <meta content="1025655121420775" prefix="fb: http://www.facebook.com/2008/fbml" property="fb:app_id"/>
  <meta content="article" property="og:type"/>
  <meta content="Backpack Battles Wiki" property="og:site_name"/>
  <meta content="Items" property="og:title"/>
  <meta content="Items can be placed in the inventory where they will be brought into battle, or kept in the storage where they will not have any effect.
Each shop item has a 10% chance to go on sale at half of the cost rounded up. The player may sell items at half price by dragging them into the chest. Buying an item..." property="og:description"/>
  <meta content="https://backpackbattles.wiki.gg/wiki/Items" property="og:url"/>
  <!-- Google Tag Manager -->
  <script>
   (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-P68LVFKN');
  </script>
  <!-- End Google Tag Manager -->
 </head>
 <body class="wgg-dom-version-1_43 skin-vector-legacy mediawiki ltr sitedir-ltr mw-hide-empty-elt ns-0 ns-subject page-Items rootpage-Items skin-vector action-view skin--responsive">
  <header id="wgg-netbar">
   <div class="wgg-netbar__left">
    <div class="wgg-netbar__logo">
     <a aria-label="wiki.gg" href="/">
      <img alt="wiki.gg logo" height="25" src="https://commons.wiki.gg/images/2/27/Network_header_logo.svg"/>
     </a>
    </div>
   </div>
   <nav aria-label="Personal tools" class="wgg-netbar__user-links">
    <div class="mw-portlet mw-portlet-personal" id="p-personal">
     <ul>
      <li class="mw-list-item wgg-netbar__desktop-only" id="pt-createaccount">
       <a class="" data-mw="interface" href="/wiki/Special:CreateAccount?returnto=Items" title="You are encouraged to create an account and log in; however, it is not mandatory">
        <span>
         Create account
        </span>
       </a>
      </li>
      <li class="mw-list-item wgg-netbar__desktop-only" id="pt-login">
       <a accesskey="o" class="" data-mw="interface" href="/wiki/Special:UserLogin?returnto=Items" title="You are encouraged to log in; however, it is not mandatory [o]">
        <span>
         Log in
        </span>
       </a>
      </li>
     </ul>
    </div>
    <div class="wgg-netbar__dropdown wgg-netbar__mobile-only" id="wgg-user-menu-overflow">
     <input aria-haspopup="true" aria-labelledby="wgg-user-menu-overflow-label" id="wgg-user-menu-overflow-checkbox" role="button" type="checkbox"/>
     <label class="wgg-netbar__icon-button" for="wgg-user-menu-overflow-checkbox" id="wgg-user-menu-overflow-label">
      <span class="wgg-netbar-icon wgg-netbar-icon--overflow">
      </span>
      <span>
       Toggle personal tools menu
      </span>
     </label>
     <div class="wgg-netbar__dropdown-content">
      <div class="mw-portlet mw-portlet-user-menu" id="p-user-menu">
       <ul>
        <li class="mw-list-item wgg-netbar__mobile-only" id="pt-createaccount-overflow">
         <a class="" data-mw="interface" href="/wiki/Special:CreateAccount?returnto=Items" title="You are encouraged to create an account and log in; however, it is not mandatory">
          <span>
           Create account
          </span>
         </a>
        </li>
        <li class="mw-list-item wgg-netbar__mobile-only" id="pt-login-overflow">
         <a accesskey="o" class="" data-mw="interface" href="/wiki/Special:UserLogin?returnto=Items" title="You are encouraged to log in; however, it is not mandatory [o]">
          <span>
           Log in
          </span>
         </a>
        </li>
       </ul>
      </div>
     </div>
    </div>
   </nav>
  </header>
  <div class="noprint" id="mw-page-base">
  </div>
  <div class="noprint" id="mw-head-base">
  </div>
  <div class="content-wrapper">
   <div id="mw-navigation">
    <h2>
     Navigation menu
    </h2>
    <div id="mw-head">
     <div id="left-navigation">
      <div aria-labelledby="p-namespaces-label" class="mw-portlet mw-portlet-namespaces vector-menu-tabs vector-menu-tabs-legacy vectorTabs vector-menu" id="p-namespaces">
       <h3 class="vector-menu-heading" id="p-namespaces-label">
        <span class="vector-menu-heading-label">
         Namespaces
        </span>
       </h3>
       <div class="vector-menu-content body">
        <ul class="vector-menu-content-list menu">
         <li class="selected mw-list-item" id="ca-nstab-main">
          <a accesskey="c" href="/wiki/Items" title="View the content page [c]">
           <span>
            Page
           </span>
          </a>
         </li>
         <li class="new mw-list-item" id="ca-talk">
          <a accesskey="t" class="new" href="/wiki/Talk:Items?action=edit&amp;redlink=1" rel="discussion" title="Discussion about the content page (page does not exist) [t]">
           <span>
            Discussion
           </span>
          </a>
         </li>
        </ul>
       </div>
      </div>
      <div aria-labelledby="p-variants-label" class="mw-portlet mw-portlet-variants emptyPortlet vector-menu-dropdown vector-menu" id="p-variants">
       <input aria-haspopup="true" aria-labelledby="p-variants-label" class="vector-menu-checkbox" data-event-name="ui.dropdown-p-variants" id="p-variants-checkbox" role="button" type="checkbox"/>
       <label class="vector-menu-heading" id="p-variants-label">
        <span class="vector-menu-heading-label">
         English
        </span>
       </label>
       <div class="vector-menu-content body">
        <ul class="vector-menu-content-list menu">
        </ul>
       </div>
      </div>
     </div>
     <div id="right-navigation">
      <div aria-labelledby="p-views-label" class="mw-portlet mw-portlet-views vector-menu-tabs vector-menu-tabs-legacy vectorTabs vector-menu" id="p-views">
       <h3 class="vector-menu-heading" id="p-views-label">
        <span class="vector-menu-heading-label">
         Views
        </span>
       </h3>
       <div class="vector-menu-content body">
        <ul class="vector-menu-content-list menu">
         <li class="selected mw-list-item" id="ca-view">
          <a href="/wiki/Items">
           <span>
            Read
           </span>
          </a>
         </li>
         <li class="mw-list-item" id="ca-edit">
          <a accesskey="e" href="/wiki/Special:CreateAccount?warning=accountrequiredtoedit&amp;returnto=Items&amp;returntoquery=action%3Dedit" title="Edit this page [e]">
           <span>
            Sign up to edit
           </span>
          </a>
         </li>
         <li class="mw-list-item" id="ca-viewsource">
          <a accesskey="e" href="/wiki/Items?action=edit" title="This page is protected.
You can view its source [e]">
           <span>
            View source
           </span>
          </a>
         </li>
         <li class="mw-list-item" id="ca-history">
          <a accesskey="h" href="/wiki/Items?action=history" title="Past revisions of this page [h]">
           <span>
            History
           </span>
          </a>
         </li>
        </ul>
       </div>
      </div>
      <div aria-labelledby="p-cactions-label" class="mw-portlet mw-portlet-cactions vectorMenu vector-menu-dropdown vector-menu" id="p-cactions" title="More options">
       <input aria-haspopup="true" aria-labelledby="p-cactions-label" class="vector-menu-checkbox" data-event-name="ui.dropdown-p-cactions" id="p-cactions-checkbox" role="button" type="checkbox"/>
       <label class="vector-menu-heading" id="p-cactions-label">
        <span class="vector-menu-heading-label">
         More
        </span>
       </label>
       <div class="vector-menu-content body">
        <ul class="vector-menu-content-list menu">
         <li class="mw-list-item" id="ca-cargo-purge">
          <a href="/wiki/Items?action=purge">
           <span>
            Purge cache
           </span>
          </a>
         </li>
        </ul>
       </div>
      </div>
      <div class="vector-search-box-vue vector-search-box-show-thumbnail vector-search-box-auto-expand-width vector-search-box" id="p-search" role="search">
       <h3>
        Search
       </h3>
       <form action="/wiki/Special:Search" class="vector-search-box-form" id="searchform">
        <div class="vector-search-box-inner" data-search-loc="header-navigation" id="simpleSearch">
         <input accesskey="f" aria-label="Search Backpack Battles Wiki" autocapitalize="sentences" class="vector-search-box-input" id="searchInput" name="search" placeholder="Search Backpack Battles Wiki" title="Search Backpack Battles Wiki [f]" type="search"/>
         <input class="searchButton mw-fallbackSearchButton" id="mw-searchButton" name="fulltext" title="Search the pages for this text" type="submit" value="Search"/>
         <input class="searchButton" id="searchButton" name="go" title="Go to a page with this exact name if it exists" type="submit" value="Go"/>
        </div>
       </form>
      </div>
     </div>
    </div>
    <div id="mw-panel">
     <div id="p-logo" role="banner">
      <a class="mw-wiki-logo" href="/" title="Visit the main page">
      </a>
     </div>
     <div aria-labelledby="p-Content-label" class="mw-portlet mw-portlet-Content vector-menu-portal portal vector-menu" id="p-Content">
      <h3 class="vector-menu-heading" id="p-Content-label">
       <span class="vector-menu-heading-label">
        Content
       </span>
      </h3>
      <div class="vector-menu-content body">
       <ul class="vector-menu-content-list menu">
        <li class="mw-list-item" id="n-mainpage-description">
         <a accesskey="z" href="/" title="Visit the main page [z]">
          <span>
           Main page
          </span>
         </a>
        </li>
        <li class="mw-list-item" id="n-Backpack-Battles">
         <a href="/wiki/Backpack_Battles">
          <span>
           Backpack Battles
          </span>
         </a>
        </li>
        <li class="mw-list-item" id="n-Items">
         <a href="/wiki/Items">
          <span>
           Items
          </span>
         </a>
        </li>
        <li class="mw-list-item" id="n-Game-Mechanics">
         <a href="/wiki/Game_Mechanics">
          <span>
           Game Mechanics
          </span>
         </a>
        </li>
        <li class="mw-list-item" id="n-Characters">
         <a href="/wiki/Characters">
          <span>
           Characters
          </span>
         </a>
        </li>
        <li class="mw-list-item" id="n-Version-History">
         <a href="/wiki/Version_History">
          <span>
           Version History
          </span>
         </a>
        </li>
       </ul>
      </div>
     </div>
     <div aria-labelledby="p-navigation-label" class="mw-portlet mw-portlet-navigation vector-menu-portal portal vector-menu" id="p-navigation">
      <h3 class="vector-menu-heading" id="p-navigation-label">
       <span class="vector-menu-heading-label">
        Navigation
       </span>
      </h3>
      <div class="vector-menu-content body">
       <ul class="vector-menu-content-list menu">
        <li class="mw-list-item" id="n-portal">
         <a href="/wiki/Backpack_Battles_Wiki:Community_portal" title="About the project, what you can do, where to find things">
          <span>
           Community portal
          </span>
         </a>
        </li>
        <li class="mw-list-item" id="n-recentchanges">
         <a accesskey="r" href="/wiki/Special:RecentChanges" title="A list of recent changes in the wiki [r]">
          <span>
           Recent changes
          </span>
         </a>
        </li>
        <li class="mw-list-item" id="n-randompage">
         <a accesskey="x" href="/wiki/Special:Random" title="Load a random page [x]">
          <span>
           Random page
          </span>
         </a>
        </li>
        <li class="mw-list-item" id="n-help-mediawiki">
         <a href="https://www.mediawiki.org/wiki/Special:MyLanguage/Help:Contents">
          <span>
           Help about MediaWiki
          </span>
         </a>
        </li>
       </ul>
      </div>
     </div>
     <div aria-labelledby="p-tb-label" class="mw-portlet mw-portlet-tb vector-menu-portal portal vector-menu" id="p-tb">
      <h3 class="vector-menu-heading" id="p-tb-label">
       <span class="vector-menu-heading-label">
        Tools
       </span>
      </h3>
      <div class="vector-menu-content body">
       <ul class="vector-menu-content-list menu">
        <li class="mw-list-item" id="t-whatlinkshere">
         <a accesskey="j" href="/wiki/Special:WhatLinksHere/Items" title="A list of all wiki pages that link here [j]">
          <span>
           What links here
          </span>
         </a>
        </li>
        <li class="mw-list-item" id="t-recentchangeslinked">
         <a accesskey="k" href="/wiki/Special:RecentChangesLinked/Items" rel="nofollow" title="Recent changes in pages linked from this page [k]">
          <span>
           Related changes
          </span>
         </a>
        </li>
        <li class="mw-list-item" id="t-newpage">
         <a accesskey="]" href="/wiki/Special:NewPage">
          <span>
           New page
          </span>
         </a>
        </li>
        <li class="mw-list-item" id="t-specialpages">
         <a accesskey="q" href="/wiki/Special:SpecialPages" title="A list of all special pages [q]">
          <span>
           Special pages
          </span>
         </a>
        </li>
        <li class="mw-list-item" id="t-print">
         <a accesskey="p" href="javascript:print();" rel="alternate" title="Printable version of this page [p]">
          <span>
           Printable version
          </span>
         </a>
        </li>
        <li class="mw-list-item" id="t-permalink">
         <a href="/wiki/Items?oldid=10081" title="Permanent link to this revision of this page">
          <span>
           Permanent link
          </span>
         </a>
        </li>
        <li class="mw-list-item" id="t-info">
         <a href="/wiki/Items?action=info" title="More information about this page">
          <span>
           Page information
          </span>
         </a>
        </li>
        <li class="mw-list-item" id="t-cargopagevalueslink">
         <a href="/wiki/Items?action=pagevalues" rel="cargo-pagevalues">
          <span>
           Cargo data
          </span>
         </a>
        </li>
       </ul>
      </div>
     </div>
    </div>
   </div>
   <div class="mw-body" id="content" role="main">
    <aside data-mw="WggShowcaseLayout" id="wikigg-sl-header">
     <div class="wikigg-showcase__unit wikigg-showcase__reserved--lb" data-wgg-unit-type="internal">
     </div>
    </aside>
    <div class="content-body">
     <main>
      <a id="top">
      </a>
      <div id="siteNotice">
      </div>
      <div class="mw-indicators">
      </div>
      <h1 class="firstHeading mw-first-heading" id="firstHeading">
       <span class="mw-page-title-main">
        Items
       </span>
      </h1>
      <div class="vector-body" id="bodyContent">
       <div class="noprint" id="siteSub">
        From Backpack Battles Wiki
       </div>
       <div id="contentSub">
       </div>
       <div id="contentSub2">
       </div>
       <div id="jump-to-nav">
       </div>
       <a class="mw-jump-link" href="#mw-head">
        Jump to navigation
       </a>
       <a class="mw-jump-link" href="#searchInput">
        Jump to search
       </a>
       <div class="mw-body-content" id="mw-content-text">
        <div class="mw-content-ltr mw-parser-output" dir="ltr" lang="en">
         <p>
          <b>
           Items
          </b>
          can be placed in the inventory where they will be brought into battle, or kept in the storage where they will not have any effect.
         </p>
         <p>
          Each shop item has a 10% chance to go on sale at half of the cost rounded up. The player may sell items at half price by dragging them into the
          <a href="/wiki/Characters#Chestnut" title="Characters">
           chest
          </a>
          . Buying an item on sale and selling it will result in no gold change.
         </p>
         <p>
          Items can be combined using
          <a href="/wiki/Recipe" title="Recipe">
           recipes
          </a>
          .
         </p>
         <div aria-labelledby="mw-toc-heading" class="toc" id="toc" role="navigation">
          <input class="toctogglecheckbox" id="toctogglecheckbox" role="button" style="display:none" type="checkbox">
           <div class="toctitle" dir="ltr" lang="en">
            <h2 id="mw-toc-heading">
             Contents
            </h2>
            <span class="toctogglespan">
             <label class="toctogglelabel" for="toctogglecheckbox">
             </label>
            </span>
           </div>
           <ul>
            <li class="toclevel-1 tocsection-1">
             <a href="#Class_Items">
              <span class="tocnumber">
               1
              </span>
              <span class="toctext">
               Class Items
              </span>
             </a>
            </li>
            <li class="toclevel-1 tocsection-2">
             <a href="#Types">
              <span class="tocnumber">
               2
              </span>
              <span class="toctext">
               Types
              </span>
             </a>
             <ul>
              <li class="toclevel-2 tocsection-3">
               <a href="#Extra_Types">
                <span class="tocnumber">
                 2.1
                </span>
                <span class="toctext">
                 Extra Types
                </span>
               </a>
              </li>
             </ul>
            </li>
            <li class="toclevel-1 tocsection-4">
             <a href="#Rarities">
              <span class="tocnumber">
               3
              </span>
              <span class="toctext">
               Rarities
              </span>
             </a>
            </li>
           </ul>
          </input>
         </div>
         <h2>
          <span class="mw-headline" id="Class_Items">
           Class Items
          </span>
         </h2>
         <p>
          <a href="/wiki/Neutral_items" title="Neutral items">
           <img alt="Neutral items" decoding="async" height="132" loading="lazy" src="/images/thumb/3/37/NeutralItems.png/150px-NeutralItems.png" width="150"/>
          </a>
          <a href="/wiki/Ranger_items" title="Ranger items">
           <img alt="Ranger items" decoding="async" height="132" loading="lazy" src="/images/thumb/a/a7/RangerItems.png/150px-RangerItems.png" width="150"/>
          </a>
          <a href="/wiki/Reaper_items" title="Reaper items">
           <img alt="Reaper items" decoding="async" height="132" loading="lazy" src="/images/thumb/d/df/ReaperItems.png/150px-ReaperItems.png" width="150"/>
          </a>
          <a href="/wiki/Berserker_items" title="Berserker items">
           <img alt="Berserker items" decoding="async" height="132" loading="lazy" src="/images/thumb/6/6a/BerserkerItems.png/150px-BerserkerItems.png" width="150"/>
          </a>
          <a href="/wiki/Pyromancer_items" title="Pyromancer items">
           <img alt="Pyromancer items" decoding="async" height="132" loading="lazy" src="/images/thumb/6/6e/PyromancerItems.png/150px-PyromancerItems.png" width="150"/>
          </a>
         </p>
         <h2>
          <span class="mw-headline" id="Types">
           Types
          </span>
         </h2>
         <p>
          <a href="/wiki/Accessory" title="Accessory">
           <img alt="Accessory" decoding="async" height="132" loading="lazy" src="/images/thumb/f/f4/Accessory.png/150px-Accessory.png" width="150"/>
          </a>
          <a href="/wiki/Armor" title="Armor">
           <img alt="Armor" decoding="async" height="132" loading="lazy" src="/images/thumb/0/06/Armor.png/150px-Armor.png" width="150"/>
          </a>
          <a href="/wiki/Bag" title="Bag">
           <img alt="Bag" decoding="async" height="132" loading="lazy" src="/images/thumb/d/d4/Bag.png/150px-Bag.png" width="150"/>
          </a>
          <a href="/wiki/Food" title="Food">
           <img alt="Food" decoding="async" height="132" loading="lazy" src="/images/thumb/c/c6/Food.png/150px-Food.png" width="150"/>
          </a>
          <a href="/wiki/Gemstone" title="Gemstone">
           <img alt="Gemstone" decoding="async" height="132" loading="lazy" src="/images/thumb/0/0a/Gemstone.png/150px-Gemstone.png" width="150"/>
          </a>
          <a href="/wiki/Gloves" title="Gloves">
           <img alt="Gloves" decoding="async" height="132" loading="lazy" src="/images/thumb/3/37/Gloves.png/150px-Gloves.png" width="150"/>
          </a>
          <a href="/wiki/Helmet" title="Helmet">
           <img alt="Helmet" decoding="async" height="132" loading="lazy" src="/images/thumb/3/37/Helmet.png/150px-Helmet.png" width="150"/>
          </a>
          <a href="/wiki/Pet" title="Pet">
           <img alt="Pet" decoding="async" height="132" loading="lazy" src="/images/thumb/1/11/Pet.png/150px-Pet.png" width="150"/>
          </a>
          <a href="/wiki/Playing_Card" title="Playing Card">
           <img alt="Playing Card" decoding="async" height="132" loading="lazy" src="/images/thumb/8/81/PlayingCard.png/150px-PlayingCard.png" width="150"/>
          </a>
          <a href="/wiki/Potion" title="Potion">
           <img alt="Potion" decoding="async" height="132" loading="lazy" src="/images/thumb/7/7a/Potion.png/150px-Potion.png" width="150"/>
          </a>
          <a href="/wiki/Shield" title="Shield">
           <img alt="Shield" decoding="async" height="132" loading="lazy" src="/images/thumb/c/cf/Shield.png/150px-Shield.png" width="150"/>
          </a>
          <a href="/wiki/Shoes" title="Shoes">
           <img alt="Shoes" decoding="async" height="132" loading="lazy" src="/images/thumb/8/82/Shoes.png/150px-Shoes.png" width="150"/>
          </a>
          <a href="/wiki/Skill" title="Skill">
           <img alt="Skill" decoding="async" height="132" loading="lazy" src="/images/thumb/7/74/Skill.png/150px-Skill.png" width="150"/>
          </a>
          <a href="/wiki/Spell_Scroll" title="Spell Scroll">
           <img alt="Spell Scroll" decoding="async" height="132" loading="lazy" src="/images/thumb/0/01/Spell_Scroll.png/150px-Spell_Scroll.png" width="150"/>
          </a>
          <a href="/wiki/Weapon" title="Weapon">
           <img alt="Weapon" decoding="async" height="132" loading="lazy" src="/images/thumb/f/fb/Weapon.png/150px-Weapon.png" width="150"/>
          </a>
         </p>
         <h4>
          <span class="mw-headline" id="Extra_Types">
           Extra Types
          </span>
         </h4>
         <p>
          <a href="/wiki/Melee" title="Melee">
           <img alt="Melee" decoding="async" height="132" loading="lazy" src="/images/thumb/8/8f/Melee.png/150px-Melee.png" width="150"/>
          </a>
          <a href="/wiki/Ranged" title="Ranged">
           <img alt="Ranged" decoding="async" height="132" loading="lazy" src="/images/thumb/c/c8/Ranged.png/150px-Ranged.png" width="150"/>
          </a>
          <a href="/wiki/Effect" title="Effect">
           <img alt="Effect" decoding="async" height="132" loading="lazy" src="/images/thumb/3/35/Effect.png/150px-Effect.png" width="150"/>
          </a>
          <a href="/wiki/Nature" title="Nature">
           <img alt="Nature" decoding="async" height="132" loading="lazy" src="/images/thumb/a/a7/Nature.png/150px-Nature.png" width="150"/>
          </a>
          <a href="/wiki/Magic" title="Magic">
           <img alt="Magic" decoding="async" height="132" loading="lazy" src="/images/thumb/f/f7/Magic.png/150px-Magic.png" width="150"/>
          </a>
          <a href="/wiki/Holy" title="Holy">
           <img alt="Holy" decoding="async" height="132" loading="lazy" src="/images/thumb/a/a3/Holy.png/150px-Holy.png" width="150"/>
          </a>
          <a href="/wiki/Dark" title="Dark">
           <img alt="Dark" decoding="async" height="132" loading="lazy" src="/images/thumb/0/0e/Dark.png/150px-Dark.png" width="150"/>
          </a>
          <a href="/wiki/Vampiric" title="Vampiric">
           <img alt="Vampiric" decoding="async" height="132" loading="lazy" src="/images/thumb/3/3a/Vampiric.png/150px-Vampiric.png" width="150"/>
          </a>
          <a href="/wiki/Fire" title="Fire">
           <img alt="Fire" decoding="async" height="132" loading="lazy" src="/images/thumb/3/30/Fire.png/150px-Fire.png" width="150"/>
          </a>
          <a href="/wiki/Ice" title="Ice">
           <img alt="Ice" decoding="async" height="132" loading="lazy" src="/images/thumb/7/77/Ice.png/150px-Ice.png" width="150"/>
          </a>
         </p>
         <h2>
          <span class="mw-headline" id="Rarities">
           <a href="/wiki/Rarity" title="Rarity">
            Rarities
           </a>
          </span>
         </h2>
         <p>
          <a href="/wiki/Common" title="Common">
           <img alt="Common" decoding="async" height="150" loading="lazy" src="/images/thumb/5/5f/Common.png/150px-Common.png" width="150"/>
          </a>
          <a href="/wiki/Rare" title="Rare">
           <img alt="Rare" decoding="async" height="150" loading="lazy" src="/images/thumb/9/9f/Rare.png/150px-Rare.png" width="150"/>
          </a>
          <a href="/wiki/Epic" title="Epic">
           <img alt="Epic" decoding="async" height="156" loading="lazy" src="/images/thumb/2/26/Epic.png/150px-Epic.png" width="150"/>
          </a>
          <a href="/wiki/Legendary" title="Legendary">
           <img alt="Legendary" decoding="async" height="146" loading="lazy" src="/images/thumb/e/e2/Legendary.png/150px-Legendary.png" width="150"/>
          </a>
          <a href="/wiki/Godly" title="Godly">
           <img alt="Godly" decoding="async" height="150" loading="lazy" src="/images/thumb/1/19/Godly.png/150px-Godly.png" width="150"/>
          </a>
          <a href="/wiki/Unique" title="Unique">
           <img alt="Unique" decoding="async" height="150" loading="lazy" src="/images/thumb/9/93/Unique.png/150px-Unique.png" width="150"/>
          </a>
         </p>
         <p>
          <br/>
         </p>
         <table class="navbox mw-collapsible" data-collapsetext="Hide" data-expandtext="Show">
          <tbody>
           <tr class="navbox-title">
            <th colspan="2" scope="col">
             <span class="navbox-vde">
              <a href="/wiki/Template:Items" title="Template:Items">
               <span title="View this navbox template">
                v
               </span>
              </a>
              ·
              <span class="plainlinks">
               <a class="external text" href="https://backpackbattles.wiki.gg/wiki/Template_talk:Items">
                <span title="Discuss this navbox template">
                 d
                </span>
               </a>
               ·
               <a class="external text" href="https://backpackbattles.wiki.gg/wiki/Template:Items?action=edit">
                <span title="Edit this navbox template">
                 e
                </span>
               </a>
              </span>
             </span>
             <a class="mw-selflink selflink">
              Items
             </a>
            </th>
           </tr>
           <tr class="navbox-gutter">
            <td colspan="2">
            </td>
           </tr>
           <tr class="navbox-row">
            <th class="navbox-group" scope="row">
             <a href="/wiki/Accessory" title="Accessory">
              Accessory
             </a>
            </th>
            <td class="navbox-list">
             <div class="hlist">
              <a href="/wiki/Acorn_Collar" title="Acorn Collar">
               Acorn Collar
              </a>
              •
              <a href="/wiki/Amulet_of_Alchemy" title="Amulet of Alchemy">
               Amulet of Alchemy
              </a>
              •
              <a href="/wiki/Amulet_of_Darkness" title="Amulet of Darkness">
               Amulet of Darkness
              </a>
              •
              <a href="/wiki/Amulet_of_Energy" title="Amulet of Energy">
               Amulet of Energy
              </a>
              •
              <a href="/wiki/Amulet_of_Feasting" title="Amulet of Feasting">
               Amulet of Feasting
              </a>
              •
              <a href="/wiki/Amulet_of_Life" title="Amulet of Life">
               Amulet of Life
              </a>
              •
              <a href="/wiki/Amulet_of_Steel" title="Amulet of Steel">
               Amulet of Steel
              </a>
              •
              <a href="/wiki/Amulet_of_the_Wild" title="Amulet of the Wild">
               Amulet of the Wild
              </a>
              •
              <a href="/wiki/Anvil" title="Anvil">
               Anvil
              </a>
              •
              <a href="/wiki/Bag_of_Stones" title="Bag of Stones">
               Bag of Stones
              </a>
              •
              <a href="/wiki/Blood_Amulet" title="Blood Amulet">
               Blood Amulet
              </a>
              •
              <a href="/wiki/Book_of_Ice" title="Book of Ice">
               Book of Ice
              </a>
              •
              <a href="/wiki/Box_of_Riches" title="Box of Riches">
               Box of Riches
              </a>
              •
              <a href="/wiki/Bunch_of_Coins" title="Bunch of Coins">
               Bunch of Coins
              </a>
              •
              <a href="/wiki/Burning_Banner" title="Burning Banner">
               Burning Banner
              </a>
              •
              <a href="/wiki/Cauldron" title="Cauldron">
               Cauldron
              </a>
              •
              <a href="/wiki/Customer_Card" title="Customer Card">
               Customer Card
              </a>
              •
              <a href="/wiki/Dark_Lantern" title="Dark Lantern">
               Dark Lantern
              </a>
              •
              <a href="/wiki/Deck_of_Cards" title="Deck of Cards">
               Deck of Cards
              </a>
              •
              <a href="/wiki/Deerwood_Guardian" title="Deerwood Guardian">
               Deerwood Guardian
              </a>
              •
              <a href="/wiki/Djinn_Lamp" title="Djinn Lamp">
               Djinn Lamp
              </a>
              •
              <a href="/wiki/Draconic_Orb" title="Draconic Orb">
               Draconic Orb
              </a>
              •
              <a href="/wiki/Dragon_Nest" title="Dragon Nest">
               Dragon Nest
              </a>
              •
              <a href="/wiki/Fanfare" title="Fanfare">
               Fanfare
              </a>
              •
              <a href="/wiki/Flame" title="Flame">
               Flame
              </a>
              •
              <a href="/wiki/Flame_Badge" title="Flame Badge">
               Flame Badge
              </a>
              •
              <a href="/wiki/Flute" title="Flute">
               Flute
              </a>
              •
              <a href="/wiki/Friendly_Fire" title="Friendly Fire">
               Friendly Fire
              </a>
              •
              <a href="/wiki/Frozen_Flame" title="Frozen Flame">
               Frozen Flame
              </a>
              •
              <a href="/wiki/Healing_Herbs" title="Healing Herbs">
               Healing Herbs
              </a>
              •
              <a href="/wiki/Heart_Container" title="Heart Container">
               Heart Container
              </a>
              •
              <a href="/wiki/Heart_of_Darkness" title="Heart of Darkness">
               Heart of Darkness
              </a>
              •
              <a href="/wiki/King_Crown" title="King Crown">
               King Crown
              </a>
              •
              <a href="/wiki/Leaf_Badge" title="Leaf Badge">
               Leaf Badge
              </a>
              •
              <a href="/wiki/Lucky_Clover" title="Lucky Clover">
               Lucky Clover
              </a>
              •
              <a href="/wiki/Lucky_Piggy" title="Lucky Piggy">
               Lucky Piggy
              </a>
              •
              <a href="/wiki/Mana_Orb" title="Mana Orb">
               Mana Orb
              </a>
              •
              <a href="/wiki/Maneki-neko" title="Maneki-neko">
               Maneki-neko
              </a>
              •
              <a href="/wiki/Mega_Clover" title="Mega Clover">
               Mega Clover
              </a>
              •
              <a href="/wiki/Miss_Fortune" title="Miss Fortune">
               Miss Fortune
              </a>
              •
              <a href="/wiki/Mr._Struggles" title="Mr. Struggles">
               Mr. Struggles
              </a>
              •
              <a href="/wiki/Mrs._Struggles" title="Mrs. Struggles">
               Mrs. Struggles
              </a>
              •
              <a href="/wiki/Nocturnal_Lock_Lifter" title="Nocturnal Lock Lifter">
               Nocturnal Lock Lifter
              </a>
              •
              <a href="/wiki/Oil_Lamp" title="Oil Lamp">
               Oil Lamp
              </a>
              •
              <a href="/wiki/Piercing_Arrow" title="Piercing Arrow">
               Piercing Arrow
              </a>
              •
              <a href="/wiki/Piggybank" title="Piggybank">
               Piggybank
              </a>
              •
              <a href="/wiki/Platinum_Customer_Card" title="Platinum Customer Card">
               Platinum Customer Card
              </a>
              •
              <a href="/wiki/Pocket_Sand" title="Pocket Sand">
               Pocket Sand
              </a>
              •
              <a href="/wiki/Poison_Ivy" title="Poison Ivy">
               Poison Ivy
              </a>
              •
              <a href="/wiki/Present" title="Present">
               Present
              </a>
              •
              <a href="/wiki/Prismatic_Orb" title="Prismatic Orb">
               Prismatic Orb
              </a>
              •
              <a href="/wiki/Rainbow_Badge" title="Rainbow Badge">
               Rainbow Badge
              </a>
              •
              <a href="/wiki/Shaman_Mask" title="Shaman Mask">
               Shaman Mask
              </a>
              •
              <a class="mw-redirect" href="/wiki/Shepherd%27s_Crook" title="Shepherd's Crook">
               Shepherd's Crook
              </a>
              •
              <a href="/wiki/Shiny_Shell" title="Shiny Shell">
               Shiny Shell
              </a>
              •
              <a href="/wiki/Skull_Badge" title="Skull Badge">
               Skull Badge
              </a>
              •
              <a href="/wiki/Snowball" title="Snowball">
               Snowball
              </a>
              •
              <a href="/wiki/Spiked_Collar" title="Spiked Collar">
               Spiked Collar
              </a>
              •
              <a href="/wiki/Stable_Recombobulator" title="Stable Recombobulator">
               Stable Recombobulator
              </a>
              •
              <a href="/wiki/Stone_Badge" title="Stone Badge">
               Stone Badge
              </a>
              •
              <a href="/wiki/Time_Dilator" title="Time Dilator">
               Time Dilator
              </a>
              •
              <a href="/wiki/Unidentified_Amulet" title="Unidentified Amulet">
               Unidentified Amulet
              </a>
              •
              <a href="/wiki/Unstable_Recombobulator" title="Unstable Recombobulator">
               Unstable Recombobulator
              </a>
              •
              <a href="/wiki/Walrus_Tusk" title="Walrus Tusk">
               Walrus Tusk
              </a>
              •
              <a href="/wiki/Whetstone" title="Whetstone">
               Whetstone
              </a>
              •
              <a href="/wiki/Wolf_Badge" title="Wolf Badge">
               Wolf Badge
              </a>
              •
              <a href="/wiki/Wolf_Emblem" title="Wolf Emblem">
               Wolf Emblem
              </a>
              •
              <a href="/wiki/Wonky_Snowman" title="Wonky Snowman">
               Wonky Snowman
              </a>
              •
              <a href="/wiki/Yggdrasil_Leaf" title="Yggdrasil Leaf">
               Yggdrasil Leaf
              </a>
             </div>
            </td>
           </tr>
           <tr class="navbox-gutter">
            <td colspan="2">
            </td>
           </tr>
           <tr class="navbox-row alt">
            <th class="navbox-group" scope="row">
             <a href="/wiki/Armor" title="Armor">
              Armor
             </a>
            </th>
            <td class="navbox-list">
             <div class="hlist">
              <a href="/wiki/Corrupted_Armor" title="Corrupted Armor">
               Corrupted Armor
              </a>
              •
              <a href="/wiki/Dragonscale_Armor" title="Dragonscale Armor">
               Dragonscale Armor
              </a>
              •
              <a href="/wiki/Holy_Armor" title="Holy Armor">
               Holy Armor
              </a>
              •
              <a href="/wiki/Ice_Armor" title="Ice Armor">
               Ice Armor
              </a>
              •
              <a href="/wiki/Leather_Armor" title="Leather Armor">
               Leather Armor
              </a>
              •
              <a href="/wiki/Moon_Armor" title="Moon Armor">
               Moon Armor
              </a>
              •
              <a href="/wiki/Stone_Armor" title="Stone Armor">
               Stone Armor
              </a>
              •
              <a href="/wiki/Sun_Armor" title="Sun Armor">
               Sun Armor
              </a>
              •
              <a href="/wiki/Vampiric_Armor" title="Vampiric Armor">
               Vampiric Armor
              </a>
             </div>
            </td>
           </tr>
           <tr class="navbox-gutter">
            <td colspan="2">
            </td>
           </tr>
           <tr class="navbox-row">
            <th class="navbox-group" scope="row">
             <a href="/wiki/Bag" title="Bag">
              Bag
             </a>
            </th>
            <td class="navbox-list">
             <div class="hlist">
              <a href="/wiki/Box_of_Prosperity" title="Box of Prosperity">
               Box of Prosperity
              </a>
              •
              <a href="/wiki/Duffle_Bag" title="Duffle Bag">
               Duffle Bag
              </a>
              •
              <a href="/wiki/Fanny_Pack" title="Fanny Pack">
               Fanny Pack
              </a>
              •
              <a href="/wiki/Fire_Pit" title="Fire Pit">
               Fire Pit
              </a>
              •
              <a href="/wiki/Leather_Bag" title="Leather Bag">
               Leather Bag
              </a>
              •
              <a href="/wiki/Offering_Bowl" title="Offering Bowl">
               Offering Bowl
              </a>
              •
              <a href="/wiki/Potion_Belt" title="Potion Belt">
               Potion Belt
              </a>
              •
              <a href="/wiki/Protective_Purse" title="Protective Purse">
               Protective Purse
              </a>
              •
              <a href="/wiki/Ranger_Bag" title="Ranger Bag">
               Ranger Bag
              </a>
              •
              <a href="/wiki/Relic_Case" title="Relic Case">
               Relic Case
              </a>
              •
              <a href="/wiki/Sack_of_Surprises" title="Sack of Surprises">
               Sack of Surprises
              </a>
              •
              <a href="/wiki/Stamina_Sack" title="Stamina Sack">
               Stamina Sack
              </a>
              •
              <a href="/wiki/Storage_Coffin" title="Storage Coffin">
               Storage Coffin
              </a>
              •
              <a href="/wiki/Utility_Pouch" title="Utility Pouch">
               Utility Pouch
              </a>
              •
              <a href="/wiki/Vineweave_Basket" title="Vineweave Basket">
               Vineweave Basket
              </a>
             </div>
            </td>
           </tr>
           <tr class="navbox-gutter">
            <td colspan="2">
            </td>
           </tr>
           <tr class="navbox-row alt">
            <th class="navbox-group" scope="row">
             <a href="/wiki/Food" title="Food">
              Food
             </a>
            </th>
            <td class="navbox-list">
             <div class="hlist">
              <a href="/wiki/Banana" title="Banana">
               Banana
              </a>
              •
              <a href="/wiki/Big_Bowl_of_Treats" title="Big Bowl of Treats">
               Big Bowl of Treats
              </a>
              •
              <a href="/wiki/Blueberries" title="Blueberries">
               Blueberries
              </a>
              •
              <a href="/wiki/Carrot" title="Carrot">
               Carrot
              </a>
              •
              <a href="/wiki/Cheese" title="Cheese">
               Cheese
              </a>
              •
              <a href="/wiki/Chili_Pepper" title="Chili Pepper">
               Chili Pepper
              </a>
              •
              <a href="/wiki/Doom_Cap" title="Doom Cap">
               Doom Cap
              </a>
              •
              <a href="/wiki/Fly_Agaric" title="Fly Agaric">
               Fly Agaric
              </a>
              •
              <a href="/wiki/Garlic" title="Garlic">
               Garlic
              </a>
              •
              <a href="/wiki/Gingerbread_Jerry" title="Gingerbread Jerry">
               Gingerbread Jerry
              </a>
              •
              <a href="/wiki/Pineapple" title="Pineapple">
               Pineapple
              </a>
              •
              <a href="/wiki/Pumpkin" title="Pumpkin">
               Pumpkin
              </a>
              •
              <a href="/wiki/Snowcake" title="Snowcake">
               Snowcake
              </a>
             </div>
            </td>
           </tr>
           <tr class="navbox-gutter">
            <td colspan="2">
            </td>
           </tr>
           <tr class="navbox-row">
            <th class="navbox-group" scope="row">
             <a href="/wiki/Gemstone" title="Gemstone">
              Gemstone
             </a>
            </th>
            <td class="navbox-list">
             <div class="hlist">
              <a href="/wiki/Amethyst" title="Amethyst">
               Amethyst
              </a>
              •
              <a href="/wiki/Badger_Rune" title="Badger Rune">
               Badger Rune
              </a>
              •
              <a href="/wiki/Burning_Coal" title="Burning Coal">
               Burning Coal
              </a>
              •
              <a href="/wiki/Corrupted_Crystal" title="Corrupted Crystal">
               Corrupted Crystal
              </a>
              •
              <a href="/wiki/Elephant_Rune" title="Elephant Rune">
               Elephant Rune
              </a>
              •
              <a href="/wiki/Emerald" title="Emerald">
               Emerald
              </a>
              •
              <a href="/wiki/Hawk_Rune" title="Hawk Rune">
               Hawk Rune
              </a>
              •
              <a href="/wiki/Lump_of_Coal" title="Lump of Coal">
               Lump of Coal
              </a>
              •
              <a href="/wiki/Ruby" title="Ruby">
               Ruby
              </a>
              •
              <a href="/wiki/Sapphire" title="Sapphire">
               Sapphire
              </a>
              •
              <a href="/wiki/Tim" title="Tim">
               Tim
              </a>
              •
              <a href="/wiki/Topaz" title="Topaz">
               Topaz
              </a>
             </div>
            </td>
           </tr>
           <tr class="navbox-gutter">
            <td colspan="2">
            </td>
           </tr>
           <tr class="navbox-row alt">
            <th class="navbox-group" scope="row">
             <a href="/wiki/Gloves" title="Gloves">
              Gloves
             </a>
            </th>
            <td class="navbox-list">
             <div class="hlist">
              <a href="/wiki/Dragon_Claws" title="Dragon Claws">
               Dragon Claws
              </a>
              •
              <a href="/wiki/Gloves_of_Haste" title="Gloves of Haste">
               Gloves of Haste
              </a>
              •
              <a href="/wiki/Gloves_of_Power" title="Gloves of Power">
               Gloves of Power
              </a>
              •
              <a href="/wiki/Vampiric_Gloves" title="Vampiric Gloves">
               Vampiric Gloves
              </a>
             </div>
            </td>
           </tr>
           <tr class="navbox-gutter">
            <td colspan="2">
            </td>
           </tr>
           <tr class="navbox-row">
            <th class="navbox-group" scope="row">
             <a href="/wiki/Helmet" title="Helmet">
              Helmet
             </a>
            </th>
            <td class="navbox-list">
             <div class="hlist">
              <a href="/wiki/Cap_of_Discomfort" title="Cap of Discomfort">
               Cap of Discomfort
              </a>
              •
              <a href="/wiki/Cap_of_Resilience" title="Cap of Resilience">
               Cap of Resilience
              </a>
              •
              <a href="/wiki/Glowing_Crown" title="Glowing Crown">
               Glowing Crown
              </a>
              •
              <a href="/wiki/Stone_Helm" title="Stone Helm">
               Stone Helm
              </a>
             </div>
            </td>
           </tr>
           <tr class="navbox-gutter">
            <td colspan="2">
            </td>
           </tr>
           <tr class="navbox-row alt">
            <th class="navbox-group" scope="row">
             <a href="/wiki/Pet" title="Pet">
              Pet
             </a>
            </th>
            <td class="navbox-list">
             <div class="hlist">
              <a href="/wiki/Amethyst_Egg" title="Amethyst Egg">
               Amethyst Egg
              </a>
              •
              <a href="/wiki/Armored_Courage_Puppy" title="Armored Courage Puppy">
               Armored Courage Puppy
              </a>
              •
              <a href="/wiki/Armored_Power_Puppy" title="Armored Power Puppy">
               Armored Power Puppy
              </a>
              •
              <a href="/wiki/Armored_Wisdom_Puppy" title="Armored Wisdom Puppy">
               Armored Wisdom Puppy
              </a>
              •
              <a href="/wiki/Blood_Goobert" title="Blood Goobert">
               Blood Goobert
              </a>
              •
              <a href="/wiki/Carrot_Goobert" title="Carrot Goobert">
               Carrot Goobert
              </a>
              •
              <a href="/wiki/Cheese_Goobert" title="Cheese Goobert">
               Cheese Goobert
              </a>
              •
              <a href="/wiki/Chili_Goobert" title="Chili Goobert">
               Chili Goobert
              </a>
              •
              <a href="/wiki/Chtulhu" title="Chtulhu">
               Chtulhu
              </a>
              •
              <a href="/wiki/Courage_Puppy" title="Courage Puppy">
               Courage Puppy
              </a>
              •
              <a href="/wiki/Cubert" title="Cubert">
               Cubert
              </a>
              •
              <a href="/wiki/Emerald_Egg" title="Emerald Egg">
               Emerald Egg
              </a>
              •
              <a href="/wiki/Emerald_Whelp" title="Emerald Whelp">
               Emerald Whelp
              </a>
              •
              <a href="/wiki/Goobert" title="Goobert">
               Goobert
              </a>
              •
              <a href="/wiki/Goobling" title="Goobling">
               Goobling
              </a>
              •
              <a href="/wiki/Hedgehog" title="Hedgehog">
               Hedgehog
              </a>
              •
              <a href="/wiki/Ice_Dragon" title="Ice Dragon">
               Ice Dragon
              </a>
              •
              <a href="/wiki/Jynx_torquilla" title="Jynx torquilla">
               Jynx torquilla
              </a>
              •
              <a href="/wiki/King_Goobert" title="King Goobert">
               King Goobert
              </a>
              •
              <a href="/wiki/Light_Goobert" title="Light Goobert">
               Light Goobert
              </a>
              •
              <a href="/wiki/Obsidian_Dragon" title="Obsidian Dragon">
               Obsidian Dragon
              </a>
              •
              <a href="/wiki/Phoenix" title="Phoenix">
               Phoenix
              </a>
              •
              <a href="/wiki/Poison_Goobert" title="Poison Goobert">
               Poison Goobert
              </a>
              •
              <a href="/wiki/Pop" title="Pop">
               Pop
              </a>
              •
              <a href="/wiki/Power_Puppy" title="Power Puppy">
               Power Puppy
              </a>
              •
              <a href="/wiki/Rainbow_Goobert_Deathslushy_Mansquisher" title="Rainbow Goobert Deathslushy Mansquisher">
               Rainbow Goobert Deathslushy Mansquisher
              </a>
              •
              <a href="/wiki/Rainbow_Goobert_Epicglob_Uberviscous" title="Rainbow Goobert Epicglob Uberviscous">
               Rainbow Goobert Epicglob Uberviscous
              </a>
              •
              <a href="/wiki/Rainbow_Goobert_Megasludge_Alphapuddle" title="Rainbow Goobert Megasludge Alphapuddle">
               Rainbow Goobert Megasludge Alphapuddle
              </a>
              •
              <a href="/wiki/Rainbow_Goobert_Omegaooze_Primeslime" title="Rainbow Goobert Omegaooze Primeslime">
               Rainbow Goobert Omegaooze Primeslime
              </a>
              •
              <a href="/wiki/Rat" title="Rat">
               Rat
              </a>
              •
              <a href="/wiki/Rat_Chef" title="Rat Chef">
               Rat Chef
              </a>
              •
              <a href="/wiki/Ruby_Chonk" title="Ruby Chonk">
               Ruby Chonk
              </a>
              •
              <a href="/wiki/Ruby_Egg" title="Ruby Egg">
               Ruby Egg
              </a>
              •
              <a href="/wiki/Ruby_Whelp" title="Ruby Whelp">
               Ruby Whelp
              </a>
              •
              <a href="/wiki/Sapphire_Egg" title="Sapphire Egg">
               Sapphire Egg
              </a>
              •
              <a href="/wiki/Sapphire_Whelp" title="Sapphire Whelp">
               Sapphire Whelp
              </a>
              •
              <a href="/wiki/Snake" title="Snake">
               Snake
              </a>
              •
              <a href="/wiki/Squirrel" title="Squirrel">
               Squirrel
              </a>
              •
              <a href="/wiki/Squirrel_Archer" title="Squirrel Archer">
               Squirrel Archer
              </a>
              •
              <a href="/wiki/Steel_Goobert" title="Steel Goobert">
               Steel Goobert
              </a>
              •
              <a href="/wiki/Stone_Golem" title="Stone Golem">
               Stone Golem
              </a>
              •
              <a href="/wiki/Toad" title="Toad">
               Toad
              </a>
              •
              <a href="/wiki/Unsettling_Presence" title="Unsettling Presence">
               Unsettling Presence
              </a>
              •
              <a href="/wiki/Wisdom_Puppy" title="Wisdom Puppy">
               Wisdom Puppy
              </a>
              •
              <a href="/wiki/Wolpertinger" title="Wolpertinger">
               Wolpertinger
              </a>
             </div>
            </td>
           </tr>
           <tr class="navbox-gutter">
            <td colspan="2">
            </td>
           </tr>
           <tr class="navbox-row">
            <th class="navbox-group" scope="row">
             <a href="/wiki/Playing_Card" title="Playing Card">
              Playing Card
             </a>
            </th>
            <td class="navbox-list">
             <div class="hlist">
              <a href="/wiki/Ace_of_Spades" title="Ace of Spades">
               Ace of Spades
              </a>
              •
              <a href="/wiki/Darkest_Lotus" title="Darkest Lotus">
               Darkest Lotus
              </a>
              •
              <a href="/wiki/Holo_Fire_Lizard" title="Holo Fire Lizard">
               Holo Fire Lizard
              </a>
              •
              <a href="/wiki/Jimbo" title="Jimbo">
               Jimbo
              </a>
              •
              <a href="/wiki/Reverse!" title="Reverse!">
               Reverse!
              </a>
              •
              <a href="/wiki/The_Fool" title="The Fool">
               The Fool
              </a>
              •
              <a href="/wiki/The_Lovers" title="The Lovers">
               The Lovers
              </a>
              •
              <a href="/wiki/White-Eyes_Blue_Dragon" title="White-Eyes Blue Dragon">
               White-Eyes Blue Dragon
              </a>
             </div>
            </td>
           </tr>
           <tr class="navbox-gutter">
            <td colspan="2">
            </td>
           </tr>
           <tr class="navbox-row alt">
            <th class="navbox-group" scope="row">
             <a href="/wiki/Potion" title="Potion">
              Potion
             </a>
            </th>
            <td class="navbox-list">
             <div class="hlist">
              <a href="/wiki/Demonic_Flask" title="Demonic Flask">
               Demonic Flask
              </a>
              •
              <a href="/wiki/Divine_Potion" title="Divine Potion">
               Divine Potion
              </a>
              •
              <a href="/wiki/Health_Potion" title="Health Potion">
               Health Potion
              </a>
              •
              <a href="/wiki/Heroic_Potion" title="Heroic Potion">
               Heroic Potion
              </a>
              •
              <a href="/wiki/Mana_Potion" title="Mana Potion">
               Mana Potion
              </a>
              •
              <a href="/wiki/Pestilence_Flask" title="Pestilence Flask">
               Pestilence Flask
              </a>
              •
              <a href="/wiki/Stone_Skin_Potion" title="Stone Skin Potion">
               Stone Skin Potion
              </a>
              •
              <a href="/wiki/Strong_Demonic_Flask" title="Strong Demonic Flask">
               Strong Demonic Flask
              </a>
              •
              <a href="/wiki/Strong_Divine_Potion" title="Strong Divine Potion">
               Strong Divine Potion
              </a>
              •
              <a href="/wiki/Strong_Health_Potion" title="Strong Health Potion">
               Strong Health Potion
              </a>
              •
              <a href="/wiki/Strong_Heroic_Potion" title="Strong Heroic Potion">
               Strong Heroic Potion
              </a>
              •
              <a href="/wiki/Strong_Mana_Potion" title="Strong Mana Potion">
               Strong Mana Potion
              </a>
              •
              <a href="/wiki/Strong_Pestilence_Flask" title="Strong Pestilence Flask">
               Strong Pestilence Flask
              </a>
              •
              <a href="/wiki/Strong_Stone_Skin_Potion" title="Strong Stone Skin Potion">
               Strong Stone Skin Potion
              </a>
              •
              <a href="/wiki/Strong_Vampiric_Potion" title="Strong Vampiric Potion">
               Strong Vampiric Potion
              </a>
              •
              <a href="/wiki/Vampiric_Potion" title="Vampiric Potion">
               Vampiric Potion
              </a>
             </div>
            </td>
           </tr>
           <tr class="navbox-gutter">
            <td colspan="2">
            </td>
           </tr>
           <tr class="navbox-row">
            <th class="navbox-group" scope="row">
             <a href="/wiki/Shield" title="Shield">
              Shield
             </a>
            </th>
            <td class="navbox-list">
             <div class="hlist">
              <a href="/wiki/Frozen_Buckler" title="Frozen Buckler">
               Frozen Buckler
              </a>
              •
              <a href="/wiki/Moon_Shield" title="Moon Shield">
               Moon Shield
              </a>
              •
              <a href="/wiki/Shield_of_Valor" title="Shield of Valor">
               Shield of Valor
              </a>
              •
              <a href="/wiki/Spiked_Shield" title="Spiked Shield">
               Spiked Shield
              </a>
              •
              <a href="/wiki/Sun_Shield" title="Sun Shield">
               Sun Shield
              </a>
              •
              <a href="/wiki/Wooden_Buckler" title="Wooden Buckler">
               Wooden Buckler
              </a>
             </div>
            </td>
           </tr>
           <tr class="navbox-gutter">
            <td colspan="2">
            </td>
           </tr>
           <tr class="navbox-row alt">
            <th class="navbox-group" scope="row">
             <a href="/wiki/Shoes" title="Shoes">
              Shoes
             </a>
            </th>
            <td class="navbox-list">
             <div class="hlist">
              <a href="/wiki/Dragonskin_Boots" title="Dragonskin Boots">
               Dragonskin Boots
              </a>
              •
              <a href="/wiki/Leather_Boots" title="Leather Boots">
               Leather Boots
              </a>
              •
              <a href="/wiki/Stone_Shoes" title="Stone Shoes">
               Stone Shoes
              </a>
             </div>
            </td>
           </tr>
           <tr class="navbox-gutter">
            <td colspan="2">
            </td>
           </tr>
           <tr class="navbox-row">
            <th class="navbox-group" scope="row">
             <a href="/wiki/Weapon" title="Weapon">
              Weapon
             </a>
            </th>
            <td class="navbox-list">
             <div class="hlist">
              <a href="/wiki/Amethyst_Whelp" title="Amethyst Whelp">
               Amethyst Whelp
              </a>
              •
              <a href="/wiki/Armored_Courage_Puppy" title="Armored Courage Puppy">
               Armored Courage Puppy
              </a>
              •
              <a href="/wiki/Artifact_Stone:_Cold" title="Artifact Stone: Cold">
               Artifact Stone: Cold
              </a>
              •
              <a href="/wiki/Artifact_Stone:_Death" title="Artifact Stone: Death">
               Artifact Stone: Death
              </a>
              •
              <a href="/wiki/Artifact_Stone:_Heat" title="Artifact Stone: Heat">
               Artifact Stone: Heat
              </a>
              •
              <a href="/wiki/Axe" title="Axe">
               Axe
              </a>
              •
              <a href="/wiki/Belladonna%27s_Shade" title="Belladonna's Shade">
               Belladonna's Shade
              </a>
              •
              <a href="/wiki/Belladonna%27s_Whisper" title="Belladonna's Whisper">
               Belladonna's Whisper
              </a>
              •
              <a href="/wiki/Blood_Harvester" title="Blood Harvester">
               Blood Harvester
              </a>
              •
              <a href="/wiki/Bloodthorne" title="Bloodthorne">
               Bloodthorne
              </a>
              •
              <a href="/wiki/Bloody_Dagger" title="Bloody Dagger">
               Bloody Dagger
              </a>
              •
              <a href="/wiki/Bow_and_Arrow" title="Bow and Arrow">
               Bow and Arrow
              </a>
              •
              <a href="/wiki/Brass_Knuckles" title="Brass Knuckles">
               Brass Knuckles
              </a>
              •
              <a href="/wiki/Broom" title="Broom">
               Broom
              </a>
              •
              <a href="/wiki/Burning_Blade" title="Burning Blade">
               Burning Blade
              </a>
              •
              <a href="/wiki/Burning_Sword" title="Burning Sword">
               Burning Sword
              </a>
              •
              <a href="/wiki/Burning_Torch" title="Burning Torch">
               Burning Torch
              </a>
              •
              <a href="/wiki/Busted_Blade" title="Busted Blade">
               Busted Blade
              </a>
              •
              <a href="/wiki/Chain_Whip" title="Chain Whip">
               Chain Whip
              </a>
              •
              <a href="/wiki/Claws_of_Attack" title="Claws of Attack">
               Claws of Attack
              </a>
              •
              <a href="/wiki/Courage_Puppy" title="Courage Puppy">
               Courage Puppy
              </a>
              •
              <a href="/wiki/Critwood_Staff" title="Critwood Staff">
               Critwood Staff
              </a>
              •
              <a href="/wiki/Crossblades" title="Crossblades">
               Crossblades
              </a>
              •
              <a href="/wiki/Cursed_Dagger" title="Cursed Dagger">
               Cursed Dagger
              </a>
              •
              <a href="/wiki/Dagger" title="Dagger">
               Dagger
              </a>
              •
              <a href="/wiki/Dancing_Dragon" title="Dancing Dragon">
               Dancing Dragon
              </a>
              •
              <a href="/wiki/Darksaber" title="Darksaber">
               Darksaber
              </a>
              •
              <a href="/wiki/Death_Scythe" title="Death Scythe">
               Death Scythe
              </a>
              •
              <a href="/wiki/Double_Axe" title="Double Axe">
               Double Axe
              </a>
              •
              <a href="/wiki/Eggscalibur" title="Eggscalibur">
               Eggscalibur
              </a>
              •
              <a href="/wiki/Emerald_Whelp" title="Emerald Whelp">
               Emerald Whelp
              </a>
              •
              <a href="/wiki/Falcon_Blade" title="Falcon Blade">
               Falcon Blade
              </a>
              •
              <a href="/wiki/Fancy_Fencing_Rapier" title="Fancy Fencing Rapier">
               Fancy Fencing Rapier
              </a>
              •
              <a href="/wiki/Flame_Whip" title="Flame Whip">
               Flame Whip
              </a>
              •
              <a href="/wiki/Forging_Hammer" title="Forging Hammer">
               Forging Hammer
              </a>
              •
              <a href="/wiki/Fortuna%27s_Grace" title="Fortuna's Grace">
               Fortuna's Grace
              </a>
              •
              <a href="/wiki/Fortuna%27s_Hope" title="Fortuna's Hope">
               Fortuna's Hope
              </a>
              •
              <a href="/wiki/Frostbite" title="Frostbite">
               Frostbite
              </a>
              •
              <a href="/wiki/Hammer" title="Hammer">
               Hammer
              </a>
              •
              <a href="/wiki/Hero_Longsword" title="Hero Longsword">
               Hero Longsword
              </a>
              •
              <a href="/wiki/Hero_Sword" title="Hero Sword">
               Hero Sword
              </a>
              •
              <a href="/wiki/Holy_Spear" title="Holy Spear">
               Holy Spear
              </a>
              •
              <a href="/wiki/Hungry_Blade" title="Hungry Blade">
               Hungry Blade
              </a>
              •
              <a href="/wiki/Ice_Dragon" title="Ice Dragon">
               Ice Dragon
              </a>
              •
              <a href="/wiki/Impractically_Large_Greatsword" title="Impractically Large Greatsword">
               Impractically Large Greatsword
              </a>
              •
              <a href="/wiki/Katana" title="Katana">
               Katana
              </a>
              •
              <a href="/wiki/Lightsaber" title="Lightsaber">
               Lightsaber
              </a>
              •
              <a href="/wiki/Magic_Staff" title="Magic Staff">
               Magic Staff
              </a>
              •
              <a href="/wiki/Magic_Torch" title="Magic Torch">
               Magic Torch
              </a>
              •
              <a href="/wiki/Manathirst" title="Manathirst">
               Manathirst
              </a>
              •
              <a href="/wiki/Molten_Dagger" title="Molten Dagger">
               Molten Dagger
              </a>
              •
              <a href="/wiki/Molten_Spear" title="Molten Spear">
               Molten Spear
              </a>
              •
              <a href="/wiki/Obsidian_Dragon" title="Obsidian Dragon">
               Obsidian Dragon
              </a>
              •
              <a href="/wiki/Pan" title="Pan">
               Pan
              </a>
              •
              <a href="/wiki/Pandamonium" title="Pandamonium">
               Pandamonium
              </a>
              •
              <a href="/wiki/Phoenix" title="Phoenix">
               Phoenix
              </a>
              •
              <a href="/wiki/Poison_Dagger" title="Poison Dagger">
               Poison Dagger
              </a>
              •
              <a href="/wiki/Pop" title="Pop">
               Pop
              </a>
              •
              <a href="/wiki/Prismatic_Sword" title="Prismatic Sword">
               Prismatic Sword
              </a>
              •
              <a href="/wiki/Pumpkin" title="Pumpkin">
               Pumpkin
              </a>
              •
              <a href="/wiki/Ripsaw_Blade" title="Ripsaw Blade">
               Ripsaw Blade
              </a>
              •
              <a href="/wiki/Ruby_Chonk" title="Ruby Chonk">
               Ruby Chonk
              </a>
              •
              <a href="/wiki/Ruby_Whelp" title="Ruby Whelp">
               Ruby Whelp
              </a>
              •
              <a href="/wiki/Sapphire_Whelp" title="Sapphire Whelp">
               Sapphire Whelp
              </a>
              •
              <a href="/wiki/Serpent_Staff" title="Serpent Staff">
               Serpent Staff
              </a>
              •
              <a href="/wiki/Shell_Totem" title="Shell Totem">
               Shell Totem
              </a>
              •
              <a href="/wiki/Shortbow" title="Shortbow">
               Shortbow
              </a>
              •
              <a href="/wiki/Shovel" title="Shovel">
               Shovel
              </a>
              •
              <a href="/wiki/Snow_Stick" title="Snow Stick">
               Snow Stick
              </a>
              •
              <a href="/wiki/Spear" title="Spear">
               Spear
              </a>
              •
              <a href="/wiki/Spectral_Dagger" title="Spectral Dagger">
               Spectral Dagger
              </a>
              •
              <a href="/wiki/Spiked_Staff" title="Spiked Staff">
               Spiked Staff
              </a>
              •
              <a href="/wiki/Squirrel_Archer" title="Squirrel Archer">
               Squirrel Archer
              </a>
              •
              <a href="/wiki/Staff_of_Fire" title="Staff of Fire">
               Staff of Fire
              </a>
              •
              <a href="/wiki/Staff_of_Unhealing" title="Staff of Unhealing">
               Staff of Unhealing
              </a>
              •
              <a href="/wiki/Stone" title="Stone">
               Stone
              </a>
              •
              <a href="/wiki/Stone_Golem" title="Stone Golem">
               Stone Golem
              </a>
              •
              <a href="/wiki/Thorn_Whip" title="Thorn Whip">
               Thorn Whip
              </a>
              •
              <a href="/wiki/Thornbloom" title="Thornbloom">
               Thornbloom
              </a>
              •
              <a href="/wiki/Torch" title="Torch">
               Torch
              </a>
              •
              <a href="/wiki/Tusk_Piercer" title="Tusk Piercer">
               Tusk Piercer
              </a>
              •
              <a href="/wiki/Tusk_Poker" title="Tusk Poker">
               Tusk Poker
              </a>
              •
              <a href="/wiki/Villain_Sword" title="Villain Sword">
               Villain Sword
              </a>
              •
              <a href="/wiki/Wooden_Sword" title="Wooden Sword">
               Wooden Sword
              </a>
             </div>
            </td>
           </tr>
          </tbody>
         </table>
         <!-- 
NewPP limit report
Cached time: 20250129100935
Cache expiry: 2592000
Reduced expiry: false
Complications: [show‐toc]
CPU time usage: 0.207 seconds
Real time usage: 0.682 seconds
Preprocessor visited node count: 558/1000000
Post‐expand include size: 10397/4194304 bytes
Template argument size: 33/4194304 bytes
Highest expansion depth: 6/100
Expensive parser function count: 0/500
Unstrip recursion depth: 0/20
Unstrip post‐expand size: 8112/10000000 bytes
Lua time usage: 0.008/15.000 seconds
Lua memory usage: 640278/52428800 bytes
-->
         <!--
Transclusion expansion time report (%,ms,calls,template)
100.00%  131.328      1 Template:Items
100.00%  131.328      1 -total
 97.80%  128.439      1 Template:Navbox
-->
         <!-- Saved in parser cache with key backpackbattles_en:pcache:idhash:315-0!canonical and timestamp 20250129100934 and revision id 10081. Rendering was triggered because: page-view
 -->
        </div>
        <div class="printfooter" data-nosnippet="">
         Retrieved from "
         <a dir="ltr" href="https://backpackbattles.wiki.gg/wiki/Items?oldid=10081">
          https://backpackbattles.wiki.gg/wiki/Items?oldid=10081
         </a>
         "
        </div>
       </div>
       <div class="catlinks" data-mw="interface" id="catlinks">
        <div class="mw-normal-catlinks" id="mw-normal-catlinks">
         <a href="/wiki/Special:Categories" title="Special:Categories">
          Category
         </a>
         :
         <ul>
          <li>
           <a href="/wiki/Category:Items" title="Category:Items">
            Items
           </a>
          </li>
         </ul>
        </div>
       </div>
      </div>
     </main>
     <aside data-mw="WggShowcaseLayout" id="wikigg-sl-rail">
      <div class="wikigg-showcase__unit wikigg-showcase__reserved--mrec" data-wgg-unit-type="pubco">
       <div id="nn_mpu_test">
       </div>
       <div id="nn_mpu1">
       </div>
      </div>
      <div class="wikigg-showcase__unit" data-wgg-unit-type="internal">
      </div>
      <div class="wikigg-showcase__unit" data-wgg-unit-type="pubco">
       <div id="nn_mpu2">
       </div>
      </div>
     </aside>
    </div>
    <aside data-mw="WggShowcaseLayout" id="wikigg-sl-footer">
     <div class="wikigg-showcase__unit wikigg-showcase__reserved--lb" data-wgg-unit-type="internal">
     </div>
    </aside>
   </div>
  </div>
  <div id="mw-data-after-content">
   <div class="mw-cookiewarning-container">
    <div class="mw-cookiewarning-text">
     <span>
      Cookies help us deliver our services. By using our services, you agree to our use of cookies.
     </span>
    </div>
    <form method="POST">
     <div class="oo-ui-layout oo-ui-horizontalLayout">
      <span class="oo-ui-widget oo-ui-widget-enabled oo-ui-buttonElement oo-ui-buttonElement-framed oo-ui-labelElement oo-ui-flaggedElement-progressive oo-ui-buttonWidget">
       <a class="oo-ui-buttonElement-button" href="https://www.indie.io/privacy-policy" rel="nofollow" role="button" tabindex="0">
        <span class="oo-ui-iconElement-icon oo-ui-iconElement-noIcon oo-ui-image-progressive">
        </span>
        <span class="oo-ui-labelElement-label">
         More information
        </span>
        <span class="oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator oo-ui-image-progressive">
        </span>
       </a>
      </span>
      <span class="oo-ui-widget oo-ui-widget-enabled oo-ui-inputWidget oo-ui-buttonElement oo-ui-buttonElement-framed oo-ui-labelElement oo-ui-flaggedElement-primary oo-ui-flaggedElement-progressive oo-ui-buttonInputWidget">
       <button class="oo-ui-inputWidget-input oo-ui-buttonElement-button" name="disablecookiewarning" tabindex="0" type="submit" value="OK">
        <span class="oo-ui-iconElement-icon oo-ui-iconElement-noIcon oo-ui-image-invert">
        </span>
        <span class="oo-ui-labelElement-label">
         OK
        </span>
        <span class="oo-ui-indicatorElement-indicator oo-ui-indicatorElement-noIndicator oo-ui-image-invert">
        </span>
       </button>
      </span>
     </div>
    </form>
   </div>
  </div>
  <footer class="mw-footer" id="footer">
   <ul id="footer-info">
    <li id="footer-info-lastmod">
     This page was last edited on 8 November 2024, at 05:27.
    </li>
    <li id="footer-info-copyright">
     <div class="wikigg-copyright-box">
      Pages that were created prior to November 2023 are adapted from the Backpack Battles Fandom wiki.
      <br/>
      Page content is under
      <a class="external" href="https://creativecommons.org/licenses/by-sa/4.0" rel="nofollow">
       Creative Commons Attribution-ShareAlike 4.0 License
      </a>
      unless otherwise noted.
     </div>
    </li>
   </ul>
   <ul id="footer-places">
    <li id="footer-places-wikigg-tos">
     <a href="https://www.indie.io/terms-of-service">
      Terms of Service
     </a>
    </li>
    <li id="footer-places-wikigg-privacy">
     <a href="https://www.indie.io/privacy-policy">
      Privacy policy
     </a>
    </li>
    <li id="footer-places-wikigg-support">
     <a href="https://support.wiki.gg">
      Support Wiki
     </a>
    </li>
    <li id="footer-places-wikigg-servicedesk">
     <a href="https://wiki.gg/go/servicedesk">
      Send a ticket to wiki.gg
     </a>
    </li>
    <li id="footer-places-wikigg-statuspage">
     <a href="https://wikiggstatus.com">
      Status page
     </a>
    </li>
    <li id="footer-places-wikigg-pcmp">
     <a class="nn-cmp-show" href="#" style="display: none">
      Manage cookie settings
     </a>
    </li>
   </ul>
   <ul class="noprint" id="footer-icons">
    <li id="footer-copyrightico">
     <a class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled" href="https://creativecommons.org/licenses/by-sa/4.0">
      <img alt="Creative Commons Attribution-ShareAlike 4.0 License" height="31" loading="lazy" src="https://commons.wiki.gg/images/1/14/CC-BY-SA_footer_badge.svg?2" width="88"/>
     </a>
    </li>
    <li id="footer-poweredbyico">
     <a class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled" href="https://www.mediawiki.org/">
      <img alt="Powered by MediaWiki" height="31" loading="lazy" src="/mw-1.43/resources/assets/poweredby_mediawiki.svg" width="88"/>
     </a>
    </li>
    <li id="footer-partofico">
     <a class="cdx-button cdx-button--fake-button cdx-button--size-large cdx-button--fake-button--enabled" href="https://wiki.gg">
      <img alt="Part of wiki.gg" height="31" loading="lazy" src="https://commons.wiki.gg/images/d/d1/Network_footer_badge.svg?2" width="88"/>
     </a>
    </li>
   </ul>
  </footer>
  <script>
   (RLQ=window.RLQ||[]).push(function(){mw.config.set({"wgBackendResponseTime":194,"wgPageParseReport":{"limitreport":{"cputime":"0.207","walltime":"0.682","ppvisitednodes":{"value":558,"limit":1000000},"postexpandincludesize":{"value":10397,"limit":4194304},"templateargumentsize":{"value":33,"limit":4194304},"expansiondepth":{"value":6,"limit":100},"expensivefunctioncount":{"value":0,"limit":500},"unstrip-depth":{"value":0,"limit":20},"unstrip-size":{"value":8112,"limit":10000000},"timingprofile":["100.00%  131.328      1 Template:Items","100.00%  131.328      1 -total"," 97.80%  128.439      1 Template:Navbox"]},"scribunto":{"limitreport-timeusage":{"value":"0.008","limit":"15.000"},"limitreport-memusage":{"value":640278,"limit":52428800}},"cachereport":{"timestamp":"20250129100935","ttl":2592000,"transientcontent":false}}});});
  </script>
  <script>
   (function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'9520b5892cc16194',t:'MTc1MDMxMTgwOS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();
  </script>
 </body>
</html>
